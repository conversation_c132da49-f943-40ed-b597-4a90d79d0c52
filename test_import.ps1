# PowerShell脚本测试文件导入接口

# 测试CSV文件导入
Write-Host "测试CSV文件导入..."
$csvForm = @{
    file = Get-Item "test_authorities.csv"
    operator = "test_user"
}

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/authorities/import" -Method POST -Form $csvForm
    Write-Host "CSV导入响应:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "CSV导入失败:" -ForegroundColor Red
    Write-Host $_.Exception.Message
}

Write-Host "`n" + "="*50 + "`n"

# 测试JSON文件导入
Write-Host "测试JSON文件导入..."
$jsonForm = @{
    file = Get-Item "test_authorities.json"
    operator = "test_user"
    overrideExisting = "true"
}

try {
    $response = Invoke-RestMethod -Uri "http://localhost:8080/api/authorities/import" -Method POST -Form $jsonForm
    Write-Host "JSON导入响应:" -ForegroundColor Green
    $response | ConvertTo-Json -Depth 10
} catch {
    Write-Host "JSON导入失败:" -ForegroundColor Red
    Write-Host $_.Exception.Message
}
