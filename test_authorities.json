[{"权限名称": "JSON用户管理", "权限编码": "JSON_USER_MANAGE", "权限说明": "JSON导入的用户管理权限", "操作列表": "create,read,update,delete", "资源类型": "menu", "资源路径": "/json/user/list,/json/user/add", "父权限ID": "", "权限层级": "1", "排序号": "10", "应用ID": "1", "模块ID": "1", "同步来源": "JSON_IMPORT"}, {"权限名称": "JSON角色管理", "权限编码": "JSON_ROLE_MANAGE", "权限说明": "JSON导入的角色管理权限", "操作列表": "create,read,update,delete", "资源类型": "menu", "资源路径": "/json/role/list,/json/role/add", "父权限ID": "", "权限层级": "1", "排序号": "20", "应用ID": "1", "模块ID": "1", "同步来源": "JSON_IMPORT"}, {"权限名称": "JSON权限管理", "权限编码": "JSON_PERMISSION_MANAGE", "权限说明": "JSON导入的权限管理权限", "操作列表": "read,update", "资源类型": "menu", "资源路径": "/json/permission/list", "父权限ID": "", "权限层级": "1", "排序号": "30", "应用ID": "1", "模块ID": "1", "同步来源": "JSON_IMPORT"}, {"权限名称": "JSON系统设置", "权限编码": "JSON_SYSTEM_SETTING", "权限说明": "JSON导入的系统设置权限", "操作列表": "read,update", "资源类型": "menu", "资源路径": "/json/system/config", "父权限ID": "", "权限层级": "1", "排序号": "40", "应用ID": "1", "模块ID": "1", "同步来源": "JSON_IMPORT"}]