import pandas as pd

# 创建Excel测试数据
data = {
    '权限名称': ['Excel用户管理', 'Excel角色管理', 'Excel权限管理', 'Excel系统设置'],
    '权限编码': ['EXCEL_USER_MANAGE', 'EXCEL_ROLE_MANAGE', 'EXCEL_PERMISSION_MANAGE', 'EXCEL_SYSTEM_SETTING'],
    '权限说明': ['Excel导入的用户管理权限', 'Excel导入的角色管理权限', 'Excel导入的权限管理权限', 'Excel导入的系统设置权限'],
    '操作列表': ['create,read,update,delete', 'create,read,update,delete', 'read,update', 'read,update'],
    '资源类型': ['menu', 'menu', 'menu', 'menu'],
    '资源路径': ['/excel/user/list,/excel/user/add', '/excel/role/list,/excel/role/add', '/excel/permission/list', '/excel/system/config'],
    '父权限ID': ['', '', '', ''],
    '权限层级': [1, 1, 1, 1],
    '排序号': [10, 20, 30, 40],
    '应用ID': [1, 1, 1, 1],
    '模块ID': [1, 1, 1, 1],
    '同步来源': ['EXCEL_IMPORT', 'EXCEL_IMPORT', 'EXCEL_IMPORT', 'EXCEL_IMPORT']
}

# 创建DataFrame并保存为Excel
df = pd.DataFrame(data)
df.to_excel('test_authorities.xlsx', index=False, engine='openpyxl')
print("Excel测试文件创建成功: test_authorities.xlsx")
