plugins {
	java
	id("org.springframework.boot") version "3.2.0" // 改用稳定版 3.2.0
	id("io.spring.dependency-management") version "1.1.7"
}

// 其他配置保持不变...
group = "cn.ac.iie"
version = "0.0.1-SNAPSHOT"

java {
	toolchain {
		languageVersion = JavaLanguageVersion.of(21)
	}
}

configurations {
	compileOnly {
		extendsFrom(configurations.annotationProcessor.get())
	}
}

repositories {
	mavenCentral()
}

val testAgent: Configuration by configurations.creating

dependencies {
	// ===== 主要依赖 =====
	implementation("org.springframework.boot:spring-boot-starter-data-jpa")
	implementation("org.springframework.boot:spring-boot-starter-web")
	implementation("org.springframework.boot:spring-boot-starter-actuator")

	// Spring Security
	implementation("org.springframework.boot:spring-boot-starter-security")

	// OpenAPI
	implementation("org.springdoc:springdoc-openapi-starter-webmvc-ui:2.2.0")
	implementation("org.springdoc:springdoc-openapi-starter-webmvc-api:2.2.0")

	// ===== 开发辅助 =====
	compileOnly("org.projectlombok:lombok")
	developmentOnly("org.springframework.boot:spring-boot-devtools")
	runtimeOnly("com.mysql:mysql-connector-j")
	annotationProcessor("org.projectlombok:lombok")
	implementation("org.apache.poi:poi:5.2.3") // 基础功能
	implementation("org.apache.poi:poi-ooxml:5.2.3") // 支持.xlsx格式

	// ===== 参数校验 =====
	implementation("org.springframework.boot:spring-boot-starter-validation")

	// ===== 测试依赖 =====
	testImplementation("org.springframework.boot:spring-boot-starter-test") {
		exclude(group = "org.junit.vintage", module = "junit-vintage-engine")
	}
	testImplementation("org.mockito:mockito-core:5.2.0")
	testImplementation("org.mockito:mockito-inline:5.2.0")
	testRuntimeOnly("org.junit.platform:junit-platform-launcher")


	// ===== 测试代理 =====
	testAgent("net.bytebuddy:byte-buddy-agent:1.14.6")
}

tasks.withType<Test> {
	useJUnitPlatform()
	val testAgentFiles = testAgent.incoming.files
	jvmArgumentProviders.add {
		testAgentFiles.map { "-javaagent:${it.absolutePath}" }
	}
}