INSERT INTO qxdb.users (id, key_id, name, password, email, phone) VALUES (1,'1111', '张三',  'xxxx','<PERSON><PERSON><PERSON>@example.com', '13800001111');
INSERT INTO qxdb.users (id, key_id, name, password, email, phone) VALUES (2, '1112', '李四', 'xxxx', '<EMAIL>', '18811112222');
insert into qxdb.apps(app_id,app_name,app_url,app_description,app_callback_url,app_logo,del_flag,creat_user,creat_time,update_user,update_time,point_user_phone,alive_flag) values (01,'支付宝','http://www.baidu.com','app1','http://www.baidu.com','http://www.baidu.com/1246w566',0,'李四',now(),'张三',now(),'18811112222',0);
insert into qxdb.apps(app_id,app_name,app_url,app_description,app_callback_url,app_logo,del_flag,creat_user,creat_time,update_user,update_time,point_user_phone,alive_flag) values (02,'密保卫士','http://www.mibao.com','app2','http://www.bmibao.com/calback','http://www.baidu.com/1246w5612343456',0,'王五',now(),'张三',now(),'18811112222',0);
insert into qxdb.apps(app_id,app_name,app_url,app_description,app_callback_url,app_logo,del_flag,creat_user,creat_time,update_user,update_time,point_user_phone,alive_flag) values (03,'支付宝','http://www.baidu.com','app1','http://www.baidu.com','http://www.baidu.com/1246w566',0,'李四',now(),'张三',now(),'18811112222',0);
insert into qxdb.apps(app_id,app_name,app_url,app_description,app_callback_url,app_logo,del_flag,creat_user,creat_time,update_user,update_time,point_user_phone,alive_flag) values (04,'密保卫士','http://www.mibao.com','app2','http://www.bmibao.com/calback','http://www.baidu.com/1246w5612343456',0,'王五',now(),'张三',now(),'18811112222',0);
insert into qxdb.apps(app_id,app_name,app_url,app_description,app_callback_url,app_logo,del_flag,creat_user,creat_time,update_user,update_time,point_user_phone,alive_flag) values (05,'支付宝','http://www.baidu.com','app1','http://www.baidu.com','http://www.baidu.com/1246w566',0,'李四',now(),'张三',now(),'18811112222',0);
insert into qxdb.apps(app_id,app_name,app_url,app_description,app_callback_url,app_logo,del_flag,creat_user,creat_time,update_user,update_time,point_user_phone,alive_flag) values (06,'密保卫士','http://www.mibao.com','app2','http://www.bmibao.com/calback','http://www.baidu.com/1246w5612343456',0,'王五',now(),'张三',now(),'18811112222',0);
insert into qxdb.apps(app_id,app_name,app_url,app_description,app_callback_url,app_logo,del_flag,creat_user,creat_time,update_user,update_time,point_user_phone,alive_flag) values (07,'支付宝','http://www.baidu.com','app1','http://www.baidu.com','http://www.baidu.com/1246w566',0,'李四',now(),'张三',now(),'18811112222',0);
insert into qxdb.apps(app_id,app_name,app_url,app_description,app_callback_url,app_logo,del_flag,creat_user,creat_time,update_user,update_time,point_user_phone,alive_flag) values (08,'密保卫士','http://www.mibao.com','app2','http://www.bmibao.com/calback','http://www.baidu.com/1246w5612343456',0,'王五',now(),'张三',now(),'18811112222',0);
insert into qxdb.modules(module_id,module_name,parent_id,module_description,app_id,module_url,del_flag,creat_user,creat_time,update_user,update_time,point_user_phone)values (0101,'用户模块',0101,'用于用户的管理',01,'http://www.baidu.com/user',0,'李四',now(),'张三',now(),'18811112222');
insert into qxdb.modules(module_id,module_name,parent_id,module_description,app_id,module_url,del_flag,creat_user,creat_time,update_user,update_time,point_user_phone)values (0102,'支付模块',0101,'payModule',01,'http://www.baidu.com/pay',0,'李四',now(),'张三',now(),'18811112222');