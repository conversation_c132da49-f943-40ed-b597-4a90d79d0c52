/*
 Navicat Premium Dump SQL

 Source Server         : **********
 Source Server Type    : MySQL
 Source Server Version : 80404 (8.4.4)
 Source Host           : **********:23306
 Source Schema         : cyh_testdb

 Target Server Type    : MySQL
 Target Server Version : 80404 (8.4.4)
 File Encoding         : 65001

 Date: 18/07/2025 18:04:57
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for apps
-- ----------------------------
DROP TABLE IF EXISTS `apps`;
CREATE TABLE `apps`  (
  `app_id` bigint NOT NULL AUTO_INCREMENT,
  `app_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `app_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `app_description` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `app_callback_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `app_logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `del_flag` int NOT NULL DEFAULT 0,
  `alive_flag` int NOT NULL DEFAULT 0,
  `point_user_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `creat_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creat_time` datetime NOT NULL,
  `update_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`app_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of apps
-- ----------------------------
INSERT INTO `apps` VALUES (1, 'OA系统', 'https://www.baidu.com', 'OA 系统是整合流程审批、文档管理与协同办公的数字化平台，提升企业运营效率', 'https://oa.example.com/callback', 'https://tse4-mm.cn.bing.net/th/id/OIP-C.OZEWv-DlclJI9AFeFEDDjgHaE8?rs=1&pid=ImgDetMain', 0, 0, '13800138005', 'admin', '2025-05-10 10:00:00', '', '2025-06-27 21:29:28');
INSERT INTO `apps` VALUES (2, 'CRM系统', 'https://crm.example.com', '用于管理企业与客户交互的数字化平台，集成客户数据管理、销售流程跟踪、营销自动化、客户服务支持等功能，助力企业优化客户关系，提升销售转化与客户忠诚度', 'https://crm.example.com/callback', '/api/files/images/appLogo_412f520e-9487-4dce-a0fc-72c3e64dd50f.png', 0, 0, '***********', 'admin', '2025-06-10 10:00:00', 'admin', '2025-07-16 16:23:33');
INSERT INTO `apps` VALUES (3, 'ERP系统', 'http://erp.example.com', '整合企业财务、采购、生产、库存、人力资源等核心业务流程的集成化管理平台，通过数据共享与流程自动化实现资源优化配置，提升企业运营效率与决策能力', 'http://erp.example.com/callback', '', 0, 0, '***********', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `apps` VALUES (4, 'HR系统', 'http://hr.example.com', '聚焦人力资源管理的数字化平台，集成员工档案、招聘管理、考勤绩效、培训发展与薪资福利等功能，助力企业实现人才全生命周期管理与组织效能提升', 'http://hr.example.com/callback', '', 0, 0, '***********', 'admin', '2025-06-10 10:00:00', 'admin', '2025-06-27 18:01:58');
INSERT INTO `apps` VALUES (5, 'SCM系统', 'http://scm.example.com', '管理企业供应链全流程的数字化平台，集成采购管理、库存控制、物流跟踪、供应商协作等功能，通过优化供应链效率实现成本降低与交付能力提升', 'http://scm.example.com/callback', '', 0, 0, '13800138005', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-05 10:00:00');
INSERT INTO `apps` VALUES (6, 'BI系统', 'http://bi.example.com', '通过数据采集、分析与可视化工具，整合企业内外部数据，提供报表生成、多维分析、预测建模等功能，助力管理者基于数据洞察驱动业务决策的智能平台', 'http://bi.example.com/callback', '', 0, 0, '13800138006', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-06 10:00:00');
INSERT INTO `apps` VALUES (7, 'CMS系统', 'http://cms.example.com', '用于创建、管理、发布数字内容的平台，集成内容编辑、页面设计、权限控制、工作流管理等功能，支持多终端内容分发与用户互动，助力企业高效运营线上内容资源', 'http://cms.example.com/callback', '', 0, 0, '13800138007', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-07 10:00:00');
INSERT INTO `apps` VALUES (8, 'EAM系统', 'http://eam.example.com', '聚焦企业资产全生命周期管理的数字化平台，集成资产登记、运维保养、检修调度、报废处置等功能，通过智能化管理提升资产利用率与运维效率', 'http://eam.example.com/callback', '', 0, 0, '13800138008', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-08 10:00:00');
INSERT INTO `apps` VALUES (9, 'MES系统', 'http://mes.example.com', '衔接企业计划层与生产控制层的智能制造平台，集成生产排程、工艺执行、设备监控、质量追溯等功能，实时优化生产流程并提升车间数字化管理水平', 'http://mes.example.com/callback', '', 0, 0, '13800138009', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-09 10:00:00');
INSERT INTO `apps` VALUES (10, 'WMS系统', 'http://wms.example.com', '专注于仓库全流程管理的数字化平台，集成入库管理、出库作业、库存盘点、仓位分配等功能，通过智能化调度与可视化监控提升仓储效率与库存准确率', 'http://wms.example.com/callback', '', 0, 0, '13800138010', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-10 10:00:00');
INSERT INTO `apps` VALUES (11, 'TMS系统', 'http://tms.example.com', '聚焦企业运输全流程管理的数字化平台，集成运输计划制定、车辆调度、路径优化、在途监控、运费结算等功能，通过智能化管控提升物流效率与运输成本透明度', 'http://tms.example.com/callback', '', 0, 0, '13800138011', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-11 10:00:00');
INSERT INTO `apps` VALUES (12, 'PMS系统', 'http://pms.example.com', '通过智能化管控提升物业运营效率与服务质量，适用于商业楼宇、住宅小区、产业园区等多种场景的资产运营与服务管理', 'http://pms.example.com/callback', '', 0, 0, '13800138012', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-12 10:00:00');
INSERT INTO `apps` VALUES (13, 'LMS系统', 'http://lms.example.com', '整合课程设计、学习资源管理、进度追踪及互动功能的数字化平台，支持企业员工培训、学校在线教学等场景，通过智能化工具实现学习过程的高效管理与效果评估', 'http://lms.example.com/callback', '', 0, 0, '13800138013', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-13 10:00:00');
INSERT INTO `apps` VALUES (14, 'AA', '', '', '', '', 0, 0, '18888888888', 'AA', '2025-06-11 11:11:49', 'AA', '2025-06-11 11:11:54');
INSERT INTO `apps` VALUES (15, 'OA系统', 'http://oa.example.com', 'OA 系统是整合流程审批、文档管理与协同办公的数字化平台，提升企业运营效率', 'http://oa.example.com/callback', 'https://tse4-mm.cn.bing.net/th/id/OIP-C.OZEWv-DlclJI9AFeFEDDjgHaE8?rs=1&pid=ImgDetMain', 0, 1, '13800138001', 'admin', '2025-05-10 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `apps` VALUES (16, 'CRM系统', 'http://crm.example.com', '用于管理企业与客户交互的数字化平台，集成客户数据管理、销售流程跟踪、营销自动化、客户服务支持等功能，助力企业优化客户关系，提升销售转化与客户忠诚度', 'http://crm.example.com/callback', '', 0, 1, '***********', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `apps` VALUES (17, 'ERP系统', 'http://erp.example.com', '整合企业财务、采购、生产、库存、人力资源等核心业务流程的集成化管理平台，通过数据共享与流程自动化实现资源优化配置，提升企业运营效率与决策能力', 'http://erp.example.com/callback', '', 0, 0, '***********', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `apps` VALUES (18, 'BI系统', 'http://bi.example.com', '通过数据采集、分析与可视化工具，整合企业内外部数据，提供报表生成、多维分析、预测建模等功能，助力管理者基于数据洞察驱动业务决策的智能平台', 'http://bi.example.com/callback', '', 0, 0, '13800138006', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-06 10:00:00');
INSERT INTO `apps` VALUES (19, 'MES系统', 'http://mes.example.com', '衔接企业计划层与生产控制层的智能制造平台，集成生产排程、工艺执行、设备监控、质量追溯等功能，实时优化生产流程并提升车间数字化管理水平', 'http://mes.example.com/callback', '', 0, 0, '13800138009', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-09 10:00:00');
INSERT INTO `apps` VALUES (20, 'LMS系统', 'http://lms.example.com', '整合课程设计、学习资源管理、进度追踪及互动功能的数字化平台，支持企业员工培训、学校在线教学等场景，通过智能化工具实现学习过程的高效管理与效果评估', 'http://lms.example.com/callback', '', 0, 0, '13800138013', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-13 10:00:00');
INSERT INTO `apps` VALUES (21, 'TMS系统', 'http://tms.example.com', '聚焦企业运输全流程管理的数字化平台，集成运输计划制定、车辆调度、路径优化、在途监控、运费结算等功能，通过智能化管控提升物流效率与运输成本透明度', 'http://tms.example.com/callback', '', 0, 0, '13800138011', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-11 10:00:00');
INSERT INTO `apps` VALUES (22, 'PMS系统', 'http://pms.example.com', '通过智能化管控提升物业运营效率与服务质量，适用于商业楼宇、住宅小区、产业园区等多种场景的资产运营与服务管理', 'http://pms.example.com/callback', '', 0, 0, '13800138012', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-12 10:00:00');
INSERT INTO `apps` VALUES (23, 'LMS系统', 'http://lms.example.com', '整合课程设计、学习资源管理、进度追踪及互动功能的数字化平台，支持企业员工培训、学校在线教学等场景，通过智能化工具实现学习过程的高效管理与效果评估', 'http://lms.example.com/callback', '', 0, 0, '13800138013', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-13 10:00:00');
INSERT INTO `apps` VALUES (24, 'AA', 'AA', 'AA', 'AA', 'AA', 0, 0, '18888888888', 'AA', '2025-06-11 11:11:49', 'AA', '2025-06-11 11:11:54');
INSERT INTO `apps` VALUES (25, 'OA系统', 'http://oa.example.com', 'OA 系统是整合流程审批、文档管理与协同办公的数字化平台，提升企业运营效率', 'http://oa.example.com/callback', 'https://tse4-mm.cn.bing.net/th/id/OIP-C.OZEWv-DlclJI9AFeFEDDjgHaE8?rs=1&pid=ImgDetMain', 0, 1, '13800138001', 'admin', '2025-05-10 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `apps` VALUES (26, 'CRM系统', 'http://crm.example.com', '用于管理企业与客户交互的数字化平台，集成客户数据管理、销售流程跟踪、营销自动化、客户服务支持等功能，助力企业优化客户关系，提升销售转化与客户忠诚度', 'http://crm.example.com/callback', '', 0, 1, '***********', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `apps` VALUES (27, 'ERP系统', 'http://erp.example.com', '整合企业财务、采购、生产、库存、人力资源等核心业务流程的集成化管理平台，通过数据共享与流程自动化实现资源优化配置，提升企业运营效率与决策能力', 'http://erp.example.com/callback', '', 0, 0, '***********', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `apps` VALUES (28, 'PM系统', 'http://www.baidu.com', '管理企业供应链全流程的数字化平台，集成采购管理、库存控制、物流跟踪、供应商协作等功能，通过优化供应链效率实现成本降低与交付能力提升', 'http://www.baidu.com', 'https://tse4-mm.cn.bing.net/th/id/OIP-C.OZEWv-DlclJI9AFeFEDDjgHaE8?rs=1&pid=ImgDetMain', 0, 1, '18811112222', '李四', '2025-06-12 17:21:18', NULL, NULL);
INSERT INTO `apps` VALUES (29, 'app', 'https://www.baidu.com', 'test', 'https://www.baidu.com', '', 0, 0, '13677777777', '111', '2025-06-13 15:27:11', NULL, NULL);
INSERT INTO `apps` VALUES (30, '百度', 'https://baidu.com', '百度是中国领先的互联网科技企业，核心作用在于通过搜索引擎技术为用户提供信息获取与服务连接的入口。作为全球最大的中文搜索引擎，它能对海量网页、图片、视频等信息进行抓取与索引，用户输入关键词即可快速获得相关内容，覆盖知识查询、新闻资讯、生活服务等场景，例如搜索“天气”可实时获取各地气象数据，搜索学术关键词能定位相关文献资料。\n除搜索核心功能外，百度构建了多元化服务生态：通过百度地图提供精准导航与位置服务，助力用户规划出行路线；百度百科、百度知道等产品聚合UGC内容，形成开放式知识共享平台；百度糯米整合本地生活服务，连接餐饮、电影等消费场景。此外，其在人工智能领域持续投入，推出百度大脑、文心一言等AI技术与大模型产品，将技术能力赋能至智能驾驶（如Apollo）、智能云等业务，推动产业数字化升级。\n百度的作用本质是“信息枢纽”与“技术赋能者”，既解决用户信息获取的效率问题，也通过技术创新为个人与企业提供数字化工具，在信息传播、生活服务、产业升级等层面发挥着广泛影响。', 'https://baidu.com/callback', '/api/files/images/appLogo_106f1c7c-359b-4992-abcc-4a68e8aea102.png', 0, 0, '***********', 'admin', '2025-06-17 15:02:41', NULL, '2025-07-16 16:24:05');
INSERT INTO `apps` VALUES (31, '百度', 'https://baidu.com', '百度是中国领先的互联网科技企业，核心作用在于通过搜索引擎技术为用户提供信息获取与服务连接的入口。作为全球最大的中文搜索引擎，它能对海量网页、图片、视频等信息进行抓取与索引，用户输入关键词即可快速获得相关内容，覆盖知识查询、新闻资讯、生活服务等场景，例如搜索“天气”可实时获取各地气象数据，搜索学术关键词能定位相关文献资料。\n\n除搜索核心功能外，百度构建了多元化服务生态：通过百度地图提供精准导航与位置服务，助力用户规划出行路线；百度百科、百度知道等产品聚合UGC内容，形成开放式知识共享平台；百度糯米整合本地生活服务，连接餐饮、电影等消费场景。此外，其在人工智能领域持续投入，推出百度大脑、文心一言等AI技术与大模型产品，将技术能力赋能至智能驾驶（如Apollo）、智能云等业务，推动产业数字化升级。\n\n百度的作用本质是“信息枢纽”与“技术赋能者”，既解决用户信息获取的效率问题，也通过技术创新为个人与企业提供数字化工具，在信息传播、生活服务、产业升级等层面发挥着广泛影响。百度是中国领先的互联网科技企业，核心作用在于通过搜索引擎技术为用户提供信息获取与服务连接的入口。作为全球最大的中文搜索引擎，它能对海量网页、图片、视频等信息进行抓取与索引，用户输入关键词即可快速获得相关内容，覆盖知识查询、新闻资讯、生活服务等场景，例如搜索“天气”可实时获取各地气象数据，搜索学术关键词能定位相关文献资料。\n\n除搜索核心功能外，百度构建了多元化服务生态：通过百度地图提供精准导航与位置服务，助力用户规划出行路线；百度百科、百度知道等产品聚合UGC内容，形成开放式知识共享平台；百度糯米整合本地生活服务，连接餐饮、电影等消费场景。此外，其在人工智能领域持续投入，推出百度大脑、文心一言等AI技术与大模型产品，将技术能力赋能至智能驾驶（如Apollo）、智能云等业务，推动产业数字化升级。\n\n百度的作用本质是“信息枢纽”与“技术赋能者”，既解决用户信息获取的效率问题，也通过技术创新为个人与企业提供数字化工具，在信息传播、生活服务、产业升级等层面发挥着广泛影响。百度是中国领先的互联网科技企业，核心作用在于通过搜索引擎技术为用户提供信息获取与服务连接的入口。作为全球最大的中文搜索引擎，它能对海量网页、图片、视频等信息进行抓取与索引，用户输入关键词即可快速获得相关内容，覆盖知识查询、新闻资讯、生活服务等场景，例如', 'https://baidu.com/callback', '', 0, 0, '***********', 'admin', '2025-06-17 15:12:24', NULL, NULL);
INSERT INTO `apps` VALUES (32, '测试应用', 'http://www.baidu.com', '这是一个测试应用并没有什么实际的作用', 'http://www.baidu.com/callback', '', 0, 0, '13666666666', 'admin', '2025-06-19 10:54:09', NULL, NULL);
INSERT INTO `apps` VALUES (33, 'User测试系统', 'https://www.baidu.com', '', 'https://www.baidu.com', '', 1, 0, '13699999999', 'admin', '2025-06-25 17:47:17', NULL, NULL);
INSERT INTO `apps` VALUES (34, '测试', 'http://**********:9082', '', 'https://**********:9082', '/api/files/appLogo_e5fcfdd5-c563-4da7-9c5a-f5075779c822.png', 0, 0, '18966666666', 'admin', '2025-07-02 09:07:34', NULL, NULL);
INSERT INTO `apps` VALUES (35, '用户中心', 'http://localhost:8081', '统一用户管理系统', 'http://localhost:8081/callback', 'user-center-logo.png', 0, 1, '13800138001', 'admin', '2025-07-07 02:26:29', 'admin', '2025-07-07 02:26:29');
INSERT INTO `apps` VALUES (36, '订单系统', 'http://localhost:8082', '电商订单管理系统', 'http://localhost:8082/callback', 'order-system-logo.png', 0, 1, '***********', 'admin', '2025-07-07 02:26:29', 'admin', '2025-07-07 02:26:29');
INSERT INTO `apps` VALUES (37, '财务系统', 'http://localhost:8083', '企业财务管理系统', 'http://localhost:8083/callback', 'finance-system-logo.png', 0, 1, '***********', 'admin', '2025-07-07 02:26:29', 'admin', '2025-07-07 02:26:29');

-- ----------------------------
-- Table structure for authorities
-- ----------------------------
DROP TABLE IF EXISTS `authorities`;
CREATE TABLE `authorities`  (
  `authority_id` bigint NOT NULL AUTO_INCREMENT,
  `authority_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `authority_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `actions` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `creat_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creat_time` datetime NOT NULL,
  `update_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `authority_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `level` int NULL DEFAULT NULL,
  `parent_id` bigint NULL DEFAULT NULL,
  `resource_paths` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `resource_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `sort_order` int NULL DEFAULT NULL,
  `sync_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `sync_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `sync_time` datetime(6) NULL DEFAULT NULL,
  PRIMARY KEY (`authority_id`) USING BTREE,
  UNIQUE INDEX `UK_julcc12xaia89a2et2gbeat0l`(`authority_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 51 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of authorities
-- ----------------------------
INSERT INTO `authorities` VALUES (14, '用户中心管理', '用户中心系统管理根权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'USER_CENTER_MANAGE', 1, NULL, '[\"/user-center\"]', 'menu', 1, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (15, '用户管理', '用户信息管理权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'USER_MANAGE', 2, 14, '[\"/user\"]', 'menu', 1, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (16, '用户列表', '用户列表查看权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'USER_LIST', 3, 15, '[\"/user/list\"]', 'menu', 1, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (17, '用户新增', '用户新增权限', '[\"create\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'USER_CREATE', 3, 16, '[\"/user/add\"]', 'button', 2, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (18, '用户编辑', '用户编辑权限', '[\"update\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'USER_UPDATE', 3, 17, '[\"/user/edit\"]', 'button', 3, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (19, '用户删除', '用户删除权限', '[\"delete\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'USER_DELETE', 3, 2, '[\"/user/delete\"]', 'button', 4, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (20, '角色管理', '角色信息管理权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ROLE_MANAGE', 2, 1, '[\"/role\"]', 'menu', 2, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (21, '角色列表', '角色列表查看权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ROLE_LIST', 3, 7, '[\"/role/list\"]', 'menu', 1, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (22, '角色新增', '角色新增权限', '[\"create\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ROLE_CREATE', 3, 7, '[\"/role/add\"]', 'module', 2, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (23, '角色编辑', '角色编辑权限', '[\"update\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ROLE_UPDATE', 3, 7, '[\"/role/edit\"]', 'module', 3, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (24, '订单系统管理', '订单系统管理根权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ORDER_SYSTEM_MANAGE', 1, NULL, '[\"/order-system\"]', 'menu', 2, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (25, '订单管理', '订单信息管理权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ORDER_MANAGE', 2, 11, '[\"/order\"]', 'menu', 1, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (26, '订单列表', '订单列表查看权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ORDER_LIST', 3, 12, '[\"/order/list\"]', 'menu', 1, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (27, '订单详情', '订单详情查看权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ORDER_DETAIL', 3, 12, '[\"/order/detail\"]', 'menu', 2, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (28, '订单创建', '订单创建权限', '[\"create\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ORDER_CREATE', 3, 12, '[\"/order/create\"]', 'module', 3, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (29, '订单取消', '订单取消权限', '[\"update\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ORDER_CANCEL', 3, 12, '[\"/order/cancel\"]', 'module', 4, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (30, '商品管理', '商品信息管理权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'PRODUCT_MANAGE', 2, 11, '[\"/product\"]', 'menu', 2, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (31, '商品列表', '商品列表查看权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'PRODUCT_LIST', 3, 17, '[\"/product/list\"]', 'menu', 1, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (32, '商品新增', '商品新增权限', '[\"create\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'PRODUCT_CREATE', 3, 17, '[\"/product/add\"]', 'module', 2, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (33, '财务系统管理', '财务系统管理根权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'FINANCE_SYSTEM_MANAGE', 1, NULL, '[\"/finance-system\"]', 'menu', 3, 'FINANCE_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (34, '财务报表', '财务报表管理权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'FINANCE_REPORT', 2, 20, '[\"/report\"]', 'menu', 1, 'FINANCE_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (35, '收入报表', '收入报表查看权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'INCOME_REPORT', 3, 21, '[\"/report/income\"]', 'menu', 1, 'FINANCE_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (36, '支出报表', '支出报表查看权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'EXPENSE_REPORT', 3, 21, '[\"/report/expense\"]', 'menu', 2, 'FINANCE_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (37, '用户API', '用户相关API权限', '[\"read\", \"create\", \"update\", \"delete\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'USER_API', 3, 2, '[\"/api/users/**\"]', 'module', 5, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (38, '订单API', '订单相关API权限', '[\"read\", \"create\", \"update\", \"delete\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ORDER_API', 3, 12, '[\"/api/orders/**\"]', 'module', 5, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (39, '财务API', '财务相关API权限', '[\"read\", \"create\", \"update\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'FINANCE_API', 3, 21, '[\"/api/finance/**\"]', 'module', 3, 'FINANCE_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (40, 'Test Permission', 'Test permission via API', '[\"read\", \"create\"]', NULL, '2025-07-07 11:40:48', NULL, '2025-07-07 11:40:48', 'TEST_API_001', 1, NULL, '[\"/test-api\"]', 'module', 1, 'API_TEST', 'SUCCESS', '2025-07-07 11:40:48.015702');
INSERT INTO `authorities` VALUES (41, 'Batch Permission 1', 'First batch permission', '[\"read\"]', NULL, '2025-07-07 11:41:59', NULL, '2025-07-07 11:41:59', 'BATCH_001', 1, NULL, '[\"/batch1\"]', 'menu', 1, 'API_TEST', 'SUCCESS', '2025-07-07 11:41:59.155447');
INSERT INTO `authorities` VALUES (42, 'Batch Permission 2', 'Second batch permission', '[\"read\", \"update\"]', NULL, '2025-07-07 11:41:59', NULL, '2025-07-07 11:41:59', 'BATCH_002', 1, NULL, '[\"/batch2\"]', 'module', 2, 'API_TEST', 'SUCCESS', '2025-07-07 11:41:59.171314');
INSERT INTO `authorities` VALUES (43, '应用信息管理', '应用信息管理权限', '[\"read\", \"create\", \"update\", \"delete\"]', NULL, '2025-07-07 11:46:08', NULL, '2025-07-07 11:46:08', '_MANAGE', 1, NULL, '[\"/app\", \"/app/list\"]', 'menu', 1, 'APP_CENTER', 'SUCCESS', '2025-07-07 11:46:07.911398');
INSERT INTO `authorities` VALUES (44, '应用信息管理', '应用信息管理权限', '[\"read\", \"create\", \"update\", \"delete\"]', NULL, '2025-07-08 10:31:32', NULL, '2025-07-08 10:31:32', 'APP_MANAGE', 1, NULL, '[\"/app\", \"/app/list\"]', 'menu', 1, 'APP_CENTER', 'SUCCESS', '2025-07-08 10:31:32.449180');
INSERT INTO `authorities` VALUES (45, 'User Query', 'Query user information', '[\"read\"]', NULL, '2025-07-08 10:51:51', NULL, '2025-07-08 10:51:51', 'USER_QUERY_NEW', 1, NULL, '[\"/user/query\"]', 'menu', 1, 'USER_CENTER', 'SUCCESS', '2025-07-08 10:51:50.973079');
INSERT INTO `authorities` VALUES (46, 'User Create', 'Create new user', '[\"create\"]', NULL, '2025-07-08 10:51:51', NULL, '2025-07-08 10:51:51', 'USER_CREATE_NEW', 1, NULL, '[\"/user/create\"]', 'module', 2, 'USER_CENTER', 'SUCCESS', '2025-07-08 10:51:50.991085');
INSERT INTO `authorities` VALUES (47, '用户查询', '查询用户信息的权限', '[\"read\"]', NULL, '2025-07-08 10:55:06', NULL, '2025-07-08 10:55:06', 'USER_QUERY', 1, NULL, '[\"/user/query\", \"/user/search\"]', 'menu', 1, 'USER_CENTER', 'SUCCESS', '2025-07-08 10:55:06.200918');
INSERT INTO `authorities` VALUES (48, '用户编辑', '编辑用户信息的权限', '[\"update\"]', NULL, '2025-07-08 10:55:06', NULL, '2025-07-08 10:55:06', 'USER_EDIT', 2, 1, '[\"/user/edit\", \"/user/update\"]', 'module', 2, 'USER_CENTER', 'SUCCESS', '2025-07-08 10:55:06.209922');
INSERT INTO `authorities` VALUES (49, '用户导出', '导出用户数据的权限', '[\"read\", \"export\"]', NULL, '2025-07-08 10:55:06', NULL, '2025-07-08 10:55:06', 'USER_EXPORT', 2, 1, '[\"/user/export\"]', 'module', 4, 'USER_CENTER', 'SUCCESS', '2025-07-08 10:55:06.215926');
INSERT INTO `authorities` VALUES (50, '应用信息管理', '应用信息管理权限', '[\"read\", \"create\", \"update\", \"delete\"]', NULL, '2025-07-16 14:24:37', NULL, '2025-07-16 14:24:38', 'APP_MANAGE1', 1, NULL, '[\"/app\", \"/app/list\"]', 'menu', 1, 'APP_CENTER', 'SUCCESS', '2025-07-16 14:24:37.723296');

-- ----------------------------
-- Table structure for departments
-- ----------------------------
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments`  (
  `department_id` bigint NOT NULL AUTO_INCREMENT,
  `department_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `department_head` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `department_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `del_flag` int NOT NULL DEFAULT 0,
  `level` int NOT NULL COMMENT '部门层级，1表示一级部门，2表示二级部门等',
  `parent_id` bigint NULL DEFAULT NULL COMMENT '父部门ID，顶级部门为null',
  `order_num` int NOT NULL COMMENT '同级部门排序号',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '部门路径，如\"0,1,2\"表示该部门的父路径',
  `has_children` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否有子部门',
  `creat_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creat_time` datetime NOT NULL,
  `update_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`department_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 78 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of departments
-- ----------------------------
INSERT INTO `departments` VALUES (1, '技术部', '张三', '负责技术研发', 'A栋1楼', 0, 1, NULL, 0, '0', 1, 'admin', '2023-01-01 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `departments` VALUES (2, '财务部', '李四', '财务管理', 'A栋2楼', 0, 1, NULL, 10, '0', 1, 'admin', '2023-01-01 10:00:00', 'admin', '2025-06-18 10:00:30');
INSERT INTO `departments` VALUES (3, '人事部', '王五', '人事管理', 'A栋3楼', 0, 1, NULL, 9, '0', 1, 'admin', '2023-01-01 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `departments` VALUES (4, '市场部', '赵六', '市场营销', 'B栋1楼', 0, 1, NULL, 7, '0', 0, 'admin', '2023-01-04 10:00:00', 'admin', '2025-06-13 14:06:09');
INSERT INTO `departments` VALUES (5, '销售部', '钱七', '产品销售', 'B栋2楼', 0, 1, NULL, 1, '0,5', 0, 'admin', '2023-01-05 10:00:00', 'admin', '2023-01-05 10:00:00');
INSERT INTO `departments` VALUES (6, '客服部', '孙八', '客户服务', 'B栋3楼', 0, 1, NULL, 11, '0', 1, 'admin', '2023-01-06 10:00:00', 'admin', '2023-01-06 10:00:00');
INSERT INTO `departments` VALUES (7, '采购部', '周九', '物资采购', 'C栋1楼', 0, 1, NULL, 3, '0', 0, 'admin', '2023-01-07 10:00:00', 'admin', '2025-06-19 17:45:38');
INSERT INTO `departments` VALUES (8, '仓储部', '吴十', '仓储管理', 'C栋2楼', 0, 1, NULL, 8, '0', 0, 'admin', '2023-01-08 10:00:00', 'admin', '2023-01-08 10:00:00');
INSERT INTO `departments` VALUES (9, '质检部', '郑十一', '质量检测', 'C栋3楼', 0, 1, NULL, 12, '0', 1, 'admin', '2023-01-09 10:00:00', 'admin', '2023-01-09 10:00:00');
INSERT INTO `departments` VALUES (10, '研发部', '王十二', '产品研发', 'D栋1楼', 0, 1, NULL, 6, '0', 1, 'admin', '2023-01-10 10:00:00', 'admin', '2025-06-20 09:07:09');
INSERT INTO `departments` VALUES (11, '设计部', '冯十三', '产品设计', 'D栋2楼', 0, 1, NULL, 14, '0', 1, 'admin', '2023-01-11 10:00:00', 'admin', '2023-01-11 10:00:00');
INSERT INTO `departments` VALUES (12, '法务部', '陈十四', '法律事务', 'D栋3楼', 0, 1, NULL, 13, '0', 0, 'admin', '2023-01-12 10:00:00', 'admin', '2025-06-18 09:59:35');
INSERT INTO `departments` VALUES (13, '行政部', '褚十五', '行政管理', 'E栋1楼', 0, 1, NULL, 5, '0', 0, 'admin', '2023-01-13 10:00:00', 'admin', '2023-01-13 10:00:00');
INSERT INTO `departments` VALUES (15, '测试部门2', '默认负责人', '默认部门描述', '默认地址', 1, 1, NULL, 15, '0,15', 0, '系统管理员', '2025-06-11 09:34:50', '系统管理员', '2025-06-11 09:47:42');
INSERT INTO `departments` VALUES (23, '新子部门', '默认负责人', '默认部门描述', '默认地址', 0, 3, 20, 1, '0,23', 0, '系统管理员', '2025-06-13 16:13:40', '系统管理员', '2025-06-13 16:13:40');
INSERT INTO `departments` VALUES (24, '新子部门', '默认负责人', '默认部门描述', '默认地址', 0, 5, 22, 1, '0,24', 0, '系统管理员', '2025-06-13 16:21:39', '系统管理员', '2025-06-13 16:21:39');
INSERT INTO `departments` VALUES (25, '新子部门', '默认负责人', '默认部门描述', '默认地址', 0, 3, 19, 1, '0,5,25', 0, '系统管理员', '2025-06-16 10:33:32', '系统管理员', '2025-06-16 10:33:32');
INSERT INTO `departments` VALUES (29, '新子部门', '默认负责人', '默认部门描述', '默认地址', 0, 3, 21, 1, '0,5,21,null', 0, '系统管理员', '2025-06-18 09:59:54', '系统管理员', '2025-06-18 09:59:54');
INSERT INTO `departments` VALUES (56, '新子部门', '默认负责人', '默认部门描述', '默认地址', 0, 3, 53, 1, '0,null,null', 0, '系统管理员', '2025-07-01 16:10:35', '系统管理员', '2025-07-01 16:10:35');
INSERT INTO `departments` VALUES (58, '新子部门', '默认负责人', '默认部门描述', '默认地址', 0, 4, 56, 1, '0,null,null,null', 0, '系统管理员', '2025-07-01 16:10:53', '系统管理员', '2025-07-01 16:10:53');
INSERT INTO `departments` VALUES (64, '新子部门', '默认负责人', '默认部门描述', '默认地址', 0, 2, 54, 1, '0,null,null', 0, '系统管理员', '2025-07-01 16:15:18', '系统管理员', '2025-07-01 16:15:18');
INSERT INTO `departments` VALUES (70, '新子部门', '默认负责人', '默认部门描述', '默认地址', 0, 3, 69, 1, '0,70', 0, '系统管理员', '2025-07-01 16:47:13', '系统管理员', '2025-07-01 16:47:13');
INSERT INTO `departments` VALUES (72, '新子部门', '默认负责人', '默认部门描述', '默认地址', 0, 3, 69, 3, '0,72', 0, '系统管理员', '2025-07-01 16:47:21', '系统管理员', '2025-07-01 16:47:21');
INSERT INTO `departments` VALUES (77, '新子部门', '默认负责人', '默认部门描述', '默认地址', 0, 3, 76, 1, '0,null,null', 0, '系统管理员', '2025-07-02 17:29:03', '系统管理员', '2025-07-02 17:29:03');

-- ----------------------------
-- Table structure for departments_authorities
-- ----------------------------
DROP TABLE IF EXISTS `departments_authorities`;
CREATE TABLE `departments_authorities`  (
  `authority_id` bigint NOT NULL,
  `department_id` bigint NOT NULL,
  PRIMARY KEY (`authority_id`, `department_id`) USING BTREE,
  INDEX `FKpatbmec9yvl0ipgami6qsduf3`(`department_id` ASC) USING BTREE,
  CONSTRAINT `FKi6q23wgbp7h6m0x8megx7qrem` FOREIGN KEY (`authority_id`) REFERENCES `authorities` (`authority_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKpatbmec9yvl0ipgami6qsduf3` FOREIGN KEY (`department_id`) REFERENCES `departments` (`department_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of departments_authorities
-- ----------------------------

-- ----------------------------
-- Table structure for modules
-- ----------------------------
DROP TABLE IF EXISTS `modules`;
CREATE TABLE `modules`  (
  `module_id` bigint NOT NULL AUTO_INCREMENT,
  `module_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `module_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `module_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `parent_id` int NULL DEFAULT 0,
  `del_flag` int NOT NULL DEFAULT 0,
  `point_user_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creat_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creat_time` datetime NOT NULL,
  `update_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`module_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 35 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of modules
-- ----------------------------
INSERT INTO `modules` VALUES (1, '用户管理模块', '用户管理模块', 'http://oa.example.com/user', 0, 0, '13800138001', 'admin', '2023-01-01 10:00:00', 'admin', '2025-07-18 14:05:25');
INSERT INTO `modules` VALUES (2, '角色管理模块', '角色管理模块', 'http://oa.example.com/role', 0, 1, '***********', 'admin', '2023-01-01 10:00:00', 'admin', '2025-06-30 10:02:32');
INSERT INTO `modules` VALUES (3, '权限管理模块', '权限管理模块', 'http://oa.example.com/auth', 0, 0, '***********', 'admin', '2023-01-01 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `modules` VALUES (4, '部门管理模块', '部门管理模块', 'http://oa.example.com/department', 0, 0, '13800138004', 'admin', '2023-01-04 10:00:00', 'admin', '2023-01-04 10:00:00');
INSERT INTO `modules` VALUES (5, '审批管理模块', '审批管理模块', 'http://oa.example.com/approval', 0, 0, '13800138005', 'admin', '2023-01-05 10:00:00', 'admin', '2023-01-05 10:00:00');
INSERT INTO `modules` VALUES (6, '文件管理模块', '文件管理模块', 'http://oa.example.com/file', 0, 0, '13800138006', 'admin', '2023-01-06 10:00:00', 'admin', '2023-01-06 10:00:00');
INSERT INTO `modules` VALUES (7, '项目管理模块', '项目管理模块', 'http://oa.example.com/project', 0, 0, '13800138007', 'admin', '2023-01-07 10:00:00', 'admin', '2023-01-07 10:00:00');
INSERT INTO `modules` VALUES (8, '任务管理模块', '任务管理模块', 'http://oa.example.com/task', 0, 0, '13800138008', 'admin', '2023-01-08 10:00:00', 'admin', '2023-01-08 10:00:00');
INSERT INTO `modules` VALUES (9, '日程管理模块', '日程管理模块', 'http://oa.example.com/schedule', 0, 0, '13800138009', 'admin', '2023-01-09 10:00:00', 'admin', '2023-01-09 10:00:00');
INSERT INTO `modules` VALUES (10, '公告管理模块', '公告管理模块', 'http://oa.example.com/notice', 0, 0, '13800138010', 'admin', '2023-01-10 10:00:00', 'admin', '2023-01-10 10:00:00');
INSERT INTO `modules` VALUES (11, '考勤管理模块', '考勤管理模块', 'http://oa.example.com/attendance', 0, 0, '13800138011', 'admin', '2023-01-11 10:00:00', 'admin', '2023-01-11 10:00:00');
INSERT INTO `modules` VALUES (12, '薪资管理模块', '薪资管理模块', 'http://oa.example.com/salary', 0, 0, '13800138012', 'admin', '2023-01-12 10:00:00', 'admin', '2023-01-12 10:00:00');
INSERT INTO `modules` VALUES (13, '系统设置模块', '系统设置模块', 'http://oa.example.com/setting', 0, 0, '13800138013', 'admin', '2023-01-13 10:00:00', 'admin', '2025-06-23 14:13:35');
INSERT INTO `modules` VALUES (14, 'test', '这是一个测试模块并没有实际的作用', '//www.baidu.com', 3, 1, '13666666666', 'admin', '2025-06-18 09:58:40', 'admin', '2025-06-27 21:29:19');
INSERT INTO `modules` VALUES (15, 'test', '这是一个测试模块并没有实际的作用', '//www.baidu.com', 1, 0, '13666666666', 'admin', '2025-06-18 10:18:16', 'admin', '2025-06-27 18:01:29');
INSERT INTO `modules` VALUES (16, 'test', '这是一个测试模块并没有实际的作用', '/test', 17, 1, '13666666666', 'admin', '2025-06-18 15:47:47', 'admin', '2025-07-03 15:29:36');
INSERT INTO `modules` VALUES (17, 'test', '这是一个测试模块并没有实际的作用', '//www.baidu.com', 1, 1, '13666666666', 'admin', '2025-06-18 15:51:59', 'admin', '2025-06-27 20:24:53');
INSERT INTO `modules` VALUES (18, '测试模块', '测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用', 'www.baidu,com', 2, 1, '13800138001', 'admin', '2025-06-18 16:56:51', 'admin', '2025-06-27 21:39:29');
INSERT INTO `modules` VALUES (19, 'test', '这是一个测试模块并没有实际的作用', '//www.baidu.com', 1, 1, '13666666666', 'admin', '2025-06-23 11:01:10', NULL, NULL);
INSERT INTO `modules` VALUES (20, 'test', '这是一个测试模块并没有实际的作用', '//www.baidu.com', 1, 0, '13666666666', 'admin', '2025-06-23 11:03:48', NULL, NULL);
INSERT INTO `modules` VALUES (21, 'test', '这是一个测试模块并没有实际的作用', '//www.baidu.com', 1, 0, '13666666666', 'admin', '2025-06-23 11:05:09', NULL, NULL);
INSERT INTO `modules` VALUES (22, 'test', '这是一个测试模块并没有实际的作用', '//www.baidu.com', 1, 0, '13666666666', 'admin', '2025-06-23 11:12:08', NULL, NULL);
INSERT INTO `modules` VALUES (23, '测试', '', 'test', 16, 0, '13800138001', 'admin', '2025-06-23 11:21:17', 'admin', '2025-07-03 15:33:33');
INSERT INTO `modules` VALUES (24, '详情', '', 'www.baidu.com', 23, 1, '13800138001', 'admin', '2025-06-27 15:42:24', 'admin', '2025-06-27 16:32:26');
INSERT INTO `modules` VALUES (25, '详情', '', 'www.baidu.com', 4, 1, '13800138001', 'admin', '2025-06-27 20:38:31', 'admin', '2025-06-27 20:38:39');
INSERT INTO `modules` VALUES (26, '用户管理', '用户信息管理模块', '/user', 0, 0, '13800138001', 'admin', '2025-07-07 02:28:59', 'admin', '2025-07-07 02:28:59');
INSERT INTO `modules` VALUES (27, '角色管理', '角色权限管理模块', '/role', 0, 0, '13800138001', 'admin', '2025-07-07 02:28:59', 'admin', '2025-07-07 02:28:59');
INSERT INTO `modules` VALUES (28, '部门管理', '组织架构管理模块', '/department', 0, 0, '13800138001', 'admin', '2025-07-07 02:28:59', 'admin', '2025-07-07 02:28:59');
INSERT INTO `modules` VALUES (29, '订单管理', '订单信息管理模块', '/order', 0, 0, '***********', 'admin', '2025-07-07 02:28:59', 'admin', '2025-07-07 02:28:59');
INSERT INTO `modules` VALUES (30, '商品管理', '商品信息管理模块', '/product', 0, 0, '***********', 'admin', '2025-07-07 02:28:59', 'admin', '2025-07-07 02:28:59');
INSERT INTO `modules` VALUES (31, '库存管理', '库存信息管理模块', '/inventory', 0, 0, '***********', 'admin', '2025-07-07 02:28:59', 'admin', '2025-07-07 02:28:59');
INSERT INTO `modules` VALUES (32, '财务报表', '财务报表管理模块', '/report', 0, 0, '***********', 'admin', '2025-07-07 02:28:59', 'admin', '2025-07-07 02:28:59');
INSERT INTO `modules` VALUES (33, '账务管理', '账务信息管理模块', '/account', 0, 0, '***********', 'admin', '2025-07-07 02:28:59', 'admin', '2025-07-07 02:28:59');
INSERT INTO `modules` VALUES (34, '用户管理模块', '本模块管理该公司在百度应用下所有用户的账号', 'baidu.com', 0, 0, '***********', 'admin', '2025-07-16 16:45:25', NULL, NULL);

-- ----------------------------
-- Table structure for modules_apps
-- ----------------------------
DROP TABLE IF EXISTS `modules_apps`;
CREATE TABLE `modules_apps`  (
  `app_id` bigint NOT NULL,
  `module_id` bigint NOT NULL,
  PRIMARY KEY (`app_id`, `module_id`) USING BTREE,
  INDEX `FK6goimpfk6g6agr365xea0k3hq`(`module_id` ASC) USING BTREE,
  CONSTRAINT `FK6goimpfk6g6agr365xea0k3hq` FOREIGN KEY (`module_id`) REFERENCES `modules` (`module_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKa7b3hlg1c3bqyg7gcux4vionl` FOREIGN KEY (`app_id`) REFERENCES `apps` (`app_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of modules_apps
-- ----------------------------
INSERT INTO `modules_apps` VALUES (1, 1);
INSERT INTO `modules_apps` VALUES (2, 1);
INSERT INTO `modules_apps` VALUES (3, 1);
INSERT INTO `modules_apps` VALUES (4, 1);
INSERT INTO `modules_apps` VALUES (5, 1);
INSERT INTO `modules_apps` VALUES (1, 2);
INSERT INTO `modules_apps` VALUES (2, 2);
INSERT INTO `modules_apps` VALUES (3, 2);
INSERT INTO `modules_apps` VALUES (4, 2);
INSERT INTO `modules_apps` VALUES (5, 2);
INSERT INTO `modules_apps` VALUES (1, 3);
INSERT INTO `modules_apps` VALUES (2, 3);
INSERT INTO `modules_apps` VALUES (3, 3);
INSERT INTO `modules_apps` VALUES (4, 3);
INSERT INTO `modules_apps` VALUES (5, 3);
INSERT INTO `modules_apps` VALUES (1, 4);
INSERT INTO `modules_apps` VALUES (2, 4);
INSERT INTO `modules_apps` VALUES (3, 4);
INSERT INTO `modules_apps` VALUES (4, 4);
INSERT INTO `modules_apps` VALUES (5, 4);
INSERT INTO `modules_apps` VALUES (1, 5);
INSERT INTO `modules_apps` VALUES (2, 5);
INSERT INTO `modules_apps` VALUES (3, 5);
INSERT INTO `modules_apps` VALUES (4, 5);
INSERT INTO `modules_apps` VALUES (5, 5);
INSERT INTO `modules_apps` VALUES (1, 6);
INSERT INTO `modules_apps` VALUES (2, 6);
INSERT INTO `modules_apps` VALUES (3, 6);
INSERT INTO `modules_apps` VALUES (4, 6);
INSERT INTO `modules_apps` VALUES (5, 6);
INSERT INTO `modules_apps` VALUES (1, 7);
INSERT INTO `modules_apps` VALUES (2, 7);
INSERT INTO `modules_apps` VALUES (3, 7);
INSERT INTO `modules_apps` VALUES (4, 7);
INSERT INTO `modules_apps` VALUES (5, 7);
INSERT INTO `modules_apps` VALUES (1, 8);
INSERT INTO `modules_apps` VALUES (2, 8);
INSERT INTO `modules_apps` VALUES (3, 8);
INSERT INTO `modules_apps` VALUES (4, 8);
INSERT INTO `modules_apps` VALUES (5, 8);
INSERT INTO `modules_apps` VALUES (1, 9);
INSERT INTO `modules_apps` VALUES (2, 9);
INSERT INTO `modules_apps` VALUES (3, 9);
INSERT INTO `modules_apps` VALUES (4, 9);
INSERT INTO `modules_apps` VALUES (5, 9);
INSERT INTO `modules_apps` VALUES (1, 10);
INSERT INTO `modules_apps` VALUES (2, 10);
INSERT INTO `modules_apps` VALUES (3, 10);
INSERT INTO `modules_apps` VALUES (4, 10);
INSERT INTO `modules_apps` VALUES (5, 10);
INSERT INTO `modules_apps` VALUES (1, 11);
INSERT INTO `modules_apps` VALUES (2, 11);
INSERT INTO `modules_apps` VALUES (3, 11);
INSERT INTO `modules_apps` VALUES (4, 11);
INSERT INTO `modules_apps` VALUES (5, 11);
INSERT INTO `modules_apps` VALUES (1, 12);
INSERT INTO `modules_apps` VALUES (2, 12);
INSERT INTO `modules_apps` VALUES (3, 12);
INSERT INTO `modules_apps` VALUES (4, 12);
INSERT INTO `modules_apps` VALUES (5, 12);
INSERT INTO `modules_apps` VALUES (1, 13);
INSERT INTO `modules_apps` VALUES (3, 13);
INSERT INTO `modules_apps` VALUES (4, 13);
INSERT INTO `modules_apps` VALUES (5, 13);
INSERT INTO `modules_apps` VALUES (30, 34);

-- ----------------------------
-- Table structure for modules_authorities
-- ----------------------------
DROP TABLE IF EXISTS `modules_authorities`;
CREATE TABLE `modules_authorities`  (
  `authority_id` bigint NOT NULL,
  `module_id` bigint NOT NULL,
  PRIMARY KEY (`authority_id`, `module_id`) USING BTREE,
  INDEX `FK2sfnl3otmu97rw6ecdtyu7oqf`(`module_id` ASC) USING BTREE,
  CONSTRAINT `FK2sfnl3otmu97rw6ecdtyu7oqf` FOREIGN KEY (`module_id`) REFERENCES `modules` (`module_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKsrng59yke20ahcm4qv7bd4ttn` FOREIGN KEY (`authority_id`) REFERENCES `authorities` (`authority_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of modules_authorities
-- ----------------------------
INSERT INTO `modules_authorities` VALUES (40, 1);
INSERT INTO `modules_authorities` VALUES (41, 1);
INSERT INTO `modules_authorities` VALUES (42, 1);
INSERT INTO `modules_authorities` VALUES (43, 1);
INSERT INTO `modules_authorities` VALUES (44, 1);
INSERT INTO `modules_authorities` VALUES (45, 1);
INSERT INTO `modules_authorities` VALUES (46, 1);
INSERT INTO `modules_authorities` VALUES (47, 1);
INSERT INTO `modules_authorities` VALUES (48, 1);
INSERT INTO `modules_authorities` VALUES (49, 1);
INSERT INTO `modules_authorities` VALUES (50, 1);
INSERT INTO `modules_authorities` VALUES (40, 2);

-- ----------------------------
-- Table structure for policies
-- ----------------------------
DROP TABLE IF EXISTS `policies`;
CREATE TABLE `policies`  (
  `policy_id` bigint NOT NULL AUTO_INCREMENT,
  `policy_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `policy_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `del_flag` int NOT NULL DEFAULT 0,
  `type` int NOT NULL,
  `creat_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `creat_time` datetime NOT NULL,
  `update_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `update_time` datetime NOT NULL,
  `actions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `conditions` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `effect` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `effective_time` datetime(6) NULL DEFAULT NULL,
  `expire_time` datetime(6) NULL DEFAULT NULL,
  `policy_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `policy_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `priority` int NULL DEFAULT NULL,
  `resources` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `status` int NULL DEFAULT NULL,
  PRIMARY KEY (`policy_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of policies
-- ----------------------------
INSERT INTO `policies` VALUES (1, 'Admin_Full_Access', '管理员对所有资源具有完全访问权限', 0, 1, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `policies` VALUES (2, 'User_Read_Only', '用户对特定资源具有只读访问权限', 0, 1, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `policies` VALUES (3, 'HR_Department_Read_Write', '人力资源部门可以读取和写入员工信息', 0, 1, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `policies` VALUES (4, 'Finance_Department_Full_Access', '财务部门对所有财务报告具有完全访问权限', 0, 1, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `policies` VALUES (5, 'Project_Team_Read_Write', '项目团队可以读取和写入项目文档', 0, 1, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `policies` VALUES (6, 'Temporary_Access', '特定用户或角色的临时访问权限', 0, 1, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `policies` VALUES (7, 'Restricted_Area_Access', '受限区域仅允许特定用户组访问', 0, 1, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `policies` VALUES (8, 'Log_Viewing_Permission', '查看系统日志的权限', 0, 1, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `policies` VALUES (9, 'API_Sync_Policy', 'API同步策略', 0, 2, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `policies` VALUES (10, 'Custom_Policy_1', '自定义策略描述1', 0, 3, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `policies` VALUES (11, 'Custom_Policy_2', '自定义策略描述2', 0, 3, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `policies` VALUES (12, 'Custom_Policy_3', '自定义策略描述3', 0, 3, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);
INSERT INTO `policies` VALUES (13, 'Custom_Policy_4', '自定义策略描述4', 0, 3, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL);

-- ----------------------------
-- Table structure for policies_apps
-- ----------------------------
DROP TABLE IF EXISTS `policies_apps`;
CREATE TABLE `policies_apps`  (
  `app_id` bigint NOT NULL,
  `policy_id` bigint NOT NULL,
  PRIMARY KEY (`app_id`, `policy_id`) USING BTREE,
  INDEX `FKkg3s5yneewenhf2e23wnpqrjr`(`policy_id` ASC) USING BTREE,
  CONSTRAINT `FKceh3nebwei2n8nysg4q1aqy9m` FOREIGN KEY (`app_id`) REFERENCES `apps` (`app_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKkg3s5yneewenhf2e23wnpqrjr` FOREIGN KEY (`policy_id`) REFERENCES `policies` (`policy_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of policies_apps
-- ----------------------------

-- ----------------------------
-- Table structure for policies_roles
-- ----------------------------
DROP TABLE IF EXISTS `policies_roles`;
CREATE TABLE `policies_roles`  (
  `policy_id` bigint NOT NULL,
  `role_id` bigint NOT NULL,
  PRIMARY KEY (`policy_id`, `role_id`) USING BTREE,
  INDEX `FKp5wochninp410qpharpfj9wao`(`role_id` ASC) USING BTREE,
  CONSTRAINT `FKaf79u56b5d5tpmsah2ti185pg` FOREIGN KEY (`policy_id`) REFERENCES `policies` (`policy_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKp5wochninp410qpharpfj9wao` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of policies_roles
-- ----------------------------

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles`  (
  `role_id` bigint NOT NULL AUTO_INCREMENT,
  `role_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `role_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `del_flag` int NOT NULL DEFAULT 0,
  `creat_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creat_time` datetime NOT NULL,
  `update_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `parent_role_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`role_id`) USING BTREE,
  INDEX `FKbrhtutbmiqnbmn42ggrn8ewhh`(`parent_role_id` ASC) USING BTREE,
  CONSTRAINT `FKbrhtutbmiqnbmn42ggrn8ewhh` FOREIGN KEY (`parent_role_id`) REFERENCES `roles` (`role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 24 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of roles
-- ----------------------------
INSERT INTO `roles` VALUES (1, '管理员', '系统管理员，拥有所有权限', 0, 'admin', '2023-01-01 10:00:00', 'system', '2025-07-02 17:47:40', NULL);
INSERT INTO `roles` VALUES (2, '部门经理', '部门管理者，拥有部门管理权限', 0, 'admin', '2023-01-01 10:00:00', 'system', '2025-07-02 17:35:05', NULL);
INSERT INTO `roles` VALUES (3, '普通员工', '普通员工，拥有基本权限', 0, 'admin', '2023-01-01 10:00:00', 'system', '2025-06-26 10:28:08', NULL);
INSERT INTO `roles` VALUES (4, '项目经理', '项目管理负责人，拥有项目管理权限', 0, 'admin', '2023-01-04 10:00:00', 'system', '2025-06-26 10:35:24', NULL);
INSERT INTO `roles` VALUES (5, '财务专员', '财务工作人员，拥有财务相关权限', 0, 'admin', '2023-01-05 10:00:00', 'system', '2025-06-26 10:38:03', NULL);
INSERT INTO `roles` VALUES (6, '人事专员', '人事工作人员，拥有人事相关权限', 0, 'admin', '2023-01-06 10:00:00', 'system', '2025-06-27 14:34:59', NULL);
INSERT INTO `roles` VALUES (7, '市场专员', '市场工作人员，拥有市场相关权限', 0, 'admin', '2023-01-07 10:00:00', 'system', '2025-06-27 15:28:39', NULL);
INSERT INTO `roles` VALUES (8, '销售代表', '销售人员，拥有销售相关权限', 0, 'admin', '2023-01-08 10:00:00', 'system', '2025-06-18 15:51:56', NULL);
INSERT INTO `roles` VALUES (9, '客服代表', '客服人员，拥有客服相关权限', 0, 'admin', '2023-01-09 10:00:00', 'admin', '2025-06-18 15:51:56', NULL);
INSERT INTO `roles` VALUES (10, '采购专员', '采购人员，拥有采购相关权限', 0, 'admin', '2023-01-10 10:00:00', 'admin', '2025-06-27 11:33:41', NULL);
INSERT INTO `roles` VALUES (11, '仓储管理员', '仓储管理人员，拥有仓储相关权限', 0, 'admin', '2023-01-11 10:00:00', 'admin', '2023-01-11 10:00:00', NULL);
INSERT INTO `roles` VALUES (12, '质检员', '质量检测人员，拥有质检相关权限', 0, 'admin', '2023-01-12 10:00:00', 'admin', '2023-01-12 10:00:00', NULL);
INSERT INTO `roles` VALUES (13, '系统维护员', '系统维护人员，拥有系统维护权限，系统维护人员，拥有系统维护权限，系统维护人员，拥有系统维护权限，系统维护人员，拥有系统维护权限', 0, 'admin', '2023-01-13 10:00:00', 'admin', '2023-01-13 10:00:00', NULL);
INSERT INTO `roles` VALUES (14, 'string', 'string', 0, 'system', '2025-06-04 11:29:33', 'system', '2025-06-04 11:30:12', NULL);
INSERT INTO `roles` VALUES (15, '自定义角色', '无', 0, 'system', '2025-06-04 14:09:06', 'system', '2025-06-04 14:19:37', NULL);
INSERT INTO `roles` VALUES (16, '自定义2', '无', 0, 'system', '2025-06-04 14:20:19', 'system', '2025-06-04 14:43:48', NULL);
INSERT INTO `roles` VALUES (17, '自定义3', '五', 0, 'system', '2025-06-04 14:44:18', 'system', '2025-06-04 14:44:18', NULL);
INSERT INTO `roles` VALUES (18, '自定义角色4', '无', 0, 'system', '2025-06-04 15:43:34', 'system', '2025-06-04 15:43:34', NULL);
INSERT INTO `roles` VALUES (19, '自定义角色5', 'string', 0, 'system', '2025-06-05 12:38:25', 'system', '2025-06-05 12:38:25', NULL);
INSERT INTO `roles` VALUES (20, '自定义角色6', '', 0, 'system', '2025-06-05 12:38:43', 'system', '2025-06-05 12:38:43', NULL);
INSERT INTO `roles` VALUES (21, '自定义角色61', '', 0, 'system', '2025-06-05 12:42:43', 'system', '2025-06-05 14:10:56', NULL);
INSERT INTO `roles` VALUES (22, '自定义角色8', '', 0, 'system', '2025-06-05 14:22:33', 'system', '2025-06-05 14:22:33', NULL);
INSERT INTO `roles` VALUES (23, '自定义1', '', 0, 'system', '2025-06-05 14:45:49', 'system', '2025-06-06 09:51:04', NULL);

-- ----------------------------
-- Table structure for roles_apps
-- ----------------------------
DROP TABLE IF EXISTS `roles_apps`;
CREATE TABLE `roles_apps`  (
  `app_id` bigint NOT NULL,
  `role_id` bigint NOT NULL,
  PRIMARY KEY (`app_id`, `role_id`) USING BTREE,
  INDEX `FK6hqa73ocl2taqluni40wlducr`(`role_id` ASC) USING BTREE,
  CONSTRAINT `FK6hqa73ocl2taqluni40wlducr` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKrwabmsm9r7lwrst0ur3vhinjh` FOREIGN KEY (`app_id`) REFERENCES `apps` (`app_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of roles_apps
-- ----------------------------
INSERT INTO `roles_apps` VALUES (1, 1);
INSERT INTO `roles_apps` VALUES (2, 1);
INSERT INTO `roles_apps` VALUES (3, 1);
INSERT INTO `roles_apps` VALUES (4, 1);
INSERT INTO `roles_apps` VALUES (5, 1);
INSERT INTO `roles_apps` VALUES (1, 2);
INSERT INTO `roles_apps` VALUES (2, 2);
INSERT INTO `roles_apps` VALUES (3, 2);
INSERT INTO `roles_apps` VALUES (4, 2);
INSERT INTO `roles_apps` VALUES (5, 2);
INSERT INTO `roles_apps` VALUES (1, 3);
INSERT INTO `roles_apps` VALUES (2, 3);
INSERT INTO `roles_apps` VALUES (3, 3);
INSERT INTO `roles_apps` VALUES (4, 3);
INSERT INTO `roles_apps` VALUES (5, 3);
INSERT INTO `roles_apps` VALUES (1, 4);
INSERT INTO `roles_apps` VALUES (2, 4);
INSERT INTO `roles_apps` VALUES (3, 4);
INSERT INTO `roles_apps` VALUES (4, 4);
INSERT INTO `roles_apps` VALUES (5, 4);
INSERT INTO `roles_apps` VALUES (1, 5);
INSERT INTO `roles_apps` VALUES (2, 5);
INSERT INTO `roles_apps` VALUES (3, 5);
INSERT INTO `roles_apps` VALUES (4, 5);
INSERT INTO `roles_apps` VALUES (5, 5);
INSERT INTO `roles_apps` VALUES (1, 6);
INSERT INTO `roles_apps` VALUES (2, 6);
INSERT INTO `roles_apps` VALUES (3, 6);
INSERT INTO `roles_apps` VALUES (4, 6);
INSERT INTO `roles_apps` VALUES (5, 6);
INSERT INTO `roles_apps` VALUES (1, 7);
INSERT INTO `roles_apps` VALUES (2, 7);
INSERT INTO `roles_apps` VALUES (3, 7);
INSERT INTO `roles_apps` VALUES (4, 7);
INSERT INTO `roles_apps` VALUES (5, 7);
INSERT INTO `roles_apps` VALUES (1, 8);
INSERT INTO `roles_apps` VALUES (2, 8);
INSERT INTO `roles_apps` VALUES (3, 8);
INSERT INTO `roles_apps` VALUES (4, 8);
INSERT INTO `roles_apps` VALUES (5, 8);
INSERT INTO `roles_apps` VALUES (1, 9);
INSERT INTO `roles_apps` VALUES (2, 9);
INSERT INTO `roles_apps` VALUES (3, 9);
INSERT INTO `roles_apps` VALUES (4, 9);
INSERT INTO `roles_apps` VALUES (5, 9);
INSERT INTO `roles_apps` VALUES (1, 10);
INSERT INTO `roles_apps` VALUES (2, 10);
INSERT INTO `roles_apps` VALUES (3, 10);
INSERT INTO `roles_apps` VALUES (4, 10);
INSERT INTO `roles_apps` VALUES (5, 10);
INSERT INTO `roles_apps` VALUES (1, 11);
INSERT INTO `roles_apps` VALUES (2, 11);
INSERT INTO `roles_apps` VALUES (3, 11);
INSERT INTO `roles_apps` VALUES (4, 11);
INSERT INTO `roles_apps` VALUES (5, 11);
INSERT INTO `roles_apps` VALUES (1, 12);
INSERT INTO `roles_apps` VALUES (2, 12);
INSERT INTO `roles_apps` VALUES (3, 12);
INSERT INTO `roles_apps` VALUES (4, 12);
INSERT INTO `roles_apps` VALUES (5, 12);
INSERT INTO `roles_apps` VALUES (1, 13);
INSERT INTO `roles_apps` VALUES (3, 13);
INSERT INTO `roles_apps` VALUES (4, 13);
INSERT INTO `roles_apps` VALUES (5, 13);

-- ----------------------------
-- Table structure for roles_authorities
-- ----------------------------
DROP TABLE IF EXISTS `roles_authorities`;
CREATE TABLE `roles_authorities`  (
  `authority_id` bigint NOT NULL,
  `role_id` bigint NOT NULL,
  PRIMARY KEY (`authority_id`, `role_id`) USING BTREE,
  INDEX `FKq3iqpff34tgtkvnn545a648cb`(`role_id` ASC) USING BTREE,
  CONSTRAINT `FKq3iqpff34tgtkvnn545a648cb` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKt69njxcgfcto5wcrd9ocmb35h` FOREIGN KEY (`authority_id`) REFERENCES `authorities` (`authority_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of roles_authorities
-- ----------------------------
INSERT INTO `roles_authorities` VALUES (40, 1);
INSERT INTO `roles_authorities` VALUES (41, 1);
INSERT INTO `roles_authorities` VALUES (40, 2);
INSERT INTO `roles_authorities` VALUES (40, 3);

-- ----------------------------
-- Table structure for roles_modules
-- ----------------------------
DROP TABLE IF EXISTS `roles_modules`;
CREATE TABLE `roles_modules`  (
  `module_id` bigint NOT NULL,
  `role_id` bigint NOT NULL,
  PRIMARY KEY (`module_id`, `role_id`) USING BTREE,
  INDEX `FKr9nqei7m3lwoar7lx6ehgu1g0`(`role_id` ASC) USING BTREE,
  CONSTRAINT `FKkjaec95lv9ivgm8a9vyq3vdju` FOREIGN KEY (`module_id`) REFERENCES `modules` (`module_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKr9nqei7m3lwoar7lx6ehgu1g0` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of roles_modules
-- ----------------------------
INSERT INTO `roles_modules` VALUES (1, 1);
INSERT INTO `roles_modules` VALUES (2, 1);
INSERT INTO `roles_modules` VALUES (1, 2);
INSERT INTO `roles_modules` VALUES (1, 3);

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT,
  `key_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `sex` int NULL DEFAULT NULL,
  `del_flag` int NOT NULL DEFAULT 0,
  `creat_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creat_time` datetime NOT NULL,
  `update_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`user_id`) USING BTREE,
  UNIQUE INDEX `uk_key_id`(`key_id` ASC) USING BTREE,
  UNIQUE INDEX `UK_h8spruvwnd6d7p5diy8y08pre`(`key_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, 'KEY001', '张三', 'password123', '<EMAIL>', '13800138001', 1, 0, 'admin', '2023-01-01 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `users` VALUES (2, 'KEY002', '李四', 'password123', '<EMAIL>', '***********', 1, 0, 'admin', '2023-01-02 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `users` VALUES (3, 'KEY003', '王五', 'password123', '<EMAIL>', '***********', 1, 0, 'admin', '2023-01-01 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `users` VALUES (4, 'KEY004', '赵六', 'password123', '<EMAIL>', '13800138004', 1, 0, 'admin', '2023-01-04 10:00:00', 'admin', '2025-06-19 10:06:35');
INSERT INTO `users` VALUES (5, 'KEY005', '钱七', 'password123', '<EMAIL>', '13800138005', 1, 0, 'admin', '2023-01-05 10:00:00', 'admin', '2025-06-19 10:06:35');
INSERT INTO `users` VALUES (6, 'KEY006', '孙八', 'password123', '<EMAIL>', '13800138006', 1, 0, 'admin', '2023-01-06 10:00:00', 'admin', '2025-06-19 10:06:35');
INSERT INTO `users` VALUES (7, 'KEY007', '周九', 'password123', '<EMAIL>', '13800138007', 1, 0, 'admin', '2023-01-07 10:00:00', 'admin', '2025-06-19 10:06:35');
INSERT INTO `users` VALUES (8, 'KEY008', '吴十', 'password123', '<EMAIL>', '13800138008', 1, 0, 'admin', '2023-01-08 10:00:00', 'admin', '2025-06-19 10:06:35');
INSERT INTO `users` VALUES (9, 'KEY009', '郑十一', 'password123', '<EMAIL>', '13800138009', 1, 0, 'admin', '2023-01-09 10:00:00', 'admin', '2025-06-19 10:06:35');
INSERT INTO `users` VALUES (10, 'KEY010', '王十二', 'password123', '<EMAIL>', '13800138010', 1, 0, 'admin', '2023-01-10 10:00:00', 'admin', '2025-07-04 16:28:22');
INSERT INTO `users` VALUES (11, 'KEY011', '冯十三', 'password123', '<EMAIL>', '13800138011', 1, 0, 'admin', '2023-01-11 10:00:00', 'admin', '2025-07-01 16:58:13');
INSERT INTO `users` VALUES (12, 'KEY012', '陈十四', 'password123', '<EMAIL>', '13800138012', 1, 0, 'admin', '2023-01-12 10:00:00', 'admin', '2025-07-01 16:58:12');
INSERT INTO `users` VALUES (13, 'KEY013', '褚十五', 'password123', '<EMAIL>', '13800138013', 1, 0, 'admin', '2023-01-13 10:00:00', 'admin', '2025-07-01 16:58:12');

-- ----------------------------
-- Table structure for users_apps
-- ----------------------------
DROP TABLE IF EXISTS `users_apps`;
CREATE TABLE `users_apps`  (
  `app_id` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  PRIMARY KEY (`app_id`, `user_id`) USING BTREE,
  INDEX `FKi63gvk00ithreah9ep0ck9rpo`(`user_id` ASC) USING BTREE,
  CONSTRAINT `FK5vx98fr8e5s2folb84rshi2we` FOREIGN KEY (`app_id`) REFERENCES `apps` (`app_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKi63gvk00ithreah9ep0ck9rpo` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users_apps
-- ----------------------------
INSERT INTO `users_apps` VALUES (1, 1);
INSERT INTO `users_apps` VALUES (2, 1);
INSERT INTO `users_apps` VALUES (3, 1);
INSERT INTO `users_apps` VALUES (4, 1);
INSERT INTO `users_apps` VALUES (5, 1);
INSERT INTO `users_apps` VALUES (1, 2);
INSERT INTO `users_apps` VALUES (2, 2);
INSERT INTO `users_apps` VALUES (3, 2);
INSERT INTO `users_apps` VALUES (4, 2);
INSERT INTO `users_apps` VALUES (5, 2);
INSERT INTO `users_apps` VALUES (1, 3);
INSERT INTO `users_apps` VALUES (2, 3);
INSERT INTO `users_apps` VALUES (3, 3);
INSERT INTO `users_apps` VALUES (4, 3);
INSERT INTO `users_apps` VALUES (5, 3);
INSERT INTO `users_apps` VALUES (1, 4);
INSERT INTO `users_apps` VALUES (2, 4);
INSERT INTO `users_apps` VALUES (3, 4);
INSERT INTO `users_apps` VALUES (4, 4);
INSERT INTO `users_apps` VALUES (5, 4);
INSERT INTO `users_apps` VALUES (1, 5);
INSERT INTO `users_apps` VALUES (2, 5);
INSERT INTO `users_apps` VALUES (3, 5);
INSERT INTO `users_apps` VALUES (4, 5);
INSERT INTO `users_apps` VALUES (5, 5);
INSERT INTO `users_apps` VALUES (1, 6);
INSERT INTO `users_apps` VALUES (2, 6);
INSERT INTO `users_apps` VALUES (3, 6);
INSERT INTO `users_apps` VALUES (4, 6);
INSERT INTO `users_apps` VALUES (5, 6);
INSERT INTO `users_apps` VALUES (1, 7);
INSERT INTO `users_apps` VALUES (2, 7);
INSERT INTO `users_apps` VALUES (3, 7);
INSERT INTO `users_apps` VALUES (4, 7);
INSERT INTO `users_apps` VALUES (5, 7);
INSERT INTO `users_apps` VALUES (1, 8);
INSERT INTO `users_apps` VALUES (2, 8);
INSERT INTO `users_apps` VALUES (3, 8);
INSERT INTO `users_apps` VALUES (4, 8);
INSERT INTO `users_apps` VALUES (5, 8);
INSERT INTO `users_apps` VALUES (1, 9);
INSERT INTO `users_apps` VALUES (2, 9);
INSERT INTO `users_apps` VALUES (3, 9);
INSERT INTO `users_apps` VALUES (4, 9);
INSERT INTO `users_apps` VALUES (5, 9);
INSERT INTO `users_apps` VALUES (1, 10);
INSERT INTO `users_apps` VALUES (2, 10);
INSERT INTO `users_apps` VALUES (3, 10);
INSERT INTO `users_apps` VALUES (4, 10);
INSERT INTO `users_apps` VALUES (5, 10);
INSERT INTO `users_apps` VALUES (1, 11);
INSERT INTO `users_apps` VALUES (2, 11);
INSERT INTO `users_apps` VALUES (3, 11);
INSERT INTO `users_apps` VALUES (4, 11);
INSERT INTO `users_apps` VALUES (5, 11);
INSERT INTO `users_apps` VALUES (1, 12);
INSERT INTO `users_apps` VALUES (2, 12);
INSERT INTO `users_apps` VALUES (3, 12);
INSERT INTO `users_apps` VALUES (4, 12);
INSERT INTO `users_apps` VALUES (5, 12);
INSERT INTO `users_apps` VALUES (1, 13);
INSERT INTO `users_apps` VALUES (3, 13);
INSERT INTO `users_apps` VALUES (4, 13);
INSERT INTO `users_apps` VALUES (5, 13);

-- ----------------------------
-- Table structure for users_departments
-- ----------------------------
DROP TABLE IF EXISTS `users_departments`;
CREATE TABLE `users_departments`  (
  `department_id` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  PRIMARY KEY (`department_id`, `user_id`) USING BTREE,
  INDEX `FKio53q4jjhrkjlytn95cujumwv`(`user_id` ASC) USING BTREE,
  CONSTRAINT `FKio53q4jjhrkjlytn95cujumwv` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKqkbma75l4m40ma0d8wsgsw8qp` FOREIGN KEY (`department_id`) REFERENCES `departments` (`department_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users_departments
-- ----------------------------

-- ----------------------------
-- Table structure for users_modules
-- ----------------------------
DROP TABLE IF EXISTS `users_modules`;
CREATE TABLE `users_modules`  (
  `user_id` bigint NOT NULL,
  `module_id` bigint NOT NULL,
  PRIMARY KEY (`user_id`, `module_id`) USING BTREE,
  INDEX `fk_module_id`(`module_id` ASC) USING BTREE,
  CONSTRAINT `fk_module_id` FOREIGN KEY (`module_id`) REFERENCES `modules` (`module_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_user_module_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of users_modules
-- ----------------------------
INSERT INTO `users_modules` VALUES (1, 1);
INSERT INTO `users_modules` VALUES (2, 2);
INSERT INTO `users_modules` VALUES (3, 3);
INSERT INTO `users_modules` VALUES (4, 4);
INSERT INTO `users_modules` VALUES (5, 5);
INSERT INTO `users_modules` VALUES (6, 6);
INSERT INTO `users_modules` VALUES (7, 7);
INSERT INTO `users_modules` VALUES (8, 8);
INSERT INTO `users_modules` VALUES (9, 9);
INSERT INTO `users_modules` VALUES (10, 10);
INSERT INTO `users_modules` VALUES (11, 11);
INSERT INTO `users_modules` VALUES (12, 12);
INSERT INTO `users_modules` VALUES (13, 13);

-- ----------------------------
-- Table structure for users_roles
-- ----------------------------
DROP TABLE IF EXISTS `users_roles`;
CREATE TABLE `users_roles`  (
  `role_id` bigint NOT NULL,
  `user_id` bigint NOT NULL,
  PRIMARY KEY (`role_id`, `user_id`) USING BTREE,
  INDEX `FK2o0jvgh89lemvvo17cbqvdxaa`(`user_id` ASC) USING BTREE,
  CONSTRAINT `FK2o0jvgh89lemvvo17cbqvdxaa` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKj6m8fwv7oqv74fcehir1a9ffy` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of users_roles
-- ----------------------------
INSERT INTO `users_roles` VALUES (1, 1);
INSERT INTO `users_roles` VALUES (1, 2);
INSERT INTO `users_roles` VALUES (3, 2);
INSERT INTO `users_roles` VALUES (1, 3);

SET FOREIGN_KEY_CHECKS = 1;
