# mysql configuration
spring.application.name=PM-Server
spring.datasource.url=***************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=qwer1234
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.jpa.hibernate.ddl-auto=none
spring.jpa.show-sql=true

management.health.elasticsearch.enabled=false

# Open IPI
springdoc.api-docs.path=/api-docs
springdoc.swagger-ui.path=/swagger-ui.html
springdoc.swagger-ui.enabled=true
springdoc.packagesToScan=cn.ac.iie.pmserver.controller
springdoc.swagger-ui.url=/api-docs
springdoc.swagger-ui.config-url=/api-docs/swagger-config
springdoc.cache.disabled=true
springdoc.use-fqn=true

# SLF4j
logging.level.root=info
logging.file.name=./logs/application.log
logging.logback.rollingpolicy.max-file-size=10MB
logging.logback.rollingpolicy.max-history=30
logging.logback.rollingpolicy.file-name-pattern=${LOG_FILE}.%d{yyyy-MM-dd}.%i.gz
logging.logback.rollingpolicy.total-size-cap=1GB
logging.logback.rollingpolicy.clean-history-on-start=false
logging.pattern.file=%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n
logging.pattern.level=%5p
logging.console.enabled=true
logging.charset.console=UTF-8
logging.charset.file=UTF-8
logging.logback.rollingpolicy.buffer-size=8KB
logging.pattern.console=%clr(%d{yyyy-MM-dd HH:mm:ss.SSS}){yellow} %clr([%thread]){blue} %clr(%-5level){green} %clr(%logger{36}){cyan} - %clr(%msg){faint}%n

# Static resource
app.upload.dir=D:/cyh/workspace/pm-server/src/main/resources/static/upload
#app.upload.dir=/app/images/app
app.upload.allowed-types=image/jpeg,image/png,image/jpg
app.upload.max-size=2097152

spring.servlet.multipart.max-file-size=2MB
spring.servlet.multipart.max-request-size=2MB