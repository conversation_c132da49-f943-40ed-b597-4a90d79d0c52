/*
 Navicat Premium Dump SQL

 Source Server         : **********
 Source Server Type    : MySQL
 Source Server Version : 80404 (8.4.4)
 Source Host           : **********:23306
 Source Schema         : qxdb

 Target Server Type    : MySQL
 Target Server Version : 80404 (8.4.4)
 File Encoding         : 65001

 Date: 21/07/2025 09:45:55
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for apps
-- ----------------------------
DROP TABLE IF EXISTS `apps`;
CREATE TABLE `apps`  (
  `app_id` bigint NOT NULL AUTO_INCREMENT,
  `app_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `app_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `app_description` varchar(1024) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `app_callback_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `app_logo` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `del_flag` int NOT NULL DEFAULT 0,
  `alive_flag` int NOT NULL DEFAULT 0,
  `point_user_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `creat_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creat_time` datetime NOT NULL,
  `update_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`app_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 38 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of apps
-- ----------------------------
INSERT INTO `apps` VALUES (1, 'OA系统', 'https://www.baidu.com', 'OA 系统是整合流程审批、文档管理与协同办公的数字化平台，提升企业运营效率', 'https://oa.example.com/callback', 'https://tse4-mm.cn.bing.net/th/id/OIP-C.OZEWv-DlclJI9AFeFEDDjgHaE8?rs=1&pid=ImgDetMain', 0, 0, '13800138005', 'admin', '2025-05-10 10:00:00', '', '2025-06-27 21:29:28');
INSERT INTO `apps` VALUES (2, 'CRM系统', 'https://crm.example.com', '用于管理企业与客户交互的数字化平台，集成客户数据管理、销售流程跟踪、营销自动化、客户服务支持等功能，助力企业优化客户关系，提升销售转化与客户忠诚度', 'https://crm.example.com/callback', '/api/files/appLogo_76c3a76f-f620-4db9-858f-0e9bda4c7ba5.png', 0, 0, '13800138003', 'admin', '2025-06-10 10:00:00', 'admin', '2025-07-09 10:40:59');
INSERT INTO `apps` VALUES (3, 'ERP系统', 'http://erp.example.com', '整合企业财务、采购、生产、库存、人力资源等核心业务流程的集成化管理平台，通过数据共享与流程自动化实现资源优化配置，提升企业运营效率与决策能力', 'http://erp.example.com/callback', '', 0, 0, '13800138003', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `apps` VALUES (4, 'HR系统', 'http://hr.example.com', '聚焦人力资源管理的数字化平台，集成员工档案、招聘管理、考勤绩效、培训发展与薪资福利等功能，助力企业实现人才全生命周期管理与组织效能提升', 'http://hr.example.com/callback', '', 0, 0, '13800138003', 'admin', '2025-06-10 10:00:00', 'admin', '2025-06-27 18:01:58');
INSERT INTO `apps` VALUES (5, 'SCM系统', 'http://scm.example.com', '管理企业供应链全流程的数字化平台，集成采购管理、库存控制、物流跟踪、供应商协作等功能，通过优化供应链效率实现成本降低与交付能力提升', 'http://scm.example.com/callback', '', 0, 0, '13800138005', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-05 10:00:00');
INSERT INTO `apps` VALUES (6, 'BI系统', 'http://bi.example.com', '通过数据采集、分析与可视化工具，整合企业内外部数据，提供报表生成、多维分析、预测建模等功能，助力管理者基于数据洞察驱动业务决策的智能平台', 'http://bi.example.com/callback', '', 0, 0, '13800138006', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-06 10:00:00');
INSERT INTO `apps` VALUES (7, 'CMS系统', 'http://cms.example.com', '用于创建、管理、发布数字内容的平台，集成内容编辑、页面设计、权限控制、工作流管理等功能，支持多终端内容分发与用户互动，助力企业高效运营线上内容资源', 'http://cms.example.com/callback', '', 0, 0, '13800138007', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-07 10:00:00');
INSERT INTO `apps` VALUES (8, 'EAM系统', 'http://eam.example.com', '聚焦企业资产全生命周期管理的数字化平台，集成资产登记、运维保养、检修调度、报废处置等功能，通过智能化管理提升资产利用率与运维效率', 'http://eam.example.com/callback', '', 0, 0, '13800138008', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-08 10:00:00');
INSERT INTO `apps` VALUES (9, 'MES系统', 'http://mes.example.com', '衔接企业计划层与生产控制层的智能制造平台，集成生产排程、工艺执行、设备监控、质量追溯等功能，实时优化生产流程并提升车间数字化管理水平', 'http://mes.example.com/callback', '', 0, 0, '13800138009', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-09 10:00:00');
INSERT INTO `apps` VALUES (10, 'WMS系统', 'http://wms.example.com', '专注于仓库全流程管理的数字化平台，集成入库管理、出库作业、库存盘点、仓位分配等功能，通过智能化调度与可视化监控提升仓储效率与库存准确率', 'http://wms.example.com/callback', '', 0, 0, '13800138010', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-10 10:00:00');
INSERT INTO `apps` VALUES (11, 'TMS系统', 'http://tms.example.com', '聚焦企业运输全流程管理的数字化平台，集成运输计划制定、车辆调度、路径优化、在途监控、运费结算等功能，通过智能化管控提升物流效率与运输成本透明度', 'http://tms.example.com/callback', '', 0, 0, '13800138011', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-11 10:00:00');
INSERT INTO `apps` VALUES (12, 'PMS系统', 'http://pms.example.com', '通过智能化管控提升物业运营效率与服务质量，适用于商业楼宇、住宅小区、产业园区等多种场景的资产运营与服务管理', 'http://pms.example.com/callback', '', 0, 0, '***********', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-12 10:00:00');
INSERT INTO `apps` VALUES (13, 'LMS系统', 'http://lms.example.com', '整合课程设计、学习资源管理、进度追踪及互动功能的数字化平台，支持企业员工培训、学校在线教学等场景，通过智能化工具实现学习过程的高效管理与效果评估', 'http://lms.example.com/callback', '', 0, 0, '***********', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-13 10:00:00');
INSERT INTO `apps` VALUES (14, '百度网站', 'https://baidu.com', '百度是中国领先的互联网科技企业，核心作用在于通过搜索引擎技术为用户提供信息获取与服务连接的入口。作为全球最大的中文搜索引擎，它能对海量网页、图片、视频等信息进行抓取与索引，用户输入关键词即可快速获得相关内容，覆盖知识查询、新闻资讯、生活服务等场景，例如搜索“天气”可实时获取各地气象数据，搜索学术关键词能定位相关文献资料。 除搜索核心功能外，百度构建了多元化服务生态：通过百度地图提供精准导航与位置服务，助力用户规划出行路线；百度百科、百度知道等产品聚合UGC内容，形成开放式知识共享平台；百度糯米整合本地生活服务，连接餐饮、电影等消费场景。此外，其在人工智能领域持续投入，推出百度大脑、文心一言等AI技术与大模型产品，将技术能力赋能至智能驾驶（如Apollo）、智能云等业务，推动产业数字化升级。 百度的作用本质是“信息枢纽”与“技术赋能者”，既解决用户信息获取的效率问题，也通过技术创新为个人与企业提供数字化工具，在信息传播、生活服务、产业升级等层面发挥着广泛影响。百度是中国领先的互联网科技企业，核心作用在于通过搜索引擎技术为用户提供信息获取与服务连接的入口。作为全球最大的中文搜索引擎，它能对海量网页、图片、视频等信息进行抓取与索引，用户输入关键词即可快速获得相关内容，覆盖知识查询、新闻资讯、生活服务等场景，例如搜索“天气”可实时获取各地气象数据，搜索学术关键词能定位相关文献资料。 除搜索核心功能外，百度构建了多元化服务生态：通过百度地图提供精准导航与位置服务，助力用户规划出行路线；百度百科、百度知道等产品聚合UGC内容，形成开放式知识共享平台；百度糯米整合本地生活服务，连接餐饮、电影等消费场景。此外，其在人工智能领域持续投入，推出百度大脑、文心一言等AI技术与大模型产品，将技术能力赋能至智能驾驶（如Apollo）、智能云等业务，推动产业数字化升级。 百度的作用本质是“信息枢纽”与“技术赋能者”，既解决用户信息获取的效率问题，也通过技术创新为个人与企业提供数字化工具，在信息传播、生活服务、产业升级等层面发挥着广泛影响。百度是中国领先的互联网科技企业，核心作用在于通过搜索引擎技术为用户提供信息获取与服务连接的入口。作为全球最大的中文搜索引擎，它能对海量网页、图片、视频等信息进行抓取与索引，用户输入关键词即可快速获得相关内容，覆盖知识查询、新闻资讯、生活服务等场景，例如', 'https://baidu.com/callback', '/api/files/images/appLogo_8088c94b-faa2-4683-9d28-98399b1766da.png', 0, 0, '18888888888', 'AA', '2025-06-11 11:11:49', 'AA', '2025-07-18 19:38:33');
INSERT INTO `apps` VALUES (15, 'OA系统', 'http://oa.example.com', 'OA 系统是整合流程审批、文档管理与协同办公的数字化平台，提升企业运营效率', 'http://oa.example.com/callback', 'https://tse4-mm.cn.bing.net/th/id/OIP-C.OZEWv-DlclJI9AFeFEDDjgHaE8?rs=1&pid=ImgDetMain', 0, 1, '13800138001', 'admin', '2025-05-10 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `apps` VALUES (16, 'CRM系统', 'http://crm.example.com', '用于管理企业与客户交互的数字化平台，集成客户数据管理、销售流程跟踪、营销自动化、客户服务支持等功能，助力企业优化客户关系，提升销售转化与客户忠诚度', 'http://crm.example.com/callback', '', 0, 1, '13800138002', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `apps` VALUES (17, 'ERP系统', 'http://erp.example.com', '整合企业财务、采购、生产、库存、人力资源等核心业务流程的集成化管理平台，通过数据共享与流程自动化实现资源优化配置，提升企业运营效率与决策能力', 'http://erp.example.com/callback', '', 0, 0, '13800138003', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `apps` VALUES (18, 'BI系统', 'http://bi.example.com', '通过数据采集、分析与可视化工具，整合企业内外部数据，提供报表生成、多维分析、预测建模等功能，助力管理者基于数据洞察驱动业务决策的智能平台', 'http://bi.example.com/callback', '', 0, 0, '13800138006', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-06 10:00:00');
INSERT INTO `apps` VALUES (19, 'MES系统', 'http://mes.example.com', '衔接企业计划层与生产控制层的智能制造平台，集成生产排程、工艺执行、设备监控、质量追溯等功能，实时优化生产流程并提升车间数字化管理水平', 'http://mes.example.com/callback', '', 0, 0, '13800138009', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-09 10:00:00');
INSERT INTO `apps` VALUES (20, 'LMS系统', 'http://lms.example.com', '整合课程设计、学习资源管理、进度追踪及互动功能的数字化平台，支持企业员工培训、学校在线教学等场景，通过智能化工具实现学习过程的高效管理与效果评估', 'http://lms.example.com/callback', '', 0, 0, '***********', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-13 10:00:00');
INSERT INTO `apps` VALUES (21, 'TMS系统', 'http://tms.example.com', '聚焦企业运输全流程管理的数字化平台，集成运输计划制定、车辆调度、路径优化、在途监控、运费结算等功能，通过智能化管控提升物流效率与运输成本透明度', 'http://tms.example.com/callback', '', 0, 0, '13800138011', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-11 10:00:00');
INSERT INTO `apps` VALUES (22, 'PMS系统', 'http://pms.example.com', '通过智能化管控提升物业运营效率与服务质量，适用于商业楼宇、住宅小区、产业园区等多种场景的资产运营与服务管理', 'http://pms.example.com/callback', '', 0, 0, '***********', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-12 10:00:00');
INSERT INTO `apps` VALUES (23, 'LMS系统', 'http://lms.example.com', '整合课程设计、学习资源管理、进度追踪及互动功能的数字化平台，支持企业员工培训、学校在线教学等场景，通过智能化工具实现学习过程的高效管理与效果评估', 'http://lms.example.com/callback', '', 0, 0, '***********', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-13 10:00:00');
INSERT INTO `apps` VALUES (24, 'AA', 'AA', 'AA', 'AA', 'AA', 0, 0, '18888888888', 'AA', '2025-06-11 11:11:49', 'AA', '2025-06-11 11:11:54');
INSERT INTO `apps` VALUES (25, 'OA系统', 'http://oa.example.com', 'OA 系统是整合流程审批、文档管理与协同办公的数字化平台，提升企业运营效率', 'http://oa.example.com/callback', 'https://tse4-mm.cn.bing.net/th/id/OIP-C.OZEWv-DlclJI9AFeFEDDjgHaE8?rs=1&pid=ImgDetMain', 0, 1, '13800138001', 'admin', '2025-05-10 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `apps` VALUES (26, 'CRM系统', 'http://crm.example.com', '用于管理企业与客户交互的数字化平台，集成客户数据管理、销售流程跟踪、营销自动化、客户服务支持等功能，助力企业优化客户关系，提升销售转化与客户忠诚度', 'http://crm.example.com/callback', '', 0, 1, '13800138002', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `apps` VALUES (27, 'ERP系统', 'http://erp.example.com', '整合企业财务、采购、生产、库存、人力资源等核心业务流程的集成化管理平台，通过数据共享与流程自动化实现资源优化配置，提升企业运营效率与决策能力', 'http://erp.example.com/callback', '', 0, 0, '13800138003', 'admin', '2025-06-10 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `apps` VALUES (28, 'PM系统', 'http://www.baidu.com', '管理企业供应链全流程的数字化平台，集成采购管理、库存控制、物流跟踪、供应商协作等功能，通过优化供应链效率实现成本降低与交付能力提升', 'http://www.baidu.com', 'https://tse4-mm.cn.bing.net/th/id/OIP-C.OZEWv-DlclJI9AFeFEDDjgHaE8?rs=1&pid=ImgDetMain', 0, 1, '18811112222', '李四', '2025-06-12 17:21:18', NULL, NULL);
INSERT INTO `apps` VALUES (29, 'QXGL', 'https://www.baidu.com', 'test', 'https://www.baidu.com', '', 0, 0, '13677777777', '111', '2025-06-13 15:27:11', NULL, NULL);
INSERT INTO `apps` VALUES (30, '百度', 'https://baidu.com', '百度是中国领先的互联网科技企业，核心作用在于通过搜索引擎技术为用户提供信息获取与服务连接的入口。作为全球最大的中文搜索引擎，它能对海量网页、图片、视频等信息进行抓取与索引，用户输入关键词即可快速获得相关内容，覆盖知识查询、新闻资讯、生活服务等场景，例如搜索“天气”可实时获取各地气象数据，搜索学术关键词能定位相关文献资料。\n除搜索核心功能外，百度构建了多元化服务生态：通过百度地图提供精准导航与位置服务，助力用户规划出行路线；百度百科、百度知道等产品聚合UGC内容，形成开放式知识共享平台；百度糯米整合本地生活服务，连接餐饮、电影等消费场景。此外，其在人工智能领域持续投入，推出百度大脑、文心一言等AI技术与大模型产品，将技术能力赋能至智能驾驶（如Apollo）、智能云等业务，推动产业数字化升级。\n百度的作用本质是“信息枢纽”与“技术赋能者”，既解决用户信息获取的效率问题，也通过技术创新为个人与企业提供数字化工具，在信息传播、生活服务、产业升级等层面发挥着广泛影响。', 'https://baidu.com/callback', '', 0, 0, '13888888888', 'admin', '2025-06-17 15:02:41', NULL, NULL);
INSERT INTO `apps` VALUES (37, '百度网站', 'https://baidu.com', '百度是中国领先的互联网科技企业，核心作用在于通过搜索引擎技术为用户提供信息获取与服务连接的入口。作为全球最大的中文搜索引擎，它能对海量网页、图片、视频等信息进行抓取与索引，用户输入关键词即可快速获得相关内容，覆盖知识查询、新闻资讯、生活服务等场景，例如搜索“天气”可实时获取各地气象数据，搜索学术关键词能定位相关文献资料。\n\n除搜索核心功能外，百度构建了多元化服务生态：通过百度地图提供精准导航与位置服务，助力用户规划出行路线；百度百科、百度知道等产品聚合UGC内容，形成开放式知识共享平台；百度糯米整合本地生活服务，连接餐饮、电影等消费场景。此外，其在人工智能领域持续投入，推出百度大脑、文心一言等AI技术与大模型产品，将技术能力赋能至智能驾驶（如Apollo）、智能云等业务，推动产业数字化升级。\n\n百度的作用本质是“信息枢纽”与“技术赋能者”，既解决用户信息获取的效率问题，也通过技术创新为个人与企业提供数字化工具，在信息传播、生活服务、产业升级等层面发挥着广泛影响。百度是中国领先的互联网科技企业，核心作用在于通过搜索引擎技术为用户提供信息获取与服务连接的入口。作为全球最大的中文搜索引擎，它能对海量网页、图片、视频等信息进行抓取与索引，用户输入关键词即可快速获得相关内容，覆盖知识查询、新闻资讯、生活服务等场景，例如搜索“天气”可实时获取各地气象数据，搜索学术关键词能定位相关文献资料。\n\n除搜索核心功能外，百度构建了多元化服务生态：通过百度地图提供精准导航与位置服务，助力用户规划出行路线；百度百科、百度知道等产品聚合UGC内容，形成开放式知识共享平台；百度糯米整合本地生活服务，连接餐饮、电影等消费场景。此外，其在人工智能领域持续投入，推出百度大脑、文心一言等AI技术与大模型产品，将技术能力赋能至智能驾驶（如Apollo）、智能云等业务，推动产业数字化升级。\n\n百度的作用本质是“信息枢纽”与“技术赋能者”，既解决用户信息获取的效率问题，也通过技术创新为个人与企业提供数字化工具，在信息传播、生活服务、产业升级等层面发挥着广泛影响。百度是中国领先的互联网科技企业，核心作用在于通过搜索引擎技术为用户提供信息获取与服务连接的入口。作为全球最大的中文搜索引擎，它能对海量网页、图片、视频等信息进行抓取与索引，用户输入关键词即可快速获得相关内容，覆盖知识查询、新闻资讯、生活服务等场景，例如', 'https://baidu.com/callback', '/api/files/images/appLogo_06c1d54b-66ad-4cfd-aba9-b899829a6c57.png', 0, 0, '19699999999', 'admin', '2025-07-15 18:46:53', NULL, NULL);

-- ----------------------------
-- Table structure for authorities
-- ----------------------------
DROP TABLE IF EXISTS `authorities`;
CREATE TABLE `authorities`  (
  `authority_id` bigint NOT NULL AUTO_INCREMENT,
  `authority_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `authority_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `actions` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `creat_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creat_time` datetime NOT NULL,
  `update_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `authority_code` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `level` int NULL DEFAULT NULL,
  `parent_id` bigint NULL DEFAULT NULL,
  `resource_paths` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `resource_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `sort_order` int NULL DEFAULT NULL,
  `sync_source` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `sync_status` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `sync_time` datetime(6) NULL DEFAULT NULL,
  PRIMARY KEY (`authority_id`) USING BTREE,
  UNIQUE INDEX `UK_julcc12xaia89a2et2gbeat0l`(`authority_code` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 51 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of authorities
-- ----------------------------
INSERT INTO `authorities` VALUES (14, '用户中心管理', '用户中心系统管理根权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'USER_CENTER_MANAGE', 1, NULL, '[\"/user-center\"]', 'menu', 1, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (15, '用户管理', '用户信息管理权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'USER_MANAGE', 2, 14, '[\"/user\"]', 'menu', 1, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (16, '用户列表', '用户列表查看权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'USER_LIST', 3, 15, '[\"/user/list\"]', 'menu', 1, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (17, '用户新增', '用户新增权限', '[\"create\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'USER_CREATE', 3, 16, '[\"/user/add\"]', 'button', 2, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (18, '用户编辑', '用户编辑权限', '[\"update\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'USER_UPDATE', 3, 17, '[\"/user/edit\"]', 'button', 3, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (19, '用户删除', '用户删除权限', '[\"delete\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'USER_DELETE', 3, 2, '[\"/user/delete\"]', 'button', 4, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (20, '角色管理', '角色信息管理权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ROLE_MANAGE', 2, 1, '[\"/role\"]', 'menu', 2, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (21, '角色列表', '角色列表查看权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ROLE_LIST', 3, 7, '[\"/role/list\"]', 'menu', 1, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (22, '角色新增', '角色新增权限', '[\"create\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ROLE_CREATE', 3, 7, '[\"/role/add\"]', 'module', 2, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (23, '角色编辑', '角色编辑权限', '[\"update\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ROLE_UPDATE', 3, 7, '[\"/role/edit\"]', 'module', 3, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (24, '订单系统管理', '订单系统管理根权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ORDER_SYSTEM_MANAGE', 1, NULL, '[\"/order-system\"]', 'menu', 2, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (25, '订单管理', '订单信息管理权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ORDER_MANAGE', 2, 11, '[\"/order\"]', 'menu', 1, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (26, '订单列表', '订单列表查看权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ORDER_LIST', 3, 12, '[\"/order/list\"]', 'menu', 1, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (27, '订单详情', '订单详情查看权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ORDER_DETAIL', 3, 12, '[\"/order/detail\"]', 'menu', 2, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (28, '订单创建', '订单创建权限', '[\"create\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ORDER_CREATE', 3, 12, '[\"/order/create\"]', 'module', 3, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (29, '订单取消', '订单取消权限', '[\"update\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ORDER_CANCEL', 3, 12, '[\"/order/cancel\"]', 'module', 4, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (30, '商品管理', '商品信息管理权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'PRODUCT_MANAGE', 2, 11, '[\"/product\"]', 'menu', 2, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (31, '商品列表', '商品列表查看权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'PRODUCT_LIST', 3, 17, '[\"/product/list\"]', 'menu', 1, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (32, '商品新增', '商品新增权限', '[\"create\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'PRODUCT_CREATE', 3, 17, '[\"/product/add\"]', 'module', 2, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (33, '财务系统管理', '财务系统管理根权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'FINANCE_SYSTEM_MANAGE', 1, NULL, '[\"/finance-system\"]', 'menu', 3, 'FINANCE_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (34, '财务报表', '财务报表管理权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'FINANCE_REPORT', 2, 20, '[\"/report\"]', 'menu', 1, 'FINANCE_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (35, '收入报表', '收入报表查看权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'INCOME_REPORT', 3, 21, '[\"/report/income\"]', 'menu', 1, 'FINANCE_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (36, '支出报表', '支出报表查看权限', '[\"read\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'EXPENSE_REPORT', 3, 21, '[\"/report/expense\"]', 'menu', 2, 'FINANCE_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (37, '用户API', '用户相关API权限', '[\"read\", \"create\", \"update\", \"delete\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'USER_API', 3, 2, '[\"/api/users/**\"]', 'module', 5, 'USER_CENTER', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (38, '订单API', '订单相关API权限', '[\"read\", \"create\", \"update\", \"delete\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'ORDER_API', 3, 12, '[\"/api/orders/**\"]', 'module', 5, 'ORDER_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (39, '财务API', '财务相关API权限', '[\"read\", \"create\", \"update\"]', 'admin', '2025-07-07 02:28:41', 'admin', '2025-07-07 02:28:41', 'FINANCE_API', 3, 21, '[\"/api/finance/**\"]', 'module', 3, 'FINANCE_SYSTEM', 'SUCCESS', '2025-07-07 02:28:41.000000');
INSERT INTO `authorities` VALUES (40, 'Test Permission', 'Test permission via API', '[\"read\", \"create\"]', NULL, '2025-07-07 11:40:48', NULL, '2025-07-07 11:40:48', 'TEST_API_001', 1, NULL, '[\"/test-api\"]', 'module', 1, 'API_TEST', 'SUCCESS', '2025-07-07 11:40:48.015702');
INSERT INTO `authorities` VALUES (41, 'Batch Permission 1', 'First batch permission', '[\"read\"]', NULL, '2025-07-07 11:41:59', NULL, '2025-07-07 11:41:59', 'BATCH_001', 1, NULL, '[\"/batch1\"]', 'menu', 1, 'API_TEST', 'SUCCESS', '2025-07-07 11:41:59.155447');
INSERT INTO `authorities` VALUES (42, 'Batch Permission 2', 'Second batch permission', '[\"read\", \"update\"]', NULL, '2025-07-07 11:41:59', NULL, '2025-07-07 11:41:59', 'BATCH_002', 1, NULL, '[\"/batch2\"]', 'module', 2, 'API_TEST', 'SUCCESS', '2025-07-07 11:41:59.171314');
INSERT INTO `authorities` VALUES (43, '应用信息管理', '应用信息管理权限', '[\"read\", \"create\", \"update\", \"delete\"]', NULL, '2025-07-07 11:46:08', NULL, '2025-07-07 11:46:08', '_MANAGE', 1, NULL, '[\"/app\", \"/app/list\"]', 'menu', 1, 'APP_CENTER', 'SUCCESS', '2025-07-07 11:46:07.911398');
INSERT INTO `authorities` VALUES (44, '应用信息管理', '应用信息管理权限', '[\"read\", \"create\", \"update\", \"delete\"]', NULL, '2025-07-08 10:31:32', NULL, '2025-07-08 10:31:32', 'APP_MANAGE', 1, NULL, '[\"/app\", \"/app/list\"]', 'menu', 1, 'APP_CENTER', 'SUCCESS', '2025-07-08 10:31:32.449180');
INSERT INTO `authorities` VALUES (45, 'User Query', 'Query user information', '[\"read\"]', NULL, '2025-07-08 10:51:51', NULL, '2025-07-08 10:51:51', 'USER_QUERY_NEW', 1, NULL, '[\"/user/query\"]', 'menu', 1, 'USER_CENTER', 'SUCCESS', '2025-07-08 10:51:50.973079');
INSERT INTO `authorities` VALUES (46, 'User Create', 'Create new user', '[\"create\"]', NULL, '2025-07-08 10:51:51', NULL, '2025-07-08 10:51:51', 'USER_CREATE_NEW', 1, NULL, '[\"/user/create\"]', 'module', 2, 'USER_CENTER', 'SUCCESS', '2025-07-08 10:51:50.991085');
INSERT INTO `authorities` VALUES (47, '用户查询', '查询用户信息的权限', '[\"read\"]', NULL, '2025-07-08 10:55:06', NULL, '2025-07-08 10:55:06', 'USER_QUERY', 1, NULL, '[\"/user/query\", \"/user/search\"]', 'menu', 1, 'USER_CENTER', 'SUCCESS', '2025-07-08 10:55:06.200918');
INSERT INTO `authorities` VALUES (48, '用户编辑', '编辑用户信息的权限', '[\"update\"]', NULL, '2025-07-08 10:55:06', NULL, '2025-07-08 10:55:06', 'USER_EDIT', 2, 1, '[\"/user/edit\", \"/user/update\"]', 'module', 2, 'USER_CENTER', 'SUCCESS', '2025-07-08 10:55:06.209922');
INSERT INTO `authorities` VALUES (49, '用户导出', '导出用户数据的权限', '[\"read\", \"export\"]', NULL, '2025-07-08 10:55:06', NULL, '2025-07-08 10:55:06', 'USER_EXPORT', 2, 1, '[\"/user/export\"]', 'module', 4, 'USER_CENTER', 'SUCCESS', '2025-07-08 10:55:06.215926');
INSERT INTO `authorities` VALUES (50, '应用信息管理', '应用信息管理权限', '[\"read\", \"create\", \"update\", \"delete\"]', NULL, '2025-07-16 14:24:37', NULL, '2025-07-16 14:24:38', 'APP_MANAGE1', 1, NULL, '[\"/app\", \"/app/list\"]', 'menu', 1, 'APP_CENTER', 'SUCCESS', '2025-07-16 14:24:37.723296');

-- ----------------------------
-- Table structure for departments
-- ----------------------------
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments`  (
  `department_id` bigint NOT NULL AUTO_INCREMENT,
  `department_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `department_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `department_head` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `department_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `del_flag` int NULL DEFAULT 0,
  `level` int NULL DEFAULT NULL COMMENT '部门层级，1表示一级部门，2表示二级部门等',
  `parent_id` bigint NULL DEFAULT NULL COMMENT '父部门ID，顶级部门为null',
  `order_num` int NULL DEFAULT NULL COMMENT '同级部门排序号',
  `path` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL COMMENT '部门路径，如\"0,1,2\"表示该部门的父路径',
  `has_children` tinyint(1) NULL DEFAULT 0 COMMENT '是否有子部门',
  `creat_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creat_time` datetime NULL DEFAULT NULL,
  `update_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`department_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 81 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of departments
-- ----------------------------
INSERT INTO `departments` VALUES (1, 'QXGL_DEPARTMENT1', '信息部', '张三', '负责技术研发', 'A栋1楼', 0, 1, NULL, 0, '0', 1, 'admin', '2023-01-01 10:00:00', 'admin', '2025-07-18 16:15:56');
INSERT INTO `departments` VALUES (2, 'QXGL_DEPARTMENT2', '财务部', '李四', '财务管理', 'A栋2楼', 0, 1, NULL, 10, '0', 1, 'admin', '2023-01-01 10:00:00', 'admin', '2025-07-18 16:15:56');
INSERT INTO `departments` VALUES (3, 'QXGL_DEPARTMENT3', '人事部', '王五', '人事管理', 'A栋3楼', 0, 1, NULL, 9, '0', 1, 'admin', '2023-01-01 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `departments` VALUES (4, 'QXGL_DEPARTMENT4', '市场部', '赵六', '市场营销', 'B栋1楼', 0, 1, NULL, 7, '0', 0, 'admin', '2023-01-04 10:00:00', 'admin', '2025-07-17 10:37:34');
INSERT INTO `departments` VALUES (5, 'QXGL_DEPARTMENT5', '销售部', '钱七', '产品销售', 'B栋2楼', 0, 1, NULL, 1, '0,5', 0, 'admin', '2023-01-05 10:00:00', 'admin', '2023-01-05 10:00:00');
INSERT INTO `departments` VALUES (6, 'QXGL_DEPARTMENT6', '客服部', '孙八', '客户服务', 'B栋3楼', 0, 1, NULL, 11, '0', 1, 'admin', '2023-01-06 10:00:00', 'admin', '2025-07-15 10:05:07');
INSERT INTO `departments` VALUES (7, 'QXGL_DEPARTMENT7', '采购部', '周九', '物资采购', 'C栋1楼', 0, 1, NULL, 3, '0', 0, 'admin', '2023-01-07 10:00:00', 'admin', '2025-06-19 17:45:38');
INSERT INTO `departments` VALUES (8, 'QXGL_DEPARTMENT8', '仓储部', '吴十', '仓储管理', 'C栋2楼', 0, 1, NULL, 8, '0', 0, 'admin', '2023-01-08 10:00:00', 'admin', '2023-01-08 10:00:00');
INSERT INTO `departments` VALUES (9, 'QXGL_DEPARTMENT9', '质检部', '郑十一', '质量检测', 'C栋3楼', 0, 1, NULL, 12, '0', 1, 'admin', '2023-01-09 10:00:00', 'admin', '2023-01-09 10:00:00');
INSERT INTO `departments` VALUES (10, 'QXGL_DEPARTMENT10', '研发部', '王十二', '产品研发', 'D栋1楼', 0, 1, NULL, 6, '0', 1, 'admin', '2023-01-10 10:00:00', 'admin', '2025-06-20 09:07:09');
INSERT INTO `departments` VALUES (11, 'QXGL_DEPARTMENT11', '设计部', '冯十三', '产品设计', 'D栋2楼', 0, 1, NULL, 14, '0', 1, 'admin', '2023-01-11 10:00:00', 'admin', '2023-01-11 10:00:00');
INSERT INTO `departments` VALUES (12, 'QXGL_DEPARTMENT12', '法务部', '陈十四', '法律事务', 'D栋3楼', 0, 1, NULL, 13, '0', 0, 'admin', '2023-01-12 10:00:00', 'admin', '2025-06-18 09:59:35');
INSERT INTO `departments` VALUES (13, 'QXGL_DEPARTMENT13', '行政部', '褚十五', '行政管理', 'E栋1楼', 0, 1, NULL, 5, '0', 0, 'admin', '2023-01-13 10:00:00', 'admin', '2023-01-13 10:00:00');
INSERT INTO `departments` VALUES (79, 'QXGL_DEPARTMENT14', '信息部', '默认负责人', '默认部门描述', '默认地址', 0, 1, NULL, 1, '0', 0, NULL, '2025-07-17 10:35:58', NULL, '2025-07-17 14:44:55');

-- ----------------------------
-- Table structure for departments_authorities
-- ----------------------------
DROP TABLE IF EXISTS `departments_authorities`;
CREATE TABLE `departments_authorities`  (
  `authority_id` bigint NOT NULL,
  `department_id` bigint NOT NULL,
  PRIMARY KEY (`authority_id`, `department_id`) USING BTREE,
  INDEX `FKpatbmec9yvl0ipgami6qsduf3`(`department_id` ASC) USING BTREE,
  CONSTRAINT `FKi6q23wgbp7h6m0x8megx7qrem` FOREIGN KEY (`authority_id`) REFERENCES `authorities` (`authority_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKpatbmec9yvl0ipgami6qsduf3` FOREIGN KEY (`department_id`) REFERENCES `departments` (`department_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of departments_authorities
-- ----------------------------

-- ----------------------------
-- Table structure for import_details
-- ----------------------------
DROP TABLE IF EXISTS `import_details`;
CREATE TABLE `import_details`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `record_id` bigint NOT NULL COMMENT '逻辑外键，关联import_records.id',
  `record_key` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户ID/部门编码等',
  `is_success` tinyint(1) NOT NULL COMMENT '0失败 1成功',
  `error_message` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL,
  `process_time` datetime NOT NULL,
  `operation_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_import_details_record_id`(`record_id` ASC) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 213 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of import_details
-- ----------------------------
INSERT INTO `import_details` VALUES (1, 1, '14', 0, '第2行数据错误: 应用编码不存在: QXGL', '2025-07-16 10:48:08', NULL);
INSERT INTO `import_details` VALUES (2, 2, '14', 0, '第2行数据错误: 应用编码不存在: QXGL', '2025-07-16 10:55:19', NULL);
INSERT INTO `import_details` VALUES (3, 3, '14', 0, '第2行数据错误: 应用编码不存在: 2', '2025-07-16 11:09:51', NULL);
INSERT INTO `import_details` VALUES (4, 4, '14', 0, '第2行数据错误: Query did not return a unique result: 3 results were returned', '2025-07-16 11:11:53', NULL);
INSERT INTO `import_details` VALUES (5, 7, '14', 1, NULL, '2025-07-16 11:22:59', NULL);
INSERT INTO `import_details` VALUES (6, 8, '14', 1, NULL, '2025-07-16 11:23:20', NULL);
INSERT INTO `import_details` VALUES (7, 9, '14', 1, '无错误信息', '2025-07-16 11:31:58', NULL);
INSERT INTO `import_details` VALUES (8, 10, '14', 1, '无错误信息', '2025-07-16 11:32:45', NULL);
INSERT INTO `import_details` VALUES (9, 11, '14', 1, '无错误信息', '2025-07-16 11:36:08', NULL);
INSERT INTO `import_details` VALUES (10, 12, '14', 1, '无错误信息', '2025-07-16 11:39:35', NULL);
INSERT INTO `import_details` VALUES (11, 13, '14', 1, '无错误信息', '2025-07-16 12:22:52', NULL);
INSERT INTO `import_details` VALUES (12, 14, '14', 1, '无错误信息', '2025-07-16 12:29:53', NULL);
INSERT INTO `import_details` VALUES (13, 15, '14', 1, '无错误信息', '2025-07-16 12:30:20', NULL);
INSERT INTO `import_details` VALUES (14, 16, '14', 1, '无错误信息', '2025-07-16 12:35:50', NULL);
INSERT INTO `import_details` VALUES (15, 17, '14', 1, '无错误信息', '2025-07-16 12:38:29', NULL);
INSERT INTO `import_details` VALUES (16, 17, '15', 0, '第3行数据错误: 应用编码不存在: 百度1', '2025-07-16 12:38:29', NULL);
INSERT INTO `import_details` VALUES (17, 18, '14', 1, '无错误信息', '2025-07-16 14:01:05', NULL);
INSERT INTO `import_details` VALUES (18, 18, '15', 0, '第3行数据错误: 应用编码不存在: 百度1', '2025-07-16 14:01:05', NULL);
INSERT INTO `import_details` VALUES (19, 19, '14', 1, '无错误信息', '2025-07-16 14:04:04', NULL);
INSERT INTO `import_details` VALUES (20, 19, '15', 0, '第3行数据错误: 应用编码不存在: 百度1', '2025-07-16 14:04:04', NULL);
INSERT INTO `import_details` VALUES (21, 20, '14', 1, '无错误信息', '2025-07-16 14:11:24', NULL);
INSERT INTO `import_details` VALUES (22, 20, '15', 0, '第3行数据错误: 应用编码或应用不存在: 百度1', '2025-07-16 14:11:24', NULL);
INSERT INTO `import_details` VALUES (23, 20, '14', 1, '无错误信息', '2025-07-16 14:11:24', NULL);
INSERT INTO `import_details` VALUES (28, 25, 'TEMP_KEY', 0, '第2行数据错误: 电话格式不正确', '2025-07-16 15:59:28', NULL);
INSERT INTO `import_details` VALUES (29, 25, 'TEMP_KEY', 0, '第3行数据错误: 电话格式不正确', '2025-07-16 15:59:28', NULL);
INSERT INTO `import_details` VALUES (30, 25, 'TEMP_KEY', 0, '第4行数据错误: 应用编码不能为空', '2025-07-16 15:59:28', NULL);
INSERT INTO `import_details` VALUES (31, 25, 'TEMP_KEY', 0, '第5行数据错误: 电话格式不正确', '2025-07-16 15:59:28', NULL);
INSERT INTO `import_details` VALUES (32, 26, 'TEMP_KEY', 0, '第2行数据错误: 电话格式不正确', '2025-07-16 15:59:47', NULL);
INSERT INTO `import_details` VALUES (33, 26, 'TEMP_KEY', 0, '第3行数据错误: 电话格式不正确', '2025-07-16 15:59:47', NULL);
INSERT INTO `import_details` VALUES (34, 26, 'TEMP_KEY', 0, '第4行数据错误: 应用编码不能为空', '2025-07-16 15:59:47', NULL);
INSERT INTO `import_details` VALUES (35, 26, 'TEMP_KEY', 0, '第5行数据错误: 电话格式不正确', '2025-07-16 15:59:47', NULL);
INSERT INTO `import_details` VALUES (36, 27, 'FILE_USER', 0, '第2行数据错误: 电话格式不正确', '2025-07-16 16:01:15', NULL);
INSERT INTO `import_details` VALUES (37, 27, 'FILE_USER', 0, '第3行数据错误: 电话格式不正确', '2025-07-16 16:01:15', NULL);
INSERT INTO `import_details` VALUES (38, 27, 'FILE_USER', 0, '第4行数据错误: 应用编码不能为空', '2025-07-16 16:01:15', NULL);
INSERT INTO `import_details` VALUES (39, 27, 'FILE_USER', 0, '第5行数据错误: 电话格式不正确', '2025-07-16 16:01:15', NULL);
INSERT INTO `import_details` VALUES (40, 28, 'QXGL_FILE_USER', 0, '第2行数据错误: 电话格式不正确', '2025-07-16 16:02:36', NULL);
INSERT INTO `import_details` VALUES (41, 28, 'QXGL_FILE_USER', 0, '第3行数据错误: 电话格式不正确', '2025-07-16 16:02:36', NULL);
INSERT INTO `import_details` VALUES (42, 28, 'QXGL_FILE_USER', 0, '第4行数据错误: 应用编码不能为空', '2025-07-16 16:02:36', NULL);
INSERT INTO `import_details` VALUES (43, 28, 'QXGL_FILE_USER', 0, '第5行数据错误: 电话格式不正确', '2025-07-16 16:02:36', NULL);
INSERT INTO `import_details` VALUES (44, 29, 'QXGL_FILE_USER', 0, '第2行数据错误: 电话格式不正确', '2025-07-16 16:03:34', NULL);
INSERT INTO `import_details` VALUES (45, 29, 'QXGL_FILE_USER', 0, '第3行数据错误: 电话格式不正确', '2025-07-16 16:03:34', NULL);
INSERT INTO `import_details` VALUES (46, 29, 'QXGL_FILE_USER', 0, '第4行数据错误: 电话格式不正确', '2025-07-16 16:03:34', NULL);
INSERT INTO `import_details` VALUES (47, 29, 'QXGL_FILE_USER', 0, '第5行数据错误: 电话格式不正确', '2025-07-16 16:03:34', NULL);
INSERT INTO `import_details` VALUES (48, 30, 'QXGL_FILE_USER', 0, '第2行数据错误: 电话格式不正确', '2025-07-16 16:04:11', NULL);
INSERT INTO `import_details` VALUES (49, 30, 'QXGL_FILE_USER', 0, '第3行数据错误: 电话格式不正确', '2025-07-16 16:04:11', NULL);
INSERT INTO `import_details` VALUES (50, 30, 'QXGL_FILE_USER', 0, '第4行数据错误: 电话格式不正确', '2025-07-16 16:04:11', NULL);
INSERT INTO `import_details` VALUES (51, 30, 'QXGL_FILE_USER', 0, '第5行数据错误: 电话格式不正确', '2025-07-16 16:04:11', NULL);
INSERT INTO `import_details` VALUES (52, 31, 'QXGL_FILE_USER', 0, '第2行数据错误: 电话格式不正确', '2025-07-16 16:05:46', NULL);
INSERT INTO `import_details` VALUES (53, 31, 'QXGL_FILE_USER', 0, '第3行数据错误: 电话格式不正确', '2025-07-16 16:05:46', NULL);
INSERT INTO `import_details` VALUES (54, 31, 'QXGL_FILE_USER', 0, '第4行数据错误: 电话格式不正确', '2025-07-16 16:05:46', NULL);
INSERT INTO `import_details` VALUES (55, 31, 'QXGL_FILE_USER', 0, '第5行数据错误: 电话格式不正确', '2025-07-16 16:05:46', NULL);
INSERT INTO `import_details` VALUES (56, 32, 'QXGL_FILE_USER', 0, '第2行数据错误: 手机号码格式不正确', '2025-07-16 16:07:44', NULL);
INSERT INTO `import_details` VALUES (57, 32, 'QXGL_FILE_USER', 0, '第3行数据错误: 手机号码格式不正确', '2025-07-16 16:07:44', NULL);
INSERT INTO `import_details` VALUES (58, 32, 'QXGL_FILE_USER', 0, '第4行数据错误: 手机号码格式不正确', '2025-07-16 16:07:44', NULL);
INSERT INTO `import_details` VALUES (59, 32, 'QXGL_FILE_USER', 0, '第5行数据错误: 手机号码格式不正确', '2025-07-16 16:07:44', NULL);
INSERT INTO `import_details` VALUES (60, 33, 'QXGL_FILE_USER', 0, '第2行数据错误: 手机号码必须是1开头的11位数字', '2025-07-16 16:10:16', NULL);
INSERT INTO `import_details` VALUES (61, 33, 'QXGL_FILE_USER', 0, '第3行数据错误: 手机号码必须是1开头的11位数字', '2025-07-16 16:10:16', NULL);
INSERT INTO `import_details` VALUES (62, 33, 'QXGL_FILE_USER', 0, '第4行数据错误: 手机号码必须是1开头的11位数字', '2025-07-16 16:10:17', NULL);
INSERT INTO `import_details` VALUES (63, 33, 'QXGL_FILE_USER', 0, '第5行数据错误: 手机号码必须是1开头的11位数字', '2025-07-16 16:10:17', NULL);
INSERT INTO `import_details` VALUES (64, 34, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-16 16:11:13', NULL);
INSERT INTO `import_details` VALUES (65, 34, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-16 16:11:13', NULL);
INSERT INTO `import_details` VALUES (66, 34, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-16 16:11:13', NULL);
INSERT INTO `import_details` VALUES (67, 34, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-16 16:11:13', NULL);
INSERT INTO `import_details` VALUES (68, 35, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-16 16:29:50', NULL);
INSERT INTO `import_details` VALUES (69, 35, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-16 16:29:50', NULL);
INSERT INTO `import_details` VALUES (70, 35, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-16 16:29:50', NULL);
INSERT INTO `import_details` VALUES (71, 35, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-16 16:29:50', NULL);
INSERT INTO `import_details` VALUES (72, 37, 'QXGL_FILE_USER', 0, '第2行数据错误: Query did not return a unique result: 3 results were returned', '2025-07-16 17:17:29', NULL);
INSERT INTO `import_details` VALUES (73, 37, 'QXGL_FILE_USER', 0, '第3行数据错误: Query did not return a unique result: 3 results were returned', '2025-07-16 17:17:29', NULL);
INSERT INTO `import_details` VALUES (74, 37, 'QXGL_FILE_USER', 0, '第4行数据错误: Query did not return a unique result: 3 results were returned', '2025-07-16 17:17:29', NULL);
INSERT INTO `import_details` VALUES (75, 37, 'QXGL_FILE_USER', 0, '第5行数据错误: Query did not return a unique result: 3 results were returned', '2025-07-16 17:17:29', NULL);
INSERT INTO `import_details` VALUES (80, 39, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-16 17:22:15', 'UPDATE');
INSERT INTO `import_details` VALUES (81, 39, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-16 17:22:15', 'UPDATE');
INSERT INTO `import_details` VALUES (82, 39, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-16 17:22:15', 'UPDATE');
INSERT INTO `import_details` VALUES (83, 40, 'QXGL_FILE_USER', 0, '第2行数据错误: 性别不能为空', '2025-07-17 09:33:54', NULL);
INSERT INTO `import_details` VALUES (84, 41, 'QXGL_FILE_USER', 0, '第2行数据错误: 性别不能为空', '2025-07-17 09:43:54', NULL);
INSERT INTO `import_details` VALUES (85, 42, 'QXGL_FILE_USER', 0, '第2行数据错误: 性别不能为空', '2025-07-17 09:45:55', NULL);
INSERT INTO `import_details` VALUES (86, 43, 'QXGL_FILE_USER', 0, '第2行数据错误: 应用编码不存在: QXGL', '2025-07-17 09:48:43', NULL);
INSERT INTO `import_details` VALUES (89, 46, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-17 09:57:15', 'CREATE');
INSERT INTO `import_details` VALUES (90, 47, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-17 09:57:41', 'UPDATE');
INSERT INTO `import_details` VALUES (91, 48, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-17 10:01:01', 'UPDATE');
INSERT INTO `import_details` VALUES (92, 49, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-17 10:34:21', 'UPDATE');
INSERT INTO `import_details` VALUES (93, 50, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-17 10:34:39', 'UPDATE');
INSERT INTO `import_details` VALUES (94, 51, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-17 10:35:58', 'UPDATE');
INSERT INTO `import_details` VALUES (95, 52, 'SYNC_API_USER', 1, '无错误信息', '2025-07-17 12:27:27', 'UPDATE');
INSERT INTO `import_details` VALUES (96, 53, 'SYNC_API_USER', 1, '无错误信息', '2025-07-17 12:29:08', 'UPDATE');
INSERT INTO `import_details` VALUES (97, 54, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-17 14:16:53', 'UPDATE');
INSERT INTO `import_details` VALUES (98, 55, 'QXGL_FILE_USER', 0, '第2行数据错误: 应用编码不能为空', '2025-07-17 14:17:07', NULL);
INSERT INTO `import_details` VALUES (99, 55, 'QXGL_FILE_USER', 0, '第3行数据错误: 应用编码不能为空', '2025-07-17 14:17:07', NULL);
INSERT INTO `import_details` VALUES (100, 56, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-17 14:17:55', 'UPDATE');
INSERT INTO `import_details` VALUES (101, 57, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-17 14:36:05', 'UPDATE');
INSERT INTO `import_details` VALUES (102, 58, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-17 14:44:55', 'UPDATE');
INSERT INTO `import_details` VALUES (103, 59, 'ROLE_IMPORT', 0, '应用编码不存在: 系统管理员，拥有所有权限', '2025-07-17 17:25:16', NULL);
INSERT INTO `import_details` VALUES (104, 60, 'ROLE_IMPORT', 0, '应用编码不存在: 系统管理员，拥有所有权限', '2025-07-17 17:27:22', NULL);
INSERT INTO `import_details` VALUES (105, 61, 'ROLE_IMPORT', 0, '父角色不存在: QXGL_ROLE1', '2025-07-17 17:44:52', 'UPDATE');
INSERT INTO `import_details` VALUES (106, 62, 'ROLE_IMPORT', 0, '父角色不存在: QXGL_ROLE1', '2025-07-17 17:55:39', 'UPDATE');
INSERT INTO `import_details` VALUES (107, 63, 'ROLE_IMPORT', 1, '无错误信息', '2025-07-17 18:00:23', 'UPDATE');
INSERT INTO `import_details` VALUES (108, 64, 'ROLE_IMPORT', 1, '无错误信息', '2025-07-17 18:07:52', 'UPDATE');
INSERT INTO `import_details` VALUES (109, 65, 'ROLE_IMPORT', 1, '无错误信息', '2025-07-17 18:08:40', 'UPDATE');
INSERT INTO `import_details` VALUES (110, 66, 'ROLE_IMPORT', 1, '无错误信息', '2025-07-17 18:12:24', 'UPDATE');
INSERT INTO `import_details` VALUES (111, 67, 'ROLE_SYNC', 0, '父角色编码不能为空', '2025-07-18 09:56:51', NULL);
INSERT INTO `import_details` VALUES (112, 67, 'ROLE_SYNC', 0, '应用编码不存在: APP001', '2025-07-18 09:56:51', NULL);
INSERT INTO `import_details` VALUES (113, 67, 'ROLE_SYNC', 0, '应用编码不存在: APP001', '2025-07-18 09:56:51', NULL);
INSERT INTO `import_details` VALUES (114, 68, 'ROLE_SYNC', 0, '父角色编码不能为空', '2025-07-18 09:57:19', NULL);
INSERT INTO `import_details` VALUES (115, 68, 'ROLE_SYNC', 0, '应用编码不存在: APP001', '2025-07-18 09:57:19', NULL);
INSERT INTO `import_details` VALUES (116, 68, 'ROLE_SYNC', 0, '应用编码不存在: APP001', '2025-07-18 09:57:19', NULL);
INSERT INTO `import_details` VALUES (117, 69, 'ROLE_SYNC', 0, '父角色编码不能为空', '2025-07-18 10:01:52', NULL);
INSERT INTO `import_details` VALUES (118, 69, 'ROLE_SYNC', 0, '应用编码不存在: APP001', '2025-07-18 10:01:52', NULL);
INSERT INTO `import_details` VALUES (119, 69, 'ROLE_SYNC', 0, '应用编码不存在: APP001', '2025-07-18 10:01:52', NULL);
INSERT INTO `import_details` VALUES (126, 72, 'ROLE_SYNC', 0, '父角色编码不能为空', '2025-07-18 10:05:40', NULL);
INSERT INTO `import_details` VALUES (127, 72, 'ROLE_SYNC', 0, '父角色不存在: ROLE_CODE1', '2025-07-18 10:05:40', 'CREATE');
INSERT INTO `import_details` VALUES (128, 72, 'ROLE_SYNC', 0, '父角色不存在: ROLE_CODE2', '2025-07-18 10:05:40', 'CREATE');
INSERT INTO `import_details` VALUES (129, 73, 'ROLE_SYNC', 0, '父角色编码不能为空', '2025-07-18 10:07:03', NULL);
INSERT INTO `import_details` VALUES (130, 73, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:07:03', 'UPDATE');
INSERT INTO `import_details` VALUES (131, 73, 'ROLE_SYNC', 0, '父角色不存在: QXGL_ROLE3', '2025-07-18 10:07:03', 'UPDATE');
INSERT INTO `import_details` VALUES (132, 74, 'ROLE_SYNC', 0, '父角色编码不能为空', '2025-07-18 10:07:46', NULL);
INSERT INTO `import_details` VALUES (133, 74, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:07:46', 'UPDATE');
INSERT INTO `import_details` VALUES (134, 74, 'ROLE_SYNC', 0, '父角色不存在: ROLE_CODE2', '2025-07-18 10:07:46', 'UPDATE');
INSERT INTO `import_details` VALUES (135, 75, 'ROLE_SYNC', 0, '父角色编码不能为空', '2025-07-18 10:09:56', NULL);
INSERT INTO `import_details` VALUES (136, 75, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:09:56', 'UPDATE');
INSERT INTO `import_details` VALUES (137, 75, 'ROLE_SYNC', 0, '父角色不存在: QXGL_ROLE2', '2025-07-18 10:09:56', 'UPDATE');
INSERT INTO `import_details` VALUES (138, 76, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:11:18', 'UPDATE');
INSERT INTO `import_details` VALUES (139, 76, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:11:18', 'UPDATE');
INSERT INTO `import_details` VALUES (140, 76, 'ROLE_SYNC', 0, '父角色不存在: QXGL_ROLE2', '2025-07-18 10:11:18', 'UPDATE');
INSERT INTO `import_details` VALUES (141, 77, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:12:20', 'UPDATE');
INSERT INTO `import_details` VALUES (142, 77, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:12:20', 'UPDATE');
INSERT INTO `import_details` VALUES (143, 77, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:12:20', 'UPDATE');
INSERT INTO `import_details` VALUES (144, 78, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:35:45', 'UPDATE');
INSERT INTO `import_details` VALUES (145, 78, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:35:45', 'UPDATE');
INSERT INTO `import_details` VALUES (146, 78, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:35:45', 'UPDATE');
INSERT INTO `import_details` VALUES (147, 79, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:35:48', 'UPDATE');
INSERT INTO `import_details` VALUES (148, 79, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:35:48', 'UPDATE');
INSERT INTO `import_details` VALUES (149, 79, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:35:48', 'UPDATE');
INSERT INTO `import_details` VALUES (156, 82, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:49:00', 'UPDATE');
INSERT INTO `import_details` VALUES (157, 82, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:49:00', 'UPDATE');
INSERT INTO `import_details` VALUES (158, 82, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 10:49:00', 'CREATE');
INSERT INTO `import_details` VALUES (159, 83, 'ROLE_IMPORT', 1, '无错误信息', '2025-07-18 10:52:10', 'UPDATE');
INSERT INTO `import_details` VALUES (160, 84, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-18 14:50:29', 'CREATE');
INSERT INTO `import_details` VALUES (161, 85, 'ROLE_IMPORT', 0, '第1行: 父角色不存在: QXGL_ROLE1', '2025-07-18 14:51:30', 'UPDATE');
INSERT INTO `import_details` VALUES (167, 91, 'ROLE_IMPORT', 0, '第1行: 子角色不存在: QXGL_ROLE3', '2025-07-18 15:17:05', 'UPDATE');
INSERT INTO `import_details` VALUES (168, 92, 'ROLE_IMPORT', 0, '第1行: 应用不存在: 信息部', '2025-07-18 15:18:23', NULL);
INSERT INTO `import_details` VALUES (169, 93, 'ROLE_IMPORT', 0, '第1行: 子角色不存在: QXGL_ROLE4', '2025-07-18 15:18:42', 'UPDATE');
INSERT INTO `import_details` VALUES (170, 94, 'ROLE_IMPORT', 0, '第1行: sourceSystem is marked non-null but is null', '2025-07-18 15:20:29', 'UPDATE');
INSERT INTO `import_details` VALUES (173, 97, 'ROLE_IMPORT', 1, '无错误信息', '2025-07-18 15:26:59', 'UPDATE');
INSERT INTO `import_details` VALUES (174, 98, 'ROLE_IMPORT', 1, '无错误信息', '2025-07-18 15:27:11', 'UPDATE');
INSERT INTO `import_details` VALUES (175, 99, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 15:34:12', 'UPDATE');
INSERT INTO `import_details` VALUES (176, 99, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 15:34:12', 'UPDATE');
INSERT INTO `import_details` VALUES (177, 99, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 15:34:12', 'UPDATE');
INSERT INTO `import_details` VALUES (186, 104, 'SYNC_API_USER', 1, '无错误信息', '2025-07-18 15:56:22', 'UPDATE');
INSERT INTO `import_details` VALUES (187, 104, 'SYNC_API_USER', 1, '无错误信息', '2025-07-18 15:56:22', 'UPDATE');
INSERT INTO `import_details` VALUES (188, 105, 'SYNC_API_USER', 1, '无错误信息', '2025-07-18 15:56:45', 'UPDATE');
INSERT INTO `import_details` VALUES (189, 105, 'SYNC_API_USER', 1, '无错误信息', '2025-07-18 15:56:45', 'UPDATE');
INSERT INTO `import_details` VALUES (190, 106, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 15:59:19', 'UPDATE');
INSERT INTO `import_details` VALUES (191, 106, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 15:59:19', 'UPDATE');
INSERT INTO `import_details` VALUES (192, 106, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 15:59:19', 'UPDATE');
INSERT INTO `import_details` VALUES (193, 107, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 15:59:44', 'UPDATE');
INSERT INTO `import_details` VALUES (194, 107, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 15:59:44', 'UPDATE');
INSERT INTO `import_details` VALUES (195, 107, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 15:59:44', 'UPDATE');
INSERT INTO `import_details` VALUES (196, 108, 'SYNC_API_USER', 1, '无错误信息', '2025-07-18 16:03:23', 'UPDATE');
INSERT INTO `import_details` VALUES (197, 108, 'SYNC_API_USER', 1, '无错误信息', '2025-07-18 16:03:23', 'UPDATE');
INSERT INTO `import_details` VALUES (198, 109, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 16:03:30', 'UPDATE');
INSERT INTO `import_details` VALUES (199, 109, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 16:03:30', 'UPDATE');
INSERT INTO `import_details` VALUES (200, 109, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 16:03:30', 'UPDATE');
INSERT INTO `import_details` VALUES (201, 110, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 16:04:24', 'UPDATE');
INSERT INTO `import_details` VALUES (202, 110, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 16:04:25', 'UPDATE');
INSERT INTO `import_details` VALUES (203, 110, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 16:04:25', 'UPDATE');
INSERT INTO `import_details` VALUES (204, 111, 'SYNC_API_USER', 1, '无错误信息', '2025-07-18 16:04:29', 'UPDATE');
INSERT INTO `import_details` VALUES (205, 111, 'SYNC_API_USER', 1, '无错误信息', '2025-07-18 16:04:29', 'UPDATE');
INSERT INTO `import_details` VALUES (206, 112, 'QXGL_FILE_USER', 1, '无错误信息', '2025-07-18 16:13:27', 'UPDATE');
INSERT INTO `import_details` VALUES (207, 113, 'ROLE_IMPORT', 1, '无错误信息', '2025-07-18 16:13:44', 'UPDATE');
INSERT INTO `import_details` VALUES (208, 114, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 16:14:35', 'UPDATE');
INSERT INTO `import_details` VALUES (209, 114, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 16:14:35', 'UPDATE');
INSERT INTO `import_details` VALUES (210, 114, 'ROLE_SYNC', 1, '无错误信息', '2025-07-18 16:14:35', 'UPDATE');
INSERT INTO `import_details` VALUES (211, 115, 'SYNC_API_USER', 1, '无错误信息', '2025-07-18 16:15:56', 'UPDATE');
INSERT INTO `import_details` VALUES (212, 115, 'SYNC_API_USER', 1, '无错误信息', '2025-07-18 16:15:56', 'UPDATE');

-- ----------------------------
-- Table structure for import_records
-- ----------------------------
DROP TABLE IF EXISTS `import_records`;
CREATE TABLE `import_records`  (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `file_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `file_type` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `total_count` int NOT NULL,
  `success_count` int NOT NULL,
  `failed_count` int NOT NULL,
  `mode` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'FULL/INCREMENTAL',
  `operator` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `start_time` datetime NOT NULL,
  `end_time` datetime NOT NULL,
  `status` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'PENDING/PROCESSING/SUCCESS/FAILED/PARTIAL',
  `error_summary` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `source_system` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT 'AUTH_CENTER/BUSINESS_SYSTEM',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 116 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of import_records
-- ----------------------------
INSERT INTO `import_records` VALUES (1, '用户导入模板 (2).xlsx', 'USER', 1, 0, 1, 'FULL', '当前用户名', '2025-07-16 10:48:08', '2025-07-16 10:48:08', 'FAILED', '第2行数据错误: 应用编码不存在: QXGL', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (2, '用户导入模板 (2).xlsx', 'USER', 1, 0, 1, 'FULL', 'admin', '2025-07-16 10:55:19', '2025-07-16 10:55:19', 'FAILED', '第2行数据错误: 应用编码不存在: QXGL', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (3, '用户导入模板 (2).xlsx', 'USER', 1, 0, 1, 'FULL', 'admin', '2025-07-16 11:09:51', '2025-07-16 11:09:51', 'FAILED', '第2行数据错误: 应用编码不存在: 2', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (4, '用户导入模板 (2).xlsx', 'USER', 1, 0, 1, 'FULL', 'admin', '2025-07-16 11:11:53', '2025-07-16 11:11:53', 'FAILED', '第2行数据错误: Query did not return a unique result: 3 results were returned', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (7, '用户导入模板 (2).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-16 11:22:59', '2025-07-16 11:22:59', 'SUCCESS', '', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (8, '用户导入模板 (2).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-16 11:23:20', '2025-07-16 11:23:20', 'SUCCESS', '', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (9, '用户导入模板 (2).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-16 11:31:58', '2025-07-16 11:31:58', 'SUCCESS', '', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (10, '用户导入模板 (2).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-16 11:32:45', '2025-07-16 11:32:45', 'SUCCESS', '', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (11, '用户导入模板 (2).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-16 11:36:08', '2025-07-16 11:36:08', 'SUCCESS', '', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (12, '用户导入模板 (2).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-16 11:39:35', '2025-07-16 11:39:35', 'SUCCESS', '', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (13, '用户导入模板 (2).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-16 12:22:52', '2025-07-16 12:22:52', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (14, '用户导入模板 (2).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-16 12:29:53', '2025-07-16 12:29:53', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (15, '用户导入模板 (2).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-16 12:30:20', '2025-07-16 12:30:20', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (16, '用户导入模板 (2).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-16 12:35:50', '2025-07-16 12:35:50', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (17, '用户导入模板 (2).xlsx', 'USER', 2, 1, 1, 'FULL', 'admin', '2025-07-16 12:38:29', '2025-07-16 12:38:29', 'PARTIAL', '第3行数据错误: 应用编码不存在: 百度1', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (18, '用户导入模板 (2).xlsx', 'USER', 2, 1, 1, 'FULL', 'admin', '2025-07-16 14:01:05', '2025-07-16 14:01:05', 'PARTIAL', '第3行数据错误: 应用编码不存在: 百度1', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (19, '用户导入模板 (2).xlsx', 'USER', 2, 1, 1, 'FULL', 'admin', '2025-07-16 14:04:04', '2025-07-16 14:04:04', 'PARTIAL', '第3行数据错误: 应用编码不存在: 百度1', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (20, '用户导入模板 (2).xlsx', 'USER', 3, 2, 1, 'FULL', 'admin', '2025-07-16 14:11:24', '2025-07-16 14:11:24', 'PARTIAL', '第3行数据错误: 应用编码或应用不存在: 百度1', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (25, '用户导入模板 (2).xlsx', 'USER', 4, 0, 4, 'FULL', 'admin', '2025-07-16 15:59:28', '2025-07-16 15:59:28', 'FAILED', '第2行数据错误: 电话格式不正确; 第3行数据错误: 电话格式不正确; 第4行数据错误: 应用编码不能为空; 第5行数据错误: 电话格式不正确', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (26, '用户导入模板 (2).xlsx', 'USER', 4, 0, 4, 'FULL', 'admin', '2025-07-16 15:59:47', '2025-07-16 15:59:47', 'FAILED', '第2行数据错误: 电话格式不正确; 第3行数据错误: 电话格式不正确; 第4行数据错误: 应用编码不能为空; 第5行数据错误: 电话格式不正确', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (27, '用户导入模板 (2).xlsx', 'USER', 4, 0, 4, 'FULL', 'admin', '2025-07-16 16:01:15', '2025-07-16 16:01:15', 'FAILED', '第2行数据错误: 电话格式不正确; 第3行数据错误: 电话格式不正确; 第4行数据错误: 应用编码不能为空; 第5行数据错误: 电话格式不正确', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (28, '用户导入模板 (2).xlsx', 'USER', 4, 0, 4, 'FULL', 'admin', '2025-07-16 16:02:36', '2025-07-16 16:02:36', 'FAILED', '第2行数据错误: 电话格式不正确; 第3行数据错误: 电话格式不正确; 第4行数据错误: 应用编码不能为空; 第5行数据错误: 电话格式不正确', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (29, '用户导入模板 (2).xlsx', 'USER', 4, 0, 4, 'FULL', 'admin', '2025-07-16 16:03:34', '2025-07-16 16:03:34', 'FAILED', '第2行数据错误: 电话格式不正确; 第3行数据错误: 电话格式不正确; 第4行数据错误: 电话格式不正确; 第5行数据错误: 电话格式不正确', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (30, '用户导入模板 (2).xlsx', 'USER', 4, 0, 4, 'FULL', 'admin', '2025-07-16 16:04:11', '2025-07-16 16:04:11', 'FAILED', '第2行数据错误: 电话格式不正确; 第3行数据错误: 电话格式不正确; 第4行数据错误: 电话格式不正确; 第5行数据错误: 电话格式不正确', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (31, '用户导入模板 (2).xlsx', 'USER', 4, 0, 4, 'FULL', 'admin', '2025-07-16 16:05:46', '2025-07-16 16:05:46', 'FAILED', '第2行数据错误: 电话格式不正确; 第3行数据错误: 电话格式不正确; 第4行数据错误: 电话格式不正确; 第5行数据错误: 电话格式不正确', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (32, '用户导入模板 (2).xlsx', 'USER', 4, 0, 4, 'FULL', 'admin', '2025-07-16 16:07:44', '2025-07-16 16:07:44', 'FAILED', '第2行数据错误: 手机号码格式不正确; 第3行数据错误: 手机号码格式不正确; 第4行数据错误: 手机号码格式不正确; 第5行数据错误: 手机号码格式不正确', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (33, '用户导入模板 (2).xlsx', 'USER', 4, 0, 4, 'FULL', 'admin', '2025-07-16 16:10:16', '2025-07-16 16:10:17', 'FAILED', '第2行数据错误: 手机号码必须是1开头的11位数字; 第3行数据错误: 手机号码必须是1开头的11位数字; 第4行数据错误: 手机号码必须是1开头的11位数字; 第5行数据错误: 手机号码必须是1开头的11位数字', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (34, '用户导入模板 (2).xlsx', 'USER', 4, 4, 0, 'FULL', 'admin', '2025-07-16 16:11:13', '2025-07-16 16:11:13', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (35, '用户导入模板 (2).xlsx', 'USER', 4, 4, 0, 'FULL', 'admin', '2025-07-16 16:29:50', '2025-07-16 16:29:50', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (37, '用户导入模板 (2).xlsx', 'USER', 4, 0, 4, 'FULL', 'admin', '2025-07-16 17:17:29', '2025-07-16 17:17:29', 'FAILED', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (39, '用户导入模板 (2).xlsx', 'USER', 3, 3, 0, 'FULL', 'admin', '2025-07-16 17:22:15', '2025-07-16 17:22:15', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (40, '用户导入模板 (3).xlsx', 'USER', 1, 0, 1, 'FULL', 'admin', '2025-07-17 09:33:54', '2025-07-17 09:33:54', 'FAILED', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (41, '用户导入模板 (3).xlsx', 'USER', 1, 0, 1, 'FULL', 'admin', '2025-07-17 09:43:54', '2025-07-17 09:43:54', 'FAILED', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (42, '用户导入模板 (3).xlsx', 'USER', 1, 0, 1, 'FULL', 'admin', '2025-07-17 09:45:55', '2025-07-17 09:45:55', 'FAILED', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (43, '用户导入模板 (3).xlsx', 'USER', 1, 0, 1, 'FULL', 'admin', '2025-07-17 09:48:43', '2025-07-17 09:48:43', 'FAILED', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (46, '用户导入模板 (3).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-17 09:57:15', '2025-07-17 09:57:15', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (47, '用户导入模板 (3).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-17 09:57:41', '2025-07-17 09:57:41', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (48, '用户导入模板 (3).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-17 10:01:01', '2025-07-17 10:01:01', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (49, '用户导入模板 (5).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-17 10:34:21', '2025-07-17 10:34:21', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (50, '用户导入模板 (5).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-17 10:34:39', '2025-07-17 10:34:39', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (51, '用户导入模板 (5).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-17 10:35:58', '2025-07-17 10:35:58', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (52, 'SYNC_FROM_API', 'USER', 1, 1, 0, 'FULL', 'SYSTEM_SYNC', '2025-07-17 12:27:27', '2025-07-17 12:27:27', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (53, 'SYNC_FROM_API', 'USER', 1, 1, 0, 'FULL', 'SYSTEM_SYNC', '2025-07-17 12:29:08', '2025-07-17 12:29:08', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (54, '用户导入模板 (5).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-17 14:16:53', '2025-07-17 14:16:53', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (55, '用户列表 (1).xlsx', 'USER', 2, 0, 2, 'FULL', 'admin', '2025-07-17 14:17:07', '2025-07-17 14:17:07', 'FAILED', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (56, '用户导入模板 (5).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-17 14:17:55', '2025-07-17 14:17:55', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (57, '用户导入模板 (5).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-17 14:36:05', '2025-07-17 14:36:05', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (58, '用户导入模板 (5).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-17 14:44:55', '2025-07-17 14:44:55', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (59, '角色导入模板 (3).xlsx', 'ROLE', 1, 0, 1, 'FULL', 'admin', '2025-07-17 17:25:16', '2025-07-17 17:25:16', 'PARTIAL', '第1行: 应用编码不存在: 系统管理员，拥有所有权限', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (60, '角色导入模板 (4).xlsx', 'ROLE', 1, 0, 1, 'FULL', 'admin', '2025-07-17 17:27:22', '2025-07-17 17:27:22', 'PARTIAL', '第1行: 应用编码不存在: 系统管理员，拥有所有权限', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (61, '角色导入模板 (5).xlsx', 'ROLE', 1, 0, 1, 'FULL', 'admin', '2025-07-17 17:44:52', '2025-07-17 17:44:52', 'PARTIAL', '第1行: 父角色不存在: QXGL_ROLE1', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (62, '角色导入模板 (5).xlsx', 'ROLE', 1, 0, 1, 'FULL', 'admin', '2025-07-17 17:55:39', '2025-07-17 17:55:39', 'PARTIAL', '第1行: 父角色不存在: QXGL_ROLE1', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (63, '角色导入模板 (5).xlsx', 'ROLE', 1, 1, 0, 'FULL', 'admin', '2025-07-17 18:00:23', '2025-07-17 18:00:23', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (64, '角色导入模板 (5).xlsx', 'ROLE', 1, 1, 0, 'FULL', 'admin', '2025-07-17 18:07:51', '2025-07-17 18:07:52', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (65, '角色导入模板 (5).xlsx', 'ROLE', 1, 1, 0, 'FULL', 'admin', '2025-07-17 18:08:40', '2025-07-17 18:08:40', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (66, '角色导入模板 (5).xlsx', 'ROLE', 1, 1, 0, 'FULL', 'admin', '2025-07-17 18:12:24', '2025-07-17 18:12:24', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (67, 'API_SYNC', 'ROLE', 3, 0, 3, 'INCREMENTAL', 'admin', '2025-07-18 09:56:51', '2025-07-18 09:56:51', 'PARTIAL', '第1条: 父角色编码不能为空; 第2条: 应用编码不存在: APP001; 第3条: 应用编码不存在: APP001', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (68, 'API_SYNC', 'ROLE', 3, 0, 3, 'INCREMENTAL', 'admin', '2025-07-18 09:57:19', '2025-07-18 09:57:19', 'PARTIAL', '第1条: 父角色编码不能为空; 第2条: 应用编码不存在: APP001; 第3条: 应用编码不存在: APP001', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (69, 'API_SYNC', 'ROLE', 3, 0, 3, 'INCREMENTAL', 'admin', '2025-07-18 10:01:52', '2025-07-18 10:01:52', 'PARTIAL', '第1条: 父角色编码不能为空; 第2条: 应用编码不存在: APP001; 第3条: 应用编码不存在: APP001', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (72, 'API_SYNC', 'ROLE', 3, 0, 3, 'INCREMENTAL', 'admin', '2025-07-18 10:05:40', '2025-07-18 10:05:40', 'PARTIAL', '第1条: 父角色编码不能为空; 第2条: 父角色不存在: ROLE_CODE1; 第3条: 父角色不存在: ROLE_CODE2', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (73, 'API_SYNC', 'ROLE', 3, 1, 2, 'INCREMENTAL', 'admin', '2025-07-18 10:07:03', '2025-07-18 10:07:03', 'PARTIAL', '第1条: 父角色编码不能为空; 第3条: 父角色不存在: QXGL_ROLE3', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (74, 'API_SYNC', 'ROLE', 3, 1, 2, 'INCREMENTAL', 'admin', '2025-07-18 10:07:46', '2025-07-18 10:07:46', 'PARTIAL', '第1条: 父角色编码不能为空; 第3条: 父角色不存在: ROLE_CODE2', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (75, 'API_SYNC', 'ROLE', 3, 1, 2, 'INCREMENTAL', 'admin', '2025-07-18 10:09:56', '2025-07-18 10:09:56', 'PARTIAL', '第1条: 父角色编码不能为空; 第3条: 父角色不存在: QXGL_ROLE2', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (76, 'API_SYNC', 'ROLE', 3, 2, 1, 'INCREMENTAL', 'admin', '2025-07-18 10:11:18', '2025-07-18 10:11:18', 'PARTIAL', '第3条: 父角色不存在: QXGL_ROLE2', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (77, 'API_SYNC', 'ROLE', 3, 3, 0, 'INCREMENTAL', 'admin', '2025-07-18 10:12:20', '2025-07-18 10:12:20', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (78, 'API_SYNC', 'ROLE', 3, 3, 0, 'INCREMENTAL', 'admin', '2025-07-18 10:35:45', '2025-07-18 10:35:45', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (79, 'API_SYNC', 'ROLE', 3, 3, 0, 'INCREMENTAL', 'admin', '2025-07-18 10:35:48', '2025-07-18 10:35:48', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (82, 'API_SYNC', 'ROLE', 3, 3, 0, 'INCREMENTAL', 'admin', '2025-07-18 10:49:00', '2025-07-18 10:49:00', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (83, '角色导入模板 (5).xlsx', 'ROLE', 1, 1, 0, 'FULL', 'admin', '2025-07-18 10:52:10', '2025-07-18 10:52:10', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (84, '用户导入模板 (10).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-18 14:50:28', '2025-07-18 14:50:29', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (85, '角色导入模板 (6).xlsx', 'ROLE', 1, 0, 1, 'FULL', 'admin', '2025-07-18 14:51:30', '2025-07-18 14:51:30', 'PARTIAL', '第1行: 父角色不存在: QXGL_ROLE1', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (91, '角色导入模板 (6).xlsx', 'ROLE', 1, 0, 1, 'FULL', 'admin', '2025-07-18 15:17:05', '2025-07-18 15:17:05', 'PARTIAL', '第1行: 子角色不存在: QXGL_ROLE3', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (92, '用户导入模板 (6).xlsx', 'ROLE', 1, 0, 1, 'FULL', 'admin', '2025-07-18 15:18:23', '2025-07-18 15:18:23', 'PARTIAL', '第1行: 应用不存在: 信息部', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (93, '角色导入模板 (6).xlsx', 'ROLE', 1, 0, 1, 'FULL', 'admin', '2025-07-18 15:18:42', '2025-07-18 15:18:42', 'PARTIAL', '第1行: 子角色不存在: QXGL_ROLE4', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (94, '角色导入模板 (6).xlsx', 'ROLE', 1, 0, 1, 'FULL', 'admin', '2025-07-18 15:20:29', '2025-07-18 15:20:29', 'PARTIAL', '第1行: sourceSystem is marked non-null but is null', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (97, '角色导入模板 (6).xlsx', 'ROLE', 1, 1, 0, 'FULL', 'admin', '2025-07-18 15:26:59', '2025-07-18 15:26:59', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (98, '角色导入模板 (7).xlsx', 'ROLE', 1, 1, 0, 'FULL', 'admin', '2025-07-18 15:27:11', '2025-07-18 15:27:11', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (99, 'API_SYNC', 'ROLE', 3, 3, 0, 'INCREMENTAL', 'admin', '2025-07-18 15:34:12', '2025-07-18 15:34:12', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (104, 'SYNC_FROM_API', 'USER', 2, 2, 0, 'FULL', 'SYSTEM_SYNC', '2025-07-18 15:56:22', '2025-07-18 15:56:22', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (105, 'SYNC_FROM_API', 'USER', 2, 2, 0, 'FULL', 'SYSTEM_SYNC', '2025-07-18 15:56:45', '2025-07-18 15:56:45', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (106, 'API_SYNC', 'ROLE', 3, 3, 0, 'INCREMENTAL', 'admin', '2025-07-18 15:59:19', '2025-07-18 15:59:19', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (107, 'API_SYNC', 'ROLE', 3, 3, 0, 'INCREMENTAL', 'admin', '2025-07-18 15:59:44', '2025-07-18 15:59:44', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (108, 'SYNC_FROM_API', 'USER', 2, 2, 0, 'FULL', 'SYSTEM_SYNC', '2025-07-18 16:03:23', '2025-07-18 16:03:23', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (109, 'API_SYNC', 'ROLE', 3, 3, 0, 'INCREMENTAL', 'admin', '2025-07-18 16:03:30', '2025-07-18 16:03:30', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (110, 'API_SYNC', 'ROLE', 3, 3, 0, 'INCREMENTAL', 'admin', '2025-07-18 16:04:24', '2025-07-18 16:04:25', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (111, 'SYNC_FROM_API', 'USER', 2, 2, 0, 'FULL', 'SYSTEM_SYNC', '2025-07-18 16:04:29', '2025-07-18 16:04:29', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (112, '用户导入模板 (11).xlsx', 'USER', 1, 1, 0, 'FULL', 'admin', '2025-07-18 16:13:27', '2025-07-18 16:13:27', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (113, '角色导入模板 (8).xlsx', 'ROLE', 1, 1, 0, 'FULL', 'admin', '2025-07-18 16:13:44', '2025-07-18 16:13:44', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (114, 'API_SYNC', 'ROLE', 3, 3, 0, 'INCREMENTAL', 'admin', '2025-07-18 16:14:35', '2025-07-18 16:14:35', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');
INSERT INTO `import_records` VALUES (115, 'SYNC_FROM_API', 'USER', 2, 2, 0, 'FULL', 'SYSTEM_SYNC', '2025-07-18 16:15:56', '2025-07-18 16:15:56', 'SUCCESS', '无错误信息', 'BUSINESS_SYSTEM');

-- ----------------------------
-- Table structure for modules
-- ----------------------------
DROP TABLE IF EXISTS `modules`;
CREATE TABLE `modules`  (
  `module_id` bigint NOT NULL AUTO_INCREMENT,
  `module_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `module_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `module_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `parent_id` int NULL DEFAULT 0,
  `del_flag` int NOT NULL DEFAULT 0,
  `point_user_phone` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creat_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creat_time` datetime NOT NULL,
  `update_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  PRIMARY KEY (`module_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 26 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of modules
-- ----------------------------
INSERT INTO `modules` VALUES (1, '用户管理模块', '用户管理模块', 'http://oa.example.com/user', 0, 1, '13800138001', 'admin', '2023-01-01 10:00:00', 'admin', '2025-06-30 08:54:18');
INSERT INTO `modules` VALUES (2, '角色管理模块', '角色管理模块', 'http://oa.example.com/role', 0, 1, '13800138002', 'admin', '2023-01-01 10:00:00', 'admin', '2025-06-30 10:02:32');
INSERT INTO `modules` VALUES (3, '权限管理模块', '权限管理模块', 'http://oa.example.com/auth', 0, 0, '13800138003', 'admin', '2023-01-01 10:00:00', 'admin', '2023-01-01 10:00:00');
INSERT INTO `modules` VALUES (4, '部门管理模块', '部门管理模块', 'http://oa.example.com/department', 0, 0, '13800138004', 'admin', '2023-01-04 10:00:00', 'admin', '2023-01-04 10:00:00');
INSERT INTO `modules` VALUES (5, '审批管理模块', '审批管理模块', 'http://oa.example.com/approval', 0, 0, '13800138005', 'admin', '2023-01-05 10:00:00', 'admin', '2023-01-05 10:00:00');
INSERT INTO `modules` VALUES (6, '文件管理模块', '文件管理模块', 'http://oa.example.com/file', 0, 0, '13800138006', 'admin', '2023-01-06 10:00:00', 'admin', '2023-01-06 10:00:00');
INSERT INTO `modules` VALUES (7, '项目管理模块', '项目管理模块', 'http://oa.example.com/project', 0, 0, '13800138007', 'admin', '2023-01-07 10:00:00', 'admin', '2023-01-07 10:00:00');
INSERT INTO `modules` VALUES (8, '任务管理模块', '任务管理模块', 'http://oa.example.com/task', 0, 0, '13800138008', 'admin', '2023-01-08 10:00:00', 'admin', '2023-01-08 10:00:00');
INSERT INTO `modules` VALUES (9, '日程管理模块', '日程管理模块', 'http://oa.example.com/schedule', 0, 0, '13800138009', 'admin', '2023-01-09 10:00:00', 'admin', '2023-01-09 10:00:00');
INSERT INTO `modules` VALUES (10, '公告管理模块', '公告管理模块', 'http://oa.example.com/notice', 0, 0, '13800138010', 'admin', '2023-01-10 10:00:00', 'admin', '2023-01-10 10:00:00');
INSERT INTO `modules` VALUES (11, '考勤管理模块', '考勤管理模块', 'http://oa.example.com/attendance', 0, 0, '13800138011', 'admin', '2023-01-11 10:00:00', 'admin', '2023-01-11 10:00:00');
INSERT INTO `modules` VALUES (12, '薪资管理模块', '薪资管理模块', 'http://oa.example.com/salary', 0, 0, '***********', 'admin', '2023-01-12 10:00:00', 'admin', '2023-01-12 10:00:00');
INSERT INTO `modules` VALUES (13, '系统设置模块', '系统设置模块', 'http://oa.example.com/setting', 0, 0, '***********', 'admin', '2023-01-13 10:00:00', 'admin', '2025-06-23 14:13:35');
INSERT INTO `modules` VALUES (14, 'test', '这是一个测试模块并没有实际的作用', '//www.baidu.com', 3, 1, '13666666666', 'admin', '2025-06-18 09:58:40', 'admin', '2025-06-27 21:29:19');
INSERT INTO `modules` VALUES (15, 'test', '这是一个测试模块并没有实际的作用', '//www.baidu.com', 1, 0, '13666666666', 'admin', '2025-06-18 10:18:16', 'admin', '2025-06-27 18:01:29');
INSERT INTO `modules` VALUES (16, 'test', '这是一个测试模块并没有实际的作用', '/test', 17, 1, '13666666666', 'admin', '2025-06-18 15:47:47', 'admin', '2025-07-03 15:29:36');
INSERT INTO `modules` VALUES (17, 'test', '这是一个测试模块并没有实际的作用', '//www.baidu.com', 1, 1, '13666666666', 'admin', '2025-06-18 15:51:59', 'admin', '2025-06-27 20:24:53');
INSERT INTO `modules` VALUES (18, '测试模块', '测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用测试模块并无实际的作用', 'www.baidu,com', 2, 1, '13800138001', 'admin', '2025-06-18 16:56:51', 'admin', '2025-06-27 21:39:29');
INSERT INTO `modules` VALUES (19, 'test', '这是一个测试模块并没有实际的作用', '//www.baidu.com', 1, 1, '13666666666', 'admin', '2025-06-23 11:01:10', NULL, NULL);
INSERT INTO `modules` VALUES (20, 'test', '这是一个测试模块并没有实际的作用', '//www.baidu.com', 1, 0, '13666666666', 'admin', '2025-06-23 11:03:48', NULL, NULL);
INSERT INTO `modules` VALUES (21, 'test', '这是一个测试模块并没有实际的作用', '//www.baidu.com', 1, 0, '13666666666', 'admin', '2025-06-23 11:05:09', NULL, NULL);
INSERT INTO `modules` VALUES (22, 'test', '这是一个测试模块并没有实际的作用', '//www.baidu.com', 1, 0, '13666666666', 'admin', '2025-06-23 11:12:08', NULL, NULL);
INSERT INTO `modules` VALUES (23, '测试', '', 'test', 16, 0, '13800138001', 'admin', '2025-06-23 11:21:17', 'admin', '2025-07-03 15:33:33');
INSERT INTO `modules` VALUES (24, '详情', '', 'www.baidu.com', 23, 1, '13800138001', 'admin', '2025-06-27 15:42:24', 'admin', '2025-06-27 16:32:26');
INSERT INTO `modules` VALUES (25, '详情', '', 'www.baidu.com', 4, 1, '13800138001', 'admin', '2025-06-27 20:38:31', 'admin', '2025-06-27 20:38:39');

-- ----------------------------
-- Table structure for modules_apps
-- ----------------------------
DROP TABLE IF EXISTS `modules_apps`;
CREATE TABLE `modules_apps`  (
  `module_id` bigint NOT NULL,
  `app_id` bigint NOT NULL,
  PRIMARY KEY (`module_id`, `app_id`) USING BTREE,
  INDEX `fk_module_app_app_id`(`app_id` ASC, `module_id` ASC) USING BTREE,
  CONSTRAINT `fk_module_app_app_id` FOREIGN KEY (`app_id`) REFERENCES `apps` (`app_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_module_app_module_id` FOREIGN KEY (`module_id`) REFERENCES `modules` (`module_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of modules_apps
-- ----------------------------
INSERT INTO `modules_apps` VALUES (1, 1);
INSERT INTO `modules_apps` VALUES (1, 2);
INSERT INTO `modules_apps` VALUES (1, 3);
INSERT INTO `modules_apps` VALUES (1, 4);
INSERT INTO `modules_apps` VALUES (1, 5);
INSERT INTO `modules_apps` VALUES (1, 6);
INSERT INTO `modules_apps` VALUES (1, 7);
INSERT INTO `modules_apps` VALUES (1, 8);
INSERT INTO `modules_apps` VALUES (1, 9);
INSERT INTO `modules_apps` VALUES (1, 10);
INSERT INTO `modules_apps` VALUES (1, 11);
INSERT INTO `modules_apps` VALUES (1, 12);
INSERT INTO `modules_apps` VALUES (1, 13);
INSERT INTO `modules_apps` VALUES (2, 1);
INSERT INTO `modules_apps` VALUES (2, 2);
INSERT INTO `modules_apps` VALUES (2, 3);
INSERT INTO `modules_apps` VALUES (2, 4);
INSERT INTO `modules_apps` VALUES (2, 5);
INSERT INTO `modules_apps` VALUES (2, 6);
INSERT INTO `modules_apps` VALUES (2, 7);
INSERT INTO `modules_apps` VALUES (2, 8);
INSERT INTO `modules_apps` VALUES (2, 9);
INSERT INTO `modules_apps` VALUES (2, 10);
INSERT INTO `modules_apps` VALUES (2, 11);
INSERT INTO `modules_apps` VALUES (2, 12);
INSERT INTO `modules_apps` VALUES (3, 1);
INSERT INTO `modules_apps` VALUES (3, 2);
INSERT INTO `modules_apps` VALUES (3, 3);
INSERT INTO `modules_apps` VALUES (3, 4);
INSERT INTO `modules_apps` VALUES (3, 5);
INSERT INTO `modules_apps` VALUES (3, 6);
INSERT INTO `modules_apps` VALUES (3, 7);
INSERT INTO `modules_apps` VALUES (3, 8);
INSERT INTO `modules_apps` VALUES (3, 9);
INSERT INTO `modules_apps` VALUES (3, 10);
INSERT INTO `modules_apps` VALUES (3, 11);
INSERT INTO `modules_apps` VALUES (3, 12);
INSERT INTO `modules_apps` VALUES (3, 13);
INSERT INTO `modules_apps` VALUES (4, 1);
INSERT INTO `modules_apps` VALUES (4, 2);
INSERT INTO `modules_apps` VALUES (4, 3);
INSERT INTO `modules_apps` VALUES (4, 4);
INSERT INTO `modules_apps` VALUES (4, 5);
INSERT INTO `modules_apps` VALUES (4, 6);
INSERT INTO `modules_apps` VALUES (4, 7);
INSERT INTO `modules_apps` VALUES (4, 8);
INSERT INTO `modules_apps` VALUES (4, 9);
INSERT INTO `modules_apps` VALUES (4, 10);
INSERT INTO `modules_apps` VALUES (4, 11);
INSERT INTO `modules_apps` VALUES (4, 12);
INSERT INTO `modules_apps` VALUES (4, 13);
INSERT INTO `modules_apps` VALUES (5, 1);
INSERT INTO `modules_apps` VALUES (5, 2);
INSERT INTO `modules_apps` VALUES (5, 3);
INSERT INTO `modules_apps` VALUES (5, 4);
INSERT INTO `modules_apps` VALUES (5, 5);
INSERT INTO `modules_apps` VALUES (5, 6);
INSERT INTO `modules_apps` VALUES (5, 7);
INSERT INTO `modules_apps` VALUES (5, 8);
INSERT INTO `modules_apps` VALUES (5, 9);
INSERT INTO `modules_apps` VALUES (5, 10);
INSERT INTO `modules_apps` VALUES (5, 11);
INSERT INTO `modules_apps` VALUES (5, 12);
INSERT INTO `modules_apps` VALUES (5, 13);
INSERT INTO `modules_apps` VALUES (6, 1);
INSERT INTO `modules_apps` VALUES (7, 1);
INSERT INTO `modules_apps` VALUES (8, 1);
INSERT INTO `modules_apps` VALUES (9, 1);
INSERT INTO `modules_apps` VALUES (10, 1);
INSERT INTO `modules_apps` VALUES (11, 1);
INSERT INTO `modules_apps` VALUES (12, 1);
INSERT INTO `modules_apps` VALUES (13, 1);
INSERT INTO `modules_apps` VALUES (14, 1);
INSERT INTO `modules_apps` VALUES (15, 1);
INSERT INTO `modules_apps` VALUES (16, 1);
INSERT INTO `modules_apps` VALUES (17, 1);
INSERT INTO `modules_apps` VALUES (18, 1);
INSERT INTO `modules_apps` VALUES (19, 1);
INSERT INTO `modules_apps` VALUES (20, 1);
INSERT INTO `modules_apps` VALUES (21, 1);
INSERT INTO `modules_apps` VALUES (22, 1);
INSERT INTO `modules_apps` VALUES (23, 1);
INSERT INTO `modules_apps` VALUES (24, 1);
INSERT INTO `modules_apps` VALUES (25, 1);

-- ----------------------------
-- Table structure for modules_authorities
-- ----------------------------
DROP TABLE IF EXISTS `modules_authorities`;
CREATE TABLE `modules_authorities`  (
  `authority_id` bigint NOT NULL,
  `module_id` bigint NOT NULL,
  PRIMARY KEY (`authority_id`, `module_id`) USING BTREE,
  INDEX `FK2sfnl3otmu97rw6ecdtyu7oqf`(`module_id` ASC) USING BTREE,
  CONSTRAINT `FK2sfnl3otmu97rw6ecdtyu7oqf` FOREIGN KEY (`module_id`) REFERENCES `modules` (`module_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKsrng59yke20ahcm4qv7bd4ttn` FOREIGN KEY (`authority_id`) REFERENCES `authorities` (`authority_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of modules_authorities
-- ----------------------------
INSERT INTO `modules_authorities` VALUES (40, 1);
INSERT INTO `modules_authorities` VALUES (41, 1);
INSERT INTO `modules_authorities` VALUES (42, 1);
INSERT INTO `modules_authorities` VALUES (43, 1);
INSERT INTO `modules_authorities` VALUES (44, 1);
INSERT INTO `modules_authorities` VALUES (45, 1);
INSERT INTO `modules_authorities` VALUES (46, 1);
INSERT INTO `modules_authorities` VALUES (47, 1);
INSERT INTO `modules_authorities` VALUES (48, 1);
INSERT INTO `modules_authorities` VALUES (49, 1);
INSERT INTO `modules_authorities` VALUES (50, 1);
INSERT INTO `modules_authorities` VALUES (40, 2);

-- ----------------------------
-- Table structure for policies
-- ----------------------------
DROP TABLE IF EXISTS `policies`;
CREATE TABLE `policies`  (
  `policy_id` bigint NOT NULL AUTO_INCREMENT,
  `policy_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `policy_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `del_flag` int NOT NULL DEFAULT 0,
  `type` int NOT NULL,
  `creat_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `creat_time` datetime NOT NULL,
  `update_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `update_time` datetime NOT NULL,
  PRIMARY KEY (`policy_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 14 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of policies
-- ----------------------------
INSERT INTO `policies` VALUES (1, 'Admin_Full_Access', '管理员对所有资源具有完全访问权限', 0, 1, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16');
INSERT INTO `policies` VALUES (2, 'User_Read_Only', '用户对特定资源具有只读访问权限', 0, 1, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16');
INSERT INTO `policies` VALUES (3, 'HR_Department_Read_Write', '人力资源部门可以读取和写入员工信息', 0, 1, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16');
INSERT INTO `policies` VALUES (4, 'Finance_Department_Full_Access', '财务部门对所有财务报告具有完全访问权限', 0, 1, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16');
INSERT INTO `policies` VALUES (5, 'Project_Team_Read_Write', '项目团队可以读取和写入项目文档', 0, 1, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16');
INSERT INTO `policies` VALUES (6, 'Temporary_Access', '特定用户或角色的临时访问权限', 0, 1, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16');
INSERT INTO `policies` VALUES (7, 'Restricted_Area_Access', '受限区域仅允许特定用户组访问', 0, 1, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16');
INSERT INTO `policies` VALUES (8, 'Log_Viewing_Permission', '查看系统日志的权限', 0, 1, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16');
INSERT INTO `policies` VALUES (9, 'API_Sync_Policy', 'API同步策略', 0, 2, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16');
INSERT INTO `policies` VALUES (10, 'Custom_Policy_1', '自定义策略描述1', 0, 3, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16');
INSERT INTO `policies` VALUES (11, 'Custom_Policy_2', '自定义策略描述2', 0, 3, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16');
INSERT INTO `policies` VALUES (12, 'Custom_Policy_3', '自定义策略描述3', 0, 3, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16');
INSERT INTO `policies` VALUES (13, 'Custom_Policy_4', '自定义策略描述4', 0, 3, 'admin', '2025-05-29 09:52:16', 'admin', '2025-05-29 09:52:16');

-- ----------------------------
-- Table structure for policies_apps
-- ----------------------------
DROP TABLE IF EXISTS `policies_apps`;
CREATE TABLE `policies_apps`  (
  `app_id` bigint NOT NULL,
  `policy_id` bigint NOT NULL,
  PRIMARY KEY (`app_id`, `policy_id`) USING BTREE,
  INDEX `FKkg3s5yneewenhf2e23wnpqrjr`(`policy_id` ASC) USING BTREE,
  CONSTRAINT `FKceh3nebwei2n8nysg4q1aqy9m` FOREIGN KEY (`app_id`) REFERENCES `apps` (`app_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKkg3s5yneewenhf2e23wnpqrjr` FOREIGN KEY (`policy_id`) REFERENCES `policies` (`policy_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of policies_apps
-- ----------------------------

-- ----------------------------
-- Table structure for policies_roles
-- ----------------------------
DROP TABLE IF EXISTS `policies_roles`;
CREATE TABLE `policies_roles`  (
  `policy_id` bigint NOT NULL,
  `role_id` bigint NOT NULL,
  PRIMARY KEY (`policy_id`, `role_id`) USING BTREE,
  INDEX `role_id`(`role_id` ASC) USING BTREE,
  CONSTRAINT `policies_roles_ibfk_1` FOREIGN KEY (`policy_id`) REFERENCES `policies` (`policy_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `policies_roles_ibfk_2` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of policies_roles
-- ----------------------------
INSERT INTO `policies_roles` VALUES (1, 1);
INSERT INTO `policies_roles` VALUES (9, 1);
INSERT INTO `policies_roles` VALUES (10, 1);
INSERT INTO `policies_roles` VALUES (11, 1);
INSERT INTO `policies_roles` VALUES (12, 1);
INSERT INTO `policies_roles` VALUES (13, 1);
INSERT INTO `policies_roles` VALUES (2, 2);
INSERT INTO `policies_roles` VALUES (3, 3);
INSERT INTO `policies_roles` VALUES (4, 4);
INSERT INTO `policies_roles` VALUES (5, 5);
INSERT INTO `policies_roles` VALUES (6, 6);
INSERT INTO `policies_roles` VALUES (7, 7);
INSERT INTO `policies_roles` VALUES (8, 8);
INSERT INTO `policies_roles` VALUES (2, 9);

-- ----------------------------
-- Table structure for roles
-- ----------------------------
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles`  (
  `role_id` bigint NOT NULL AUTO_INCREMENT,
  `role_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `role_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `role_description` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `del_flag` int NULL DEFAULT 0,
  `creat_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creat_time` datetime NULL DEFAULT NULL,
  `update_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `parent_role_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `role_level` bigint NULL DEFAULT NULL,
  `source_system` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `resource_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `business_role_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `parent_role_id` bigint NULL DEFAULT NULL,
  PRIMARY KEY (`role_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 28 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of roles
-- ----------------------------
INSERT INTO `roles` VALUES (1, 'QXGL_ROLE1', '系统管理员', '拥有所有权限', 0, 'admin', '2023-01-01 10:00:00', 'SYSTEM_IMPORT', '2025-07-18 16:14:35', '', 10, NULL, NULL, NULL, NULL);
INSERT INTO `roles` VALUES (2, 'QXGL_ROLE2', '部门经理', '管理部门权限', 0, 'admin', '2023-01-01 10:00:00', 'SYSTEM_IMPORT', '2025-07-18 16:14:35', 'QXGL_ROLE1', 2, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (3, 'QXGL_ROLE3', '普通员工', '基础权限', 0, 'admin', '2023-01-01 10:00:00', 'SYSTEM_IMPORT', '2025-07-18 10:35:48', 'QXGL_ROLE2', 3, NULL, NULL, NULL, 2);
INSERT INTO `roles` VALUES (4, 'QXGL_ROLE4', '项目经理', '项目管理负责人，拥有项目管理权限', 0, 'admin', '2023-01-04 10:00:00', 'system', '2025-06-26 10:35:24', 'QXGL_ROLE2', 3, NULL, NULL, NULL, 2);
INSERT INTO `roles` VALUES (5, 'QXGL_ROLE5', '财务专员', '财务工作人员，拥有财务相关权限', 0, 'admin', '2023-01-05 10:00:00', 'system', '2025-06-26 10:38:03', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (6, 'QXGL_ROLE6', '人事专员', '人事工作人员，拥有人事相关权限', 0, 'admin', '2023-01-06 10:00:00', 'system', '2025-06-27 14:34:59', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (7, 'QXGL_ROLE7', '市场专员', '市场工作人员，拥有市场相关权限', 0, 'admin', '2023-01-07 10:00:00', 'system', '2025-07-07 15:04:01', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (8, 'QXGL_ROLE8', '销售代表', '销售人员，拥有销售相关权限', 0, 'admin', '2023-01-08 10:00:00', 'system', '2025-06-18 15:51:56', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (9, 'QXGL_ROLE9', '客服代表', '客服人员，拥有客服相关权限', 0, 'admin', '2023-01-09 10:00:00', 'admin', '2025-06-18 15:51:56', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (10, 'QXGL_ROLE10', '采购专员', '采购人员，拥有采购相关权限', 0, 'admin', '2023-01-10 10:00:00', 'admin', '2025-06-27 11:33:41', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (11, 'QXGL_ROLE11', '仓储管理员', '仓储管理人员，拥有仓储相关权限', 0, 'admin', '2023-01-11 10:00:00', 'admin', '2023-01-11 10:00:00', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (12, 'QXGL_ROLE12', '质检员', '质量检测人员，拥有质检相关权限', 0, 'admin', '2023-01-12 10:00:00', 'admin', '2023-01-12 10:00:00', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (13, 'QXGL_ROLE13', '系统维护员', '系统维护人员，拥有系统维护权限，系统维护人员，拥有系统维护权限，系统维护人员，拥有系统维护权限，系统维护人员，拥有系统维护权限', 0, 'admin', '2023-01-13 10:00:00', 'admin', '2023-01-13 10:00:00', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (14, 'QXGL_ROLE14', 'string', 'string', 0, 'system', '2025-06-04 11:29:33', 'system', '2025-06-04 11:30:12', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (15, 'QXGL_ROLE15', '自定义角色', '无', 0, 'system', '2025-06-04 14:09:06', 'system', '2025-06-04 14:19:37', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (16, 'QXGL_ROLE16', '自定义2', '无', 0, 'system', '2025-06-04 14:20:19', 'system', '2025-06-04 14:43:48', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (17, 'QXGL_ROLE17', '自定义3', '五', 0, 'system', '2025-06-04 14:44:18', 'system', '2025-06-04 14:44:18', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (18, 'QXGL_ROLE18', '自定义角色4', '无', 0, 'system', '2025-06-04 15:43:34', 'system', '2025-06-04 15:43:34', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (19, 'QXGL_ROLE19', '自定义角色5', 'string', 0, 'system', '2025-06-05 12:38:25', 'system', '2025-06-05 12:38:25', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (20, 'QXGL_ROLE20', '自定义角色6', '', 0, 'system', '2025-06-05 12:38:43', 'system', '2025-06-05 12:38:43', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (21, 'QXGL_ROLE21', '自定义角色61', '', 0, 'system', '2025-06-05 12:42:43', 'system', '2025-06-05 14:10:56', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (22, 'QXGL_ROLE22', '自定义角色8', '', 0, 'system', '2025-06-05 14:22:33', 'system', '2025-06-05 14:22:33', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (23, 'QXGL_ROLE23', '自定义1', '', 0, 'system', '2025-06-05 14:45:49', 'system', '2025-06-06 09:51:04', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (24, 'QXGL_ROLE24', '管理员', '测试', 0, 'system', '2025-07-07 16:35:29', 'system', '2025-07-07 16:35:29', 'QXGL_ROLE1', 1, NULL, NULL, NULL, 1);
INSERT INTO `roles` VALUES (27, 'QXGL_ROLE25', '普通员工', '基础权限', 0, 'SYSTEM_IMPORT', '2025-07-18 10:49:00', 'SYSTEM_IMPORT', '2025-07-18 16:14:35', 'QXGL_ROLE2', 3, 'BUSINESS_SYSTEM', 'QXGL', NULL, 2);

-- ----------------------------
-- Table structure for roles_apps
-- ----------------------------
DROP TABLE IF EXISTS `roles_apps`;
CREATE TABLE `roles_apps`  (
  `role_id` bigint NOT NULL,
  `app_id` bigint NOT NULL,
  PRIMARY KEY (`role_id`, `app_id`) USING BTREE,
  INDEX `fk_role_app_app_id`(`app_id` ASC) USING BTREE,
  CONSTRAINT `fk_role_app_app_id` FOREIGN KEY (`app_id`) REFERENCES `apps` (`app_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_role_app_role_id` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of roles_apps
-- ----------------------------
INSERT INTO `roles_apps` VALUES (1, 1);
INSERT INTO `roles_apps` VALUES (2, 1);
INSERT INTO `roles_apps` VALUES (3, 1);
INSERT INTO `roles_apps` VALUES (4, 1);
INSERT INTO `roles_apps` VALUES (5, 1);
INSERT INTO `roles_apps` VALUES (1, 2);
INSERT INTO `roles_apps` VALUES (2, 2);
INSERT INTO `roles_apps` VALUES (3, 2);
INSERT INTO `roles_apps` VALUES (4, 2);
INSERT INTO `roles_apps` VALUES (5, 2);
INSERT INTO `roles_apps` VALUES (1, 3);
INSERT INTO `roles_apps` VALUES (2, 3);
INSERT INTO `roles_apps` VALUES (3, 3);
INSERT INTO `roles_apps` VALUES (4, 3);
INSERT INTO `roles_apps` VALUES (5, 3);
INSERT INTO `roles_apps` VALUES (1, 4);
INSERT INTO `roles_apps` VALUES (2, 4);
INSERT INTO `roles_apps` VALUES (3, 4);
INSERT INTO `roles_apps` VALUES (4, 4);
INSERT INTO `roles_apps` VALUES (5, 4);
INSERT INTO `roles_apps` VALUES (1, 5);
INSERT INTO `roles_apps` VALUES (2, 5);
INSERT INTO `roles_apps` VALUES (3, 5);
INSERT INTO `roles_apps` VALUES (4, 5);
INSERT INTO `roles_apps` VALUES (5, 5);
INSERT INTO `roles_apps` VALUES (1, 6);
INSERT INTO `roles_apps` VALUES (2, 6);
INSERT INTO `roles_apps` VALUES (3, 6);
INSERT INTO `roles_apps` VALUES (4, 6);
INSERT INTO `roles_apps` VALUES (5, 6);
INSERT INTO `roles_apps` VALUES (6, 6);
INSERT INTO `roles_apps` VALUES (1, 7);
INSERT INTO `roles_apps` VALUES (2, 7);
INSERT INTO `roles_apps` VALUES (3, 7);
INSERT INTO `roles_apps` VALUES (4, 7);
INSERT INTO `roles_apps` VALUES (5, 7);
INSERT INTO `roles_apps` VALUES (7, 7);
INSERT INTO `roles_apps` VALUES (1, 8);
INSERT INTO `roles_apps` VALUES (2, 8);
INSERT INTO `roles_apps` VALUES (3, 8);
INSERT INTO `roles_apps` VALUES (4, 8);
INSERT INTO `roles_apps` VALUES (5, 8);
INSERT INTO `roles_apps` VALUES (8, 8);
INSERT INTO `roles_apps` VALUES (1, 9);
INSERT INTO `roles_apps` VALUES (2, 9);
INSERT INTO `roles_apps` VALUES (3, 9);
INSERT INTO `roles_apps` VALUES (4, 9);
INSERT INTO `roles_apps` VALUES (5, 9);
INSERT INTO `roles_apps` VALUES (9, 9);
INSERT INTO `roles_apps` VALUES (1, 10);
INSERT INTO `roles_apps` VALUES (2, 10);
INSERT INTO `roles_apps` VALUES (3, 10);
INSERT INTO `roles_apps` VALUES (4, 10);
INSERT INTO `roles_apps` VALUES (5, 10);
INSERT INTO `roles_apps` VALUES (10, 10);
INSERT INTO `roles_apps` VALUES (1, 11);
INSERT INTO `roles_apps` VALUES (2, 11);
INSERT INTO `roles_apps` VALUES (3, 11);
INSERT INTO `roles_apps` VALUES (4, 11);
INSERT INTO `roles_apps` VALUES (5, 11);
INSERT INTO `roles_apps` VALUES (11, 11);
INSERT INTO `roles_apps` VALUES (1, 12);
INSERT INTO `roles_apps` VALUES (2, 12);
INSERT INTO `roles_apps` VALUES (3, 12);
INSERT INTO `roles_apps` VALUES (4, 12);
INSERT INTO `roles_apps` VALUES (5, 12);
INSERT INTO `roles_apps` VALUES (12, 12);
INSERT INTO `roles_apps` VALUES (1, 13);
INSERT INTO `roles_apps` VALUES (3, 13);
INSERT INTO `roles_apps` VALUES (4, 13);
INSERT INTO `roles_apps` VALUES (5, 13);
INSERT INTO `roles_apps` VALUES (13, 13);
INSERT INTO `roles_apps` VALUES (14, 14);
INSERT INTO `roles_apps` VALUES (15, 15);
INSERT INTO `roles_apps` VALUES (16, 16);
INSERT INTO `roles_apps` VALUES (17, 17);
INSERT INTO `roles_apps` VALUES (18, 18);
INSERT INTO `roles_apps` VALUES (19, 19);
INSERT INTO `roles_apps` VALUES (20, 20);
INSERT INTO `roles_apps` VALUES (21, 21);
INSERT INTO `roles_apps` VALUES (22, 22);
INSERT INTO `roles_apps` VALUES (23, 23);
INSERT INTO `roles_apps` VALUES (24, 24);
INSERT INTO `roles_apps` VALUES (1, 29);
INSERT INTO `roles_apps` VALUES (2, 29);
INSERT INTO `roles_apps` VALUES (3, 29);
INSERT INTO `roles_apps` VALUES (4, 29);
INSERT INTO `roles_apps` VALUES (27, 29);

-- ----------------------------
-- Table structure for roles_authorities
-- ----------------------------
DROP TABLE IF EXISTS `roles_authorities`;
CREATE TABLE `roles_authorities`  (
  `authority_id` bigint NOT NULL,
  `role_id` bigint NOT NULL,
  PRIMARY KEY (`authority_id`, `role_id`) USING BTREE,
  INDEX `FKq3iqpff34tgtkvnn545a648cb`(`role_id` ASC) USING BTREE,
  CONSTRAINT `FKq3iqpff34tgtkvnn545a648cb` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `FKt69njxcgfcto5wcrd9ocmb35h` FOREIGN KEY (`authority_id`) REFERENCES `authorities` (`authority_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of roles_authorities
-- ----------------------------
INSERT INTO `roles_authorities` VALUES (40, 1);
INSERT INTO `roles_authorities` VALUES (41, 1);
INSERT INTO `roles_authorities` VALUES (40, 2);
INSERT INTO `roles_authorities` VALUES (40, 3);

-- ----------------------------
-- Table structure for roles_modules
-- ----------------------------
DROP TABLE IF EXISTS `roles_modules`;
CREATE TABLE `roles_modules`  (
  `role_id` bigint NOT NULL,
  `module_id` bigint NOT NULL,
  PRIMARY KEY (`role_id`, `module_id`) USING BTREE,
  INDEX `fk_role_module_module_id`(`module_id` ASC) USING BTREE,
  CONSTRAINT `fk_role_module_module_id` FOREIGN KEY (`module_id`) REFERENCES `modules` (`module_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_role_module_role_id` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of roles_modules
-- ----------------------------
INSERT INTO `roles_modules` VALUES (1, 1);
INSERT INTO `roles_modules` VALUES (2, 1);
INSERT INTO `roles_modules` VALUES (1, 2);
INSERT INTO `roles_modules` VALUES (2, 2);
INSERT INTO `roles_modules` VALUES (1, 3);
INSERT INTO `roles_modules` VALUES (3, 3);
INSERT INTO `roles_modules` VALUES (4, 4);
INSERT INTO `roles_modules` VALUES (5, 5);
INSERT INTO `roles_modules` VALUES (6, 6);
INSERT INTO `roles_modules` VALUES (7, 7);
INSERT INTO `roles_modules` VALUES (8, 8);
INSERT INTO `roles_modules` VALUES (9, 9);
INSERT INTO `roles_modules` VALUES (10, 10);
INSERT INTO `roles_modules` VALUES (11, 11);
INSERT INTO `roles_modules` VALUES (12, 12);
INSERT INTO `roles_modules` VALUES (13, 13);

-- ----------------------------
-- Table structure for users
-- ----------------------------
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users`  (
  `user_id` bigint NOT NULL AUTO_INCREMENT,
  `name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  `email` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `phone` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `sex` int NULL DEFAULT NULL,
  `del_flag` int NULL DEFAULT 0,
  `creat_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `creat_time` datetime NOT NULL,
  `update_user` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `update_time` datetime NULL DEFAULT NULL,
  `source_system` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `resource_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `user_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `auth_user_code` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NULL DEFAULT NULL,
  `key_id` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL,
  PRIMARY KEY (`user_id`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 25 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of users
-- ----------------------------
INSERT INTO `users` VALUES (1, '张三1', 'Abcd1234!', '<EMAIL>', '1554377261', 1, 0, 'admin', '2023-01-01 10:00:00', 'SYSTEM_IMPORT', '2025-07-18 16:15:56', NULL, NULL, 'QXGL_USER1', NULL, 'KEY001');
INSERT INTO `users` VALUES (2, '李四', 'Bcde2345!', '<EMAIL>', '1554377262', 0, 0, 'admin', '2023-01-02 10:00:00', 'SYSTEM_IMPORT', '2025-07-18 16:15:56', NULL, NULL, 'QXGL_USER2', NULL, 'KEY002');
INSERT INTO `users` VALUES (3, '王五', 'password123', '<EMAIL>', '13800138003', 1, 0, 'admin', '2023-01-01 10:00:00', 'admin', '2023-01-01 10:00:00', NULL, NULL, 'QXGL_USER3', NULL, 'KEY003');
INSERT INTO `users` VALUES (4, '赵六', 'password123', '<EMAIL>', '13800138004', 1, 0, 'admin', '2023-01-04 10:00:00', 'admin', '2025-06-19 10:06:35', NULL, NULL, 'QXGL_USER4', NULL, 'KEY004');
INSERT INTO `users` VALUES (5, '钱七', 'password123', '<EMAIL>', '13800138005', 1, 0, 'admin', '2023-01-05 10:00:00', 'admin', '2025-06-19 10:06:35', NULL, NULL, 'QXGL_USER5', NULL, 'KEY005');
INSERT INTO `users` VALUES (6, '孙八', 'password123', '<EMAIL>', '13800138006', 1, 0, 'admin', '2023-01-06 10:00:00', 'admin', '2025-06-19 10:06:35', NULL, NULL, 'QXGL_USER6', NULL, 'KEY006');
INSERT INTO `users` VALUES (7, '周九', 'password123', '<EMAIL>', '13800138007', 1, 0, 'admin', '2023-01-07 10:00:00', 'admin', '2025-06-19 10:06:35', NULL, NULL, 'QXGL_USER7', NULL, 'KEY007');
INSERT INTO `users` VALUES (8, '吴十', 'password123', '<EMAIL>', '13800138008', 1, 0, 'admin', '2023-01-08 10:00:00', 'admin', '2025-07-07 14:25:50', NULL, NULL, 'QXGL_USER8', NULL, 'KEY008');
INSERT INTO `users` VALUES (9, '郑十一', 'password123', '<EMAIL>', '13800138009', 1, 0, 'admin', '2023-01-09 10:00:00', 'admin', '2025-06-19 10:06:35', NULL, NULL, 'QXGL_USER9', NULL, 'KEY009');
INSERT INTO `users` VALUES (10, '王十二', 'password123', '<EMAIL>', '13800138010', 1, 0, 'admin', '2023-01-10 10:00:00', 'admin', '2025-07-04 16:28:22', NULL, NULL, 'QXGL_USER10', NULL, 'KEY010');
INSERT INTO `users` VALUES (11, '冯十三', 'password123', '<EMAIL>', '13800138011', 1, 0, 'admin', '2023-01-11 10:00:00', 'admin', '2025-07-01 16:58:13', NULL, NULL, 'QXGL_USER11', NULL, 'KEY011');
INSERT INTO `users` VALUES (12, '陈十四', 'password123', '<EMAIL>', '***********', 1, 0, 'admin', '2023-01-12 10:00:00', 'admin', '2025-07-01 16:58:12', NULL, NULL, 'QXGL_USER12', NULL, 'KEY012');
INSERT INTO `users` VALUES (13, '褚十五', 'password123', '<EMAIL>', '***********', 1, 0, 'admin', '2023-01-13 10:00:00', 'admin', '2025-07-15 18:27:43', NULL, NULL, 'QXGL_USER13', NULL, 'KEY013');
INSERT INTO `users` VALUES (14, '李四', 'Abcd1234!', '<EMAIL>', '2147483647', 1, 0, 'SYSTEM_IMPORT', '2025-07-16 10:48:08', 'SYSTEM_IMPORT', '2025-07-16 17:22:15', 'BUSINESS_SYSTEM', 'QXGL', 'QXGL_USER14', '73cfeb07-bd57-41bd-8ad6-959df239c768', '550e8400-e29b-41d4-a716-446655440000');
INSERT INTO `users` VALUES (18, '王五', 'Abcd1234!', '<EMAIL>', '2147483647', 1, 0, 'SYSTEM_IMPORT', '2025-07-16 16:11:13', 'SYSTEM_IMPORT', '2025-07-16 17:22:15', 'BUSINESS_SYSTEM', '百度', 'QXGL_USER18', '09822d1f-a28d-4138-ab32-f895be5e4451', '550e8400-e29b-41d4-a716-446655440011');
INSERT INTO `users` VALUES (19, '赵六', 'Abcd1234!', '<EMAIL>', '2147483647', 1, 0, 'SYSTEM_IMPORT', '2025-07-16 16:11:13', 'SYSTEM_IMPORT', '2025-07-16 17:22:15', 'BUSINESS_SYSTEM', '百度', 'QXGL_USER19', '748ff231-977f-495a-9812-a2ed03641dfd', '550e8400-e29b-41d4-a716-446655440111');
INSERT INTO `users` VALUES (23, '张三', 'Abcd1234!', '<EMAIL>', '1554377261', 1, 0, 'SYSTEM_IMPORT', '2025-07-17 09:57:15', 'SYSTEM_IMPORT', '2025-07-17 14:44:55', 'BUSINESS_SYSTEM', 'QXGL', 'QXGL01', '45ccb8ea-2ec5-41fe-aac0-1e444e90d9f5', '550e8400-e29b-41d4-a716-446655440000');
INSERT INTO `users` VALUES (24, '张三', 'Abcd1234!', '<EMAIL>', '1554377261', 1, 0, 'SYSTEM_IMPORT', '2025-07-18 14:50:29', 'SYSTEM_IMPORT', '2025-07-18 14:50:29', 'BUSINESS_SYSTEM', 'QXGL', 'SFRZ_USER1', '017e5ab6-453c-4792-a654-bb256239301e', '550e8400-e29b-41d4-a716-446655440000');

-- ----------------------------
-- Table structure for users_apps
-- ----------------------------
DROP TABLE IF EXISTS `users_apps`;
CREATE TABLE `users_apps`  (
  `user_id` bigint NOT NULL,
  `app_id` bigint NOT NULL,
  PRIMARY KEY (`user_id`, `app_id`) USING BTREE,
  INDEX `fk_app_id`(`app_id` ASC) USING BTREE,
  CONSTRAINT `fk_app_id` FOREIGN KEY (`app_id`) REFERENCES `apps` (`app_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_user_app_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of users_apps
-- ----------------------------
INSERT INTO `users_apps` VALUES (1, 1);
INSERT INTO `users_apps` VALUES (2, 1);
INSERT INTO `users_apps` VALUES (3, 1);
INSERT INTO `users_apps` VALUES (4, 1);
INSERT INTO `users_apps` VALUES (5, 1);
INSERT INTO `users_apps` VALUES (13, 1);
INSERT INTO `users_apps` VALUES (1, 2);
INSERT INTO `users_apps` VALUES (2, 2);
INSERT INTO `users_apps` VALUES (3, 2);
INSERT INTO `users_apps` VALUES (4, 2);
INSERT INTO `users_apps` VALUES (5, 2);
INSERT INTO `users_apps` VALUES (1, 3);
INSERT INTO `users_apps` VALUES (2, 3);
INSERT INTO `users_apps` VALUES (3, 3);
INSERT INTO `users_apps` VALUES (4, 3);
INSERT INTO `users_apps` VALUES (5, 3);
INSERT INTO `users_apps` VALUES (13, 3);
INSERT INTO `users_apps` VALUES (1, 4);
INSERT INTO `users_apps` VALUES (2, 4);
INSERT INTO `users_apps` VALUES (3, 4);
INSERT INTO `users_apps` VALUES (4, 4);
INSERT INTO `users_apps` VALUES (5, 4);
INSERT INTO `users_apps` VALUES (1, 5);
INSERT INTO `users_apps` VALUES (2, 5);
INSERT INTO `users_apps` VALUES (3, 5);
INSERT INTO `users_apps` VALUES (4, 5);
INSERT INTO `users_apps` VALUES (5, 5);
INSERT INTO `users_apps` VALUES (1, 6);
INSERT INTO `users_apps` VALUES (2, 6);
INSERT INTO `users_apps` VALUES (3, 6);
INSERT INTO `users_apps` VALUES (4, 6);
INSERT INTO `users_apps` VALUES (5, 6);
INSERT INTO `users_apps` VALUES (6, 6);
INSERT INTO `users_apps` VALUES (1, 7);
INSERT INTO `users_apps` VALUES (2, 7);
INSERT INTO `users_apps` VALUES (3, 7);
INSERT INTO `users_apps` VALUES (4, 7);
INSERT INTO `users_apps` VALUES (5, 7);
INSERT INTO `users_apps` VALUES (7, 7);
INSERT INTO `users_apps` VALUES (1, 8);
INSERT INTO `users_apps` VALUES (2, 8);
INSERT INTO `users_apps` VALUES (3, 8);
INSERT INTO `users_apps` VALUES (4, 8);
INSERT INTO `users_apps` VALUES (5, 8);
INSERT INTO `users_apps` VALUES (8, 8);
INSERT INTO `users_apps` VALUES (1, 9);
INSERT INTO `users_apps` VALUES (2, 9);
INSERT INTO `users_apps` VALUES (3, 9);
INSERT INTO `users_apps` VALUES (4, 9);
INSERT INTO `users_apps` VALUES (5, 9);
INSERT INTO `users_apps` VALUES (9, 9);
INSERT INTO `users_apps` VALUES (1, 10);
INSERT INTO `users_apps` VALUES (2, 10);
INSERT INTO `users_apps` VALUES (3, 10);
INSERT INTO `users_apps` VALUES (4, 10);
INSERT INTO `users_apps` VALUES (5, 10);
INSERT INTO `users_apps` VALUES (10, 10);
INSERT INTO `users_apps` VALUES (1, 11);
INSERT INTO `users_apps` VALUES (2, 11);
INSERT INTO `users_apps` VALUES (3, 11);
INSERT INTO `users_apps` VALUES (4, 11);
INSERT INTO `users_apps` VALUES (5, 11);
INSERT INTO `users_apps` VALUES (11, 11);
INSERT INTO `users_apps` VALUES (1, 12);
INSERT INTO `users_apps` VALUES (2, 12);
INSERT INTO `users_apps` VALUES (3, 12);
INSERT INTO `users_apps` VALUES (4, 12);
INSERT INTO `users_apps` VALUES (5, 12);
INSERT INTO `users_apps` VALUES (12, 12);
INSERT INTO `users_apps` VALUES (1, 13);
INSERT INTO `users_apps` VALUES (3, 13);
INSERT INTO `users_apps` VALUES (4, 13);
INSERT INTO `users_apps` VALUES (5, 13);
INSERT INTO `users_apps` VALUES (13, 13);
INSERT INTO `users_apps` VALUES (1, 29);
INSERT INTO `users_apps` VALUES (2, 29);
INSERT INTO `users_apps` VALUES (23, 29);
INSERT INTO `users_apps` VALUES (24, 29);
INSERT INTO `users_apps` VALUES (14, 30);
INSERT INTO `users_apps` VALUES (18, 30);
INSERT INTO `users_apps` VALUES (19, 30);

-- ----------------------------
-- Table structure for users_departments
-- ----------------------------
DROP TABLE IF EXISTS `users_departments`;
CREATE TABLE `users_departments`  (
  `user_id` bigint NOT NULL,
  `department_id` bigint NOT NULL,
  PRIMARY KEY (`user_id`, `department_id`) USING BTREE,
  INDEX `fk_department_id`(`department_id` ASC) USING BTREE,
  CONSTRAINT `fk_department_id` FOREIGN KEY (`department_id`) REFERENCES `departments` (`department_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of users_departments
-- ----------------------------
INSERT INTO `users_departments` VALUES (1, 1);
INSERT INTO `users_departments` VALUES (14, 1);
INSERT INTO `users_departments` VALUES (18, 1);
INSERT INTO `users_departments` VALUES (19, 1);
INSERT INTO `users_departments` VALUES (23, 1);
INSERT INTO `users_departments` VALUES (24, 1);
INSERT INTO `users_departments` VALUES (2, 2);
INSERT INTO `users_departments` VALUES (3, 3);
INSERT INTO `users_departments` VALUES (4, 4);
INSERT INTO `users_departments` VALUES (5, 5);
INSERT INTO `users_departments` VALUES (6, 6);
INSERT INTO `users_departments` VALUES (7, 7);
INSERT INTO `users_departments` VALUES (8, 8);
INSERT INTO `users_departments` VALUES (9, 9);
INSERT INTO `users_departments` VALUES (10, 10);
INSERT INTO `users_departments` VALUES (11, 11);
INSERT INTO `users_departments` VALUES (12, 12);
INSERT INTO `users_departments` VALUES (13, 13);
INSERT INTO `users_departments` VALUES (23, 79);

-- ----------------------------
-- Table structure for users_modules
-- ----------------------------
DROP TABLE IF EXISTS `users_modules`;
CREATE TABLE `users_modules`  (
  `user_id` bigint NOT NULL,
  `module_id` bigint NOT NULL,
  PRIMARY KEY (`user_id`, `module_id`) USING BTREE,
  INDEX `fk_module_id`(`module_id` ASC) USING BTREE,
  CONSTRAINT `fk_module_id` FOREIGN KEY (`module_id`) REFERENCES `modules` (`module_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_user_module_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of users_modules
-- ----------------------------
INSERT INTO `users_modules` VALUES (1, 1);
INSERT INTO `users_modules` VALUES (2, 2);
INSERT INTO `users_modules` VALUES (3, 3);
INSERT INTO `users_modules` VALUES (4, 4);
INSERT INTO `users_modules` VALUES (5, 5);
INSERT INTO `users_modules` VALUES (6, 6);
INSERT INTO `users_modules` VALUES (7, 7);
INSERT INTO `users_modules` VALUES (8, 8);
INSERT INTO `users_modules` VALUES (9, 9);
INSERT INTO `users_modules` VALUES (10, 10);
INSERT INTO `users_modules` VALUES (11, 11);
INSERT INTO `users_modules` VALUES (12, 12);
INSERT INTO `users_modules` VALUES (13, 13);

-- ----------------------------
-- Table structure for users_roles
-- ----------------------------
DROP TABLE IF EXISTS `users_roles`;
CREATE TABLE `users_roles`  (
  `user_id` bigint NOT NULL,
  `role_id` bigint NOT NULL,
  PRIMARY KEY (`user_id`, `role_id`) USING BTREE,
  INDEX `fk_role_id`(`role_id` ASC) USING BTREE,
  CONSTRAINT `fk_role_id` FOREIGN KEY (`role_id`) REFERENCES `roles` (`role_id`) ON DELETE RESTRICT ON UPDATE RESTRICT,
  CONSTRAINT `fk_user_role_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`user_id`) ON DELETE RESTRICT ON UPDATE RESTRICT
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = DYNAMIC;

-- ----------------------------
-- Records of users_roles
-- ----------------------------
INSERT INTO `users_roles` VALUES (1, 1);
INSERT INTO `users_roles` VALUES (2, 1);
INSERT INTO `users_roles` VALUES (23, 1);
INSERT INTO `users_roles` VALUES (24, 1);
INSERT INTO `users_roles` VALUES (1, 2);
INSERT INTO `users_roles` VALUES (2, 2);
INSERT INTO `users_roles` VALUES (3, 2);
INSERT INTO `users_roles` VALUES (13, 2);
INSERT INTO `users_roles` VALUES (23, 2);
INSERT INTO `users_roles` VALUES (24, 2);
INSERT INTO `users_roles` VALUES (1, 3);
INSERT INTO `users_roles` VALUES (3, 3);
INSERT INTO `users_roles` VALUES (13, 3);
INSERT INTO `users_roles` VALUES (4, 4);
INSERT INTO `users_roles` VALUES (5, 5);
INSERT INTO `users_roles` VALUES (6, 6);
INSERT INTO `users_roles` VALUES (7, 7);
INSERT INTO `users_roles` VALUES (8, 8);
INSERT INTO `users_roles` VALUES (9, 9);
INSERT INTO `users_roles` VALUES (10, 10);
INSERT INTO `users_roles` VALUES (11, 11);
INSERT INTO `users_roles` VALUES (13, 13);
INSERT INTO `users_roles` VALUES (13, 16);

SET FOREIGN_KEY_CHECKS = 1;
