# 权限文件导入接口说明

## 概述

实现了简洁高效的权限文件导入功能，支持Excel、CSV、JSON三种文件格式，自动解析文件内容并批量导入权限数据。

## 接口信息

- **接口路径**: `POST /api/authorities/import`
- **接口描述**: 通过文件批量导入权限信息，支持Excel、CSV、JSON格式
- **Content-Type**: `multipart/form-data`

## 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| file | MultipartFile | 是 | - | 导入文件 |
| appId | Long | 否 | - | 应用ID（可在文件中指定） |
| moduleId | Long | 否 | - | 模块ID（可在文件中指定） |
| overrideExisting | Boolean | 否 | false | 是否覆盖已存在的权限 |
| operator | String | 否 | system | 操作人 |

## 支持的文件格式

### Excel格式 (.xlsx)

第一行为表头，包含以下字段：

| 字段名 | 必填 | 说明 | 示例 |
|--------|------|------|------|
| 权限名称 | 是 | 权限的显示名称 | 用户管理 |
| 权限编码 | 否 | 权限的唯一标识 | USER_MANAGE |
| 权限说明 | 是 | 权限的详细描述 | 用户信息的增删改查权限 |
| 操作列表 | 是 | 支持的操作，逗号分隔 | create,read,update,delete |
| 资源类型 | 否 | 资源类型 | menu |
| 资源路径 | 否 | 资源路径，逗号分隔 | /user/list,/user/add |
| 父权限ID | 否 | 父权限ID | 1 |
| 权限层级 | 否 | 权限层级 | 2 |
| 排序号 | 否 | 排序号 | 10 |
| 应用ID | 是 | 所属应用ID | 1 |
| 模块ID | 是 | 所属模块ID | 101 |
| 同步来源 | 否 | 同步来源标识 | USER_CENTER |

### CSV格式 (.csv)

与Excel格式相同，使用逗号分隔字段。

### JSON格式 (.json)

JSON数组格式，每个元素包含权限的所有字段：

```json
[
  {
    "权限名称": "用户管理",
    "权限编码": "USER_MANAGE",
    "权限说明": "用户信息的增删改查权限",
    "操作列表": "create,read,update,delete",
    "资源类型": "menu",
    "资源路径": "/user/list,/user/add",
    "父权限ID": "1",
    "权限层级": "2",
    "排序号": "10",
    "应用ID": "1",
    "模块ID": "101",
    "同步来源": "USER_CENTER"
  }
]
```

## 响应格式

```json
{
  "code": 200,
  "message": "success",
  "data": {
    "totalCount": 10,
    "successCount": 8,
    "failedCount": 1,
    "skippedCount": 1,
    "successList": ["用户管理", "角色管理"],
    "failedList": ["权限管理 (数据不完整)"],
    "skippedList": ["系统设置 (权限编码已存在)"],
    "fileName": "authorities.xlsx",
    "operator": "admin",
    "importTime": "2025-07-25T10:30:00"
  }
}
```

## 使用示例

### 基本导入

```bash
curl -X POST "http://localhost:8080/api/authorities/import" \
  -F "file=@authorities.xlsx" \
  -F "operator=admin"
```

### 指定应用和模块

```bash
curl -X POST "http://localhost:8080/api/authorities/import" \
  -F "file=@authorities.csv" \
  -F "appId=1" \
  -F "moduleId=101" \
  -F "overrideExisting=true" \
  -F "operator=admin"
```

### JSON文件导入

```bash
curl -X POST "http://localhost:8080/api/authorities/import" \
  -F "file=@authorities.json" \
  -F "operator=admin"
```

## 错误处理

### 常见错误

1. **400 Bad Request**: 导入文件为空或格式不支持
2. **400 Bad Request**: 文件中没有有效的权限数据
3. **500 Internal Server Error**: 文件解析失败或数据库操作失败

### 错误响应示例

```json
{
  "code": 400,
  "message": "不支持的文件格式: txt，支持格式: xlsx, csv, json",
  "timestamp": 1721890200000
}
```

## 实现特点

### 简洁高效
- 无需创建复杂的DTO类，直接使用Map处理数据
- 内置文件解析器，支持三种主流格式
- 统一的错误处理和结果返回

### 性能优化
- 批量处理权限数据，减少数据库交互
- 流式读取文件，支持大文件导入
- 事务控制，确保数据一致性

### 灵活配置
- 支持参数覆盖文件中的应用ID和模块ID
- 可选择是否覆盖已存在的权限
- 详细的导入结果统计

## 注意事项

1. **文件大小**: 建议单次导入不超过1000条权限记录
2. **数据验证**: 必填字段缺失会导致导入失败
3. **权限编码**: 重复的权限编码根据覆盖设置处理
4. **应用模块**: 确保指定的应用ID和模块ID存在
