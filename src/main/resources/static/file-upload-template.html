<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>权限文件导入模板</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .card {
            background: white;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .card h2 {
            color: #667eea;
            margin-bottom: 20px;
            font-size: 1.5em;
            border-bottom: 2px solid #eee;
            padding-bottom: 10px;
        }

        .format-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .format-card {
            border: 2px solid #eee;
            border-radius: 8px;
            padding: 20px;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .format-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
        }

        .format-card.selected {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .format-icon {
            font-size: 2em;
            margin-bottom: 10px;
            display: block;
        }

        .format-name {
            font-size: 1.2em;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }

        .format-desc {
            color: #666;
            font-size: 0.9em;
            margin-bottom: 10px;
        }

        .format-features {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
        }

        .feature-tag {
            background: #e3f2fd;
            color: #1976d2;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
        }

        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .upload-area.dragover {
            border-color: #667eea;
            background-color: #f8f9ff;
        }

        .upload-area input[type="file"] {
            display: none;
        }

        .upload-btn {
            background: #667eea;
            color: white;
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: background 0.3s ease;
        }

        .upload-btn:hover {
            background: #5a6fd8;
        }

        .download-btn {
            background: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
            transition: background 0.3s ease;
        }

        .download-btn:hover {
            background: #218838;
        }

        .info-btn {
            background: #17a2b8;
            color: white;
            padding: 8px 16px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin-left: 10px;
            font-size: 0.9em;
        }

        .info-btn:hover {
            background: #138496;
        }

        .file-info {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 5px 5px 0;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            width: 0%;
            transition: width 0.3s ease;
        }

        .result-area {
            margin-top: 20px;
            padding: 20px;
            border-radius: 8px;
            display: none;
        }

        .result-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }

        .result-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }

        .guide-section {
            margin-top: 20px;
        }

        .guide-content {
            display: none;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 10px;
        }

        .field-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .field-table th,
        .field-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .field-table th {
            background: #f2f2f2;
            font-weight: bold;
        }

        .required {
            color: #dc3545;
            font-weight: bold;
        }

        .optional {
            color: #6c757d;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .format-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>权限文件导入模板</h1>
            <p>支持Excel、CSV、JSON三种格式的权限数据导入</p>
        </div>

        <!-- 格式选择 -->
        <div class="card">
            <h2>📋 选择文件格式</h2>
            <div class="format-grid" id="formatGrid">
                <!-- 格式卡片将通过JavaScript动态生成 -->
            </div>
        </div>

        <!-- 模板下载 -->
        <div class="card">
            <h2>📥 下载模板文件</h2>
            <p>请先选择文件格式，然后下载对应的模板文件：</p>
            <div id="downloadSection" style="margin-top: 15px;">
                <p style="color: #666;">请先选择文件格式</p>
            </div>
        </div>

        <!-- 文件上传 -->
        <div class="card">
            <h2>📤 上传权限文件</h2>
            <div class="upload-area" id="uploadArea">
                <div>
                    <p style="font-size: 1.2em; margin-bottom: 10px;">拖拽文件到此处或点击选择文件</p>
                    <button class="upload-btn" onclick="document.getElementById('fileInput').click()">选择文件</button>
                    <input type="file" id="fileInput" accept=".xlsx,.csv,.json">
                    <p style="margin-top: 10px; color: #666; font-size: 0.9em;">支持 .xlsx, .csv, .json 格式</p>
                </div>
            </div>
            
            <div id="fileInfo" class="file-info" style="display: none;">
                <!-- 文件信息将显示在这里 -->
            </div>
            
            <div class="progress-bar" id="progressBar" style="display: none;">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            
            <div id="uploadResult" class="result-area">
                <!-- 上传结果将显示在这里 -->
            </div>
        </div>

        <!-- 格式说明 -->
        <div class="card">
            <h2>📖 格式说明</h2>
            <div id="guideSection">
                <p style="color: #666;">请选择文件格式查看详细说明</p>
            </div>
        </div>
    </div>

    <script>
        let selectedFormat = null;
        let supportedFormats = [];

        // 页面加载时获取支持的格式
        document.addEventListener('DOMContentLoaded', function() {
            loadSupportedFormats();
            setupFileUpload();
        });

        // 加载支持的文件格式
        async function loadSupportedFormats() {
            try {
                const response = await fetch('/api/authorities/templates/formats');
                const data = await response.json();
                
                if (data.code === 200) {
                    supportedFormats = data.data.formats;
                    renderFormatCards();
                }
            } catch (error) {
                console.error('加载格式失败:', error);
            }
        }

        // 渲染格式卡片
        function renderFormatCards() {
            const grid = document.getElementById('formatGrid');
            grid.innerHTML = '';

            supportedFormats.forEach(format => {
                const card = document.createElement('div');
                card.className = 'format-card';
                card.onclick = () => selectFormat(format.format);
                
                const icon = getFormatIcon(format.format);
                const features = format.features.map(f => `<span class="feature-tag">${f}</span>`).join('');
                
                card.innerHTML = `
                    <div class="format-icon">${icon}</div>
                    <div class="format-name">${format.name}</div>
                    <div class="format-desc">${format.description}</div>
                    <div class="format-features">${features}</div>
                    <div style="margin-top: 10px; font-size: 0.8em; color: #666;">
                        最大: ${format.maxSize}
                    </div>
                `;
                
                grid.appendChild(card);
            });
        }

        // 获取格式图标
        function getFormatIcon(format) {
            const icons = {
                'xlsx': '📊',
                'csv': '📄',
                'json': '🔧'
            };
            return icons[format] || '📁';
        }

        // 选择格式
        function selectFormat(format) {
            selectedFormat = format;

            // 更新选中状态
            document.querySelectorAll('.format-card').forEach(card => {
                card.classList.remove('selected');
            });
            event.currentTarget.classList.add('selected');

            // 更新下载区域
            updateDownloadSection();

            // 加载格式说明
            loadFormatGuide(format);
        }

        // 更新下载区域
        function updateDownloadSection() {
            const section = document.getElementById('downloadSection');

            if (selectedFormat) {
                section.innerHTML = `
                    <a href="/api/authorities/templates/download?format=${selectedFormat}"
                       class="download-btn" download>
                        📥 下载 ${selectedFormat.toUpperCase()} 模板
                    </a>
                    <button class="info-btn" onclick="showExample('${selectedFormat}')">
                        👁️ 查看示例
                    </button>
                `;
            } else {
                section.innerHTML = '<p style="color: #666;">请先选择文件格式</p>';
            }
        }

        // 显示示例数据
        async function showExample(format) {
            try {
                const response = await fetch(`/api/authorities/templates/example/${format}`);
                const data = await response.json();

                if (data.code === 200) {
                    const modal = createModal('示例数据', formatExampleData(data.data, format));
                    document.body.appendChild(modal);
                }
            } catch (error) {
                console.error('获取示例数据失败:', error);
            }
        }

        // 格式化示例数据
        function formatExampleData(data, format) {
            if (format === 'json') {
                return `<pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">${JSON.stringify(data, null, 2)}</pre>`;
            } else if (format === 'csv') {
                return `<pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;">${data}</pre>`;
            } else {
                // Excel格式显示为表格
                let table = '<table class="field-table"><thead><tr>';
                data[0].forEach(header => {
                    table += `<th>${header}</th>`;
                });
                table += '</tr></thead><tbody>';

                for (let i = 1; i < data.length; i++) {
                    table += '<tr>';
                    data[i].forEach(cell => {
                        table += `<td>${cell}</td>`;
                    });
                    table += '</tr>';
                }
                table += '</tbody></table>';
                return table;
            }
        }

        // 创建模态框
        function createModal(title, content) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed; top: 0; left: 0; width: 100%; height: 100%;
                background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
                align-items: center; justify-content: center;
            `;

            modal.innerHTML = `
                <div style="background: white; border-radius: 10px; padding: 30px; max-width: 80%; max-height: 80%; overflow: auto;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                        <h3>${title}</h3>
                        <button onclick="this.closest('div').parentElement.remove()"
                                style="background: none; border: none; font-size: 1.5em; cursor: pointer;">×</button>
                    </div>
                    <div>${content}</div>
                </div>
            `;

            modal.onclick = (e) => {
                if (e.target === modal) modal.remove();
            };

            return modal;
        }

        // 加载格式说明
        async function loadFormatGuide(format) {
            try {
                const response = await fetch(`/api/authorities/templates/format-guide/${format}`);
                const data = await response.json();

                if (data.code === 200) {
                    renderFormatGuide(data.data);
                }
            } catch (error) {
                console.error('加载格式说明失败:', error);
            }
        }

        // 渲染格式说明
        function renderFormatGuide(guide) {
            const section = document.getElementById('guideSection');

            let fieldsTable = '<table class="field-table"><thead><tr><th>字段名</th><th>类型</th><th>必填</th><th>说明</th><th>示例</th></tr></thead><tbody>';
            guide.fields.forEach(field => {
                const requiredClass = field.required ? 'required' : 'optional';
                const requiredText = field.required ? '是' : '否';
                fieldsTable += `
                    <tr>
                        <td class="${requiredClass}">${field.name}</td>
                        <td>${field.type}</td>
                        <td class="${requiredClass}">${requiredText}</td>
                        <td>${field.description}</td>
                        <td><code>${field.example}</code></td>
                    </tr>
                `;
            });
            fieldsTable += '</tbody></table>';

            let tips = '<ul>';
            guide.tips.forEach(tip => {
                tips += `<li>${tip}</li>`;
            });
            tips += '</ul>';

            section.innerHTML = `
                <h3>${guide.name}</h3>
                <p>${guide.description}</p>

                <h4 style="margin-top: 20px;">📋 字段定义</h4>
                ${fieldsTable}

                <h4 style="margin-top: 20px;">📏 文件规则</h4>
                <ul>
                    <li>最大文件大小: ${guide.rules.maxFileSize}</li>
                    <li>最大记录数: ${guide.rules.maxRows || guide.rules.maxRecords}</li>
                    <li>必填字段: ${guide.rules.requiredFields.join(', ')}</li>
                </ul>

                <h4 style="margin-top: 20px;">💡 使用提示</h4>
                ${tips}
            `;
        }

        // 设置文件上传
        function setupFileUpload() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            // 拖拽上传
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileSelect(files[0]);
                }
            });

            // 文件选择
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleFileSelect(e.target.files[0]);
                }
            });
        }

        // 处理文件选择
        async function handleFileSelect(file) {
            // 显示文件信息
            showFileInfo(file);

            // 验证文件
            await validateFile(file);
        }

        // 显示文件信息
        function showFileInfo(file) {
            const fileInfo = document.getElementById('fileInfo');
            const fileSize = formatFileSize(file.size);

            fileInfo.innerHTML = `
                <strong>文件名:</strong> ${file.name}<br>
                <strong>文件大小:</strong> ${fileSize}<br>
                <strong>文件类型:</strong> ${file.type || '未知'}
            `;
            fileInfo.style.display = 'block';
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
            if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
            return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
        }

        // 验证文件
        async function validateFile(file) {
            try {
                const formData = new FormData();
                formData.append('file', file);

                const response = await fetch('/api/authorities/templates/validate', {
                    method: 'POST',
                    body: formData
                });

                const data = await response.json();

                if (data.code === 200) {
                    showValidationResult(data.data);

                    if (data.data.isValid) {
                        showUploadButton(file);
                    }
                } else {
                    showError('文件验证失败: ' + data.message);
                }
            } catch (error) {
                console.error('文件验证失败:', error);
                showError('文件验证失败: ' + error.message);
            }
        }

        // 显示验证结果
        function showValidationResult(validation) {
            const fileInfo = document.getElementById('fileInfo');
            let statusHtml = '';

            if (validation.isValid) {
                statusHtml = `
                    <div style="color: #28a745; margin-top: 10px;">
                        ✅ 文件验证通过<br>
                        <strong>格式:</strong> ${validation.formatName}<br>
                        <strong>表头数量:</strong> ${validation.parseResult?.headerCount || 'N/A'}<br>
                        <strong>数据行数:</strong> ${validation.parseResult?.dataRows || 'N/A'}
                    </div>
                `;
            } else {
                statusHtml = `
                    <div style="color: #dc3545; margin-top: 10px;">
                        ❌ 文件验证失败<br>
                        <strong>错误:</strong> ${validation.error || '不支持的文件格式'}<br>
                        <strong>支持格式:</strong> ${validation.supportedFormats || 'xlsx, csv, json'}
                    </div>
                `;
            }

            fileInfo.innerHTML += statusHtml;
        }

        // 显示上传按钮
        function showUploadButton(file) {
            const uploadResult = document.getElementById('uploadResult');
            uploadResult.innerHTML = `
                <button class="upload-btn" onclick="uploadFile()" style="margin-top: 15px;">
                    🚀 开始导入
                </button>
                <div style="margin-top: 10px;">
                    <label><input type="checkbox" id="overrideExisting"> 覆盖已存在的权限</label>
                </div>
            `;
            uploadResult.style.display = 'block';

            // 保存文件引用
            window.selectedFile = file;
        }

        // 上传文件
        async function uploadFile() {
            if (!window.selectedFile) {
                showError('请先选择文件');
                return;
            }

            const progressBar = document.getElementById('progressBar');
            const progressFill = document.getElementById('progressFill');
            const uploadResult = document.getElementById('uploadResult');

            // 显示进度条
            progressBar.style.display = 'block';
            progressFill.style.width = '0%';

            try {
                const formData = new FormData();
                formData.append('file', window.selectedFile);
                formData.append('overrideExisting', document.getElementById('overrideExisting').checked);
                formData.append('operator', 'web_user');

                // 模拟进度
                let progress = 0;
                const progressInterval = setInterval(() => {
                    progress += Math.random() * 30;
                    if (progress > 90) progress = 90;
                    progressFill.style.width = progress + '%';
                }, 200);

                const response = await fetch('/api/authorities/import', {
                    method: 'POST',
                    body: formData
                });

                clearInterval(progressInterval);
                progressFill.style.width = '100%';

                const data = await response.json();

                setTimeout(() => {
                    progressBar.style.display = 'none';
                    showUploadResult(data);
                }, 500);

            } catch (error) {
                console.error('上传失败:', error);
                progressBar.style.display = 'none';
                showError('上传失败: ' + error.message);
            }
        }

        // 显示上传结果
        function showUploadResult(data) {
            const uploadResult = document.getElementById('uploadResult');

            if (data.code === 200) {
                const result = data.data;
                uploadResult.className = 'result-area result-success';
                uploadResult.innerHTML = `
                    <h3>✅ 导入成功</h3>
                    <div style="margin-top: 15px;">
                        <strong>导入统计:</strong><br>
                        总数: ${result.totalCount} |
                        成功: ${result.successCount} |
                        失败: ${result.failedCount} |
                        跳过: ${result.skippedCount}
                    </div>
                    ${result.failedCount > 0 ? `
                        <div style="margin-top: 10px;">
                            <strong>失败记录:</strong><br>
                            ${result.failedList.slice(0, 5).join('<br>')}
                            ${result.failedList.length > 5 ? '<br>...' : ''}
                        </div>
                    ` : ''}
                `;
            } else {
                uploadResult.className = 'result-area result-error';
                uploadResult.innerHTML = `
                    <h3>❌ 导入失败</h3>
                    <div style="margin-top: 15px;">
                        <strong>错误信息:</strong> ${data.message}
                    </div>
                `;
            }

            uploadResult.style.display = 'block';
        }

        // 显示错误
        function showError(message) {
            const uploadResult = document.getElementById('uploadResult');
            uploadResult.className = 'result-area result-error';
            uploadResult.innerHTML = `
                <h3>❌ 错误</h3>
                <div style="margin-top: 15px;">${message}</div>
            `;
            uploadResult.style.display = 'block';
        }
    </script>
</body>
</html>
