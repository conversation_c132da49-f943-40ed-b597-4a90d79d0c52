package cn.ac.iie.pmserver;

import cn.ac.iie.pmserver.utils.DateUtils;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication(scanBasePackages = "cn.ac.iie.pmserver")
public class PmServerApplication {

	public static void main(String[] args) {
		SpringApplication.run(PmServerApplication.class, args);
		System.out.println("---------------------"+ DateUtils.parse(DateUtils.getCurrentTime()));
	}

}

