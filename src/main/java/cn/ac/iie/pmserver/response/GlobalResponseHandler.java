package cn.ac.iie.pmserver.response;

import cn.ac.iie.pmserver.utils.JsonUtils;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.core.MethodParameter;
import org.springframework.http.MediaType;
import org.springframework.http.converter.HttpMessageConverter;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.lang.NonNull;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice;

/**
 * 全局响应处理器
 * 用于统一封装控制器返回结果到标准ApiResponse格式
 */
@ControllerAdvice
public class GlobalResponseHandler implements ResponseBodyAdvice<Object> {

    /**
     * 判断是否需要对返回值进行处理
     * @param returnType 控制器方法的返回类型
     * @param converterType 选中的消息转换器类型
     * @return true表示需要处理，false表示跳过处理
     */
    @Override
    public boolean supports(@NonNull MethodParameter returnType, @NonNull Class<? extends HttpMessageConverter<?>> converterType) {
        // 1. 尝试获取当前请求
        ServletRequestAttributes attributes = null;
        try {
            attributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        } catch (Exception e) {
            // 防止某些环境下 RequestContextHolder 不可用
            return true;
        }

        // 2. 如果没有请求信息（可能是 Swagger 内部调用），直接放行
        if (attributes == null) {
            return false; // ⚠️ 修改为 false，不处理 Swagger 内部请求
        }

        // 3. 获取请求路径
        HttpServletRequest request = attributes.getRequest();
        String requestURI = request.getRequestURI();

        // 4. 排除所有 Swagger 相关请求
        return !(requestURI.contains("/v3/api-docs")
                || requestURI.contains("/swagger-ui")
                || requestURI.contains("/api-docs"));
    }

    /**
     * 在响应体写入前对返回值进行处理
     * @param body 控制器方法返回的原始对象
     * @param returnType 控制器方法的返回类型
     * @param selectedContentType 选中的内容类型
     * @param selectedConverterType 选中的消息转换器类型
     * @param request 当前HTTP请求
     * @param response 当前HTTP响应
     * @return 处理后的响应体对象
     */
    @Override
    public Object beforeBodyWrite(Object body, @NonNull MethodParameter returnType, @NonNull MediaType selectedContentType,
                                  @NonNull Class<? extends HttpMessageConverter<?>> selectedConverterType,
                                  @NonNull ServerHttpRequest request, @NonNull ServerHttpResponse response) {
        // 如果返回值已经是ApiResponse类型，则直接返回，避免重复包装
        if (body instanceof ApiResponse) {
            return body;
        }

        // 处理String类型返回值，需要先转换为JSON字符串，否则会报类型转换异常
        if (body instanceof String) {
            return JsonUtils.toJson(ApiResponse.success(body));
        }

        // 对于其他类型，统一包装成ApiResponse成功格式
        return ApiResponse.success(body);
    }
}