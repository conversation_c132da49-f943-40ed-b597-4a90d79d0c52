package cn.ac.iie.pmserver.response;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Getter;

/**
 * 统一API响应封装类
 * 用于规范接口返回的数据格式
 * @param <T> 响应数据的泛型类型
 */
@Getter // Lombok注解，自动生成getter方法
@JsonInclude(JsonInclude.Include.NON_NULL) // Jackson注解，值为null的字段不参与序列化
public class ApiResponse<T> {
    /**
     * 业务状态码（非HTTP状态码）
     * 200表示成功，其他值表示不同类型的错误
     */
    private final int code;

    /**
     * 响应消息
     * 用于描述操作结果或错误信息
     */
    private final String message;

    /**
     * 响应数据
     * 成功时返回的业务数据，错误时通常为null
     */
    private final T data;

    /**
     * 响应时间戳（毫秒）
     * 表示响应生成的时间
     */
    private final long timestamp = System.currentTimeMillis();

    /**
     * 全参数构造函数
     * @param code 业务状态码
     * @param message 响应消息
     * @param data 响应数据
     */
    public ApiResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }

    /**
     * 成功响应（无数据）
     * @return 返回code=200的成功响应
     */
    public static ApiResponse<?> success() {
        return new ApiResponse<>(200, "Success", null);
    }

    /**
     * 成功响应（带数据）
     * @param data 要返回的业务数据
     * @param <T> 数据的泛型类型
     * @return 返回code=200的成功响应
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(200, "success", data);
    }

    /**
     * 错误响应
     * @param code 错误码
     * @param msg 错误信息
     * @return 返回包含错误信息的响应
     */
    public static ApiResponse<?> error(int code, String msg) {
        return new ApiResponse<>(code, msg, null);
    }

    /**
     * 错误响应
     * @param code 错误码
     * @param msg 错误信息
     * @param data 错误数据
     * @return 返回包含错误信息的响应。
     */
    public static ApiResponse<?> error(int code, String msg,Object data){
        return new ApiResponse<>(code, msg, data);
    }
}