package cn.ac.iie.pmserver.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.core.userdetails.User;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.provisioning.InMemoryUserDetailsManager;
import org.springframework.security.web.SecurityFilterChain;

/**
 * Spring Security 安全配置类
 * 配置应用的安全策略，包括认证、授权和密码加密等
 */
@Configuration
public class SecurityConfig {

    /**
     * 配置密码编码器
     * @return BCryptPasswordEncoder 使用BCrypt强哈希算法加密密码
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder(); // 用于密码加密存储
    }

    /**
     * 配置内存用户存储服务
     * @return UserDetailsService 包含示例用户信息
     *
     * @implNote 生产环境应替换为数据库实现的UserDetailsService
     * @example 创建了一个测试用户：用户名"user"，密码"password"，角色"USER"
     */
    @Bean
    public UserDetailsService userDetailsService() {
        UserDetails user = User.builder()
                .username("user")  // 用户名
                .password(passwordEncoder().encode("password"))  // 加密后的密码
                .roles("USER")  // 用户角色
                .build();
        return new InMemoryUserDetailsManager(user); // 基于内存的用户存储
    }

    /**
     * 配置安全过滤链（核心安全配置）
     * @param http HttpSecurity配置对象
     * @return SecurityFilterChain 安全过滤链
     * @throws Exception 配置异常
     *
     * @implNote 当前配置为完全开放所有接口（仅用于开发测试）
     * @implNote 生产环境应按需配置权限规则（参考注释掉的示例）
     */
//    @Bean
//    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
//        // 示例1：基础权限控制（部分开放+需要认证）
//        /*
//        http.authorizeHttpRequests(auth -> auth
//                .requestMatchers("/public/**").permitAll()  // 公开访问
//                .anyRequest().authenticated()  // 其他接口需要认证
//        ).formLogin(Customizer.withDefaults());  // 启用默认登录页
//        */
//
//        // 示例2：基于角色的精细控制
//        /*
//        http.authorizeHttpRequests(auth -> auth
//                .requestMatchers("/api/admin/**").hasRole("ADMIN")  // 需要ADMIN角色
//                .requestMatchers("/api/**").authenticated()  // 需登录
//                .requestMatchers("/static/**", "/login").permitAll() // 公开访问
//        );
//        */
//
//        // 当前配置：完全开放所有接口（开发测试用）
//        http.authorizeHttpRequests(auth -> auth.anyRequest().permitAll()); // 所有接口放行
//
//        return http.build();
//    }
    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
        http
                .csrf(csrf -> csrf.disable()) // 关闭 CSRF 保护
                .authorizeHttpRequests(auth -> auth
                        .anyRequest().permitAll() // 所有请求都开放
                );

        return http.build();
    }

//    @Bean
//    public SecurityFilterChain securityFilterChain(HttpSecurity http) throws Exception {
//        // 示例1：基础权限控制（部分开放+需要认证）
//        /*
//        http.authorizeHttpRequests(auth -> auth
//                .requestMatchers("/public/**").permitAll()  // 公开访问
//                .anyRequest().authenticated()  // 其他接口需要认证
//        ).formLogin(Customizer.withDefaults());  // 启用默认登录页
//        */
//
//        // 示例2：基于角色的精细控制
//        /*
//        http.authorizeHttpRequests(auth -> auth
//                .requestMatchers("/api/admin/**").hasRole("ADMIN")  // 需要ADMIN角色
//                .requestMatchers("/api/**").authenticated()  // 需登录
//                .requestMatchers("/static/**", "/login").permitAll() // 公开访问
//        );
//        */
//
//        // 当前配置：完全开放所有接口（开发测试用）
//        http.authorizeHttpRequests(auth -> auth.anyRequest().permitAll()); // 所有接口放行
//        http.csrf(csrf -> csrf.disable());
//        return http.build();
//    }
}