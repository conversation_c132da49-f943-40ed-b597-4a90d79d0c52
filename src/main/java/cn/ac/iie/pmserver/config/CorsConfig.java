package cn.ac.iie.pmserver.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.CorsRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * 跨域配置类
 * 配置全局CORS（跨域资源共享）策略
 *
 * @Configuration 表示这是一个Spring配置类
 */
@Configuration
public class CorsConfig implements WebMvcConfigurer {

    /**
     * 配置CORS跨域规则
     * @param registry CORS注册器，用于配置跨域规则
     *
     * @implSpec 当前配置说明：
     * 1. 对所有路径(/**)启用CORS
     * 2. 允许所有来源域（生产环境应改为具体域名）
     * 3. 允许GET/POST/PUT/DELETE方法
     * 4. 允许所有请求头
     * 5. 预检请求缓存1小时(3600秒)
     */
    @Override
    public void addCorsMappings(CorsRegistry registry) {
        registry.addMapping("/**")                // 匹配所有API路径
                .allowedOrigins("*")              // 允许所有来源域（生产环境需替换为具体域名，如"https://domain.com"）
                .allowedMethods("GET", "POST", "PUT", "DELETE")  // 允许的HTTP方法
                .allowedHeaders("*")             // 允许所有请求头
                .maxAge(3600)                   // 预检请求缓存时间（秒）
                .exposedHeaders("*")
                .allowCredentials(false);
    }
}