package cn.ac.iie.pmserver.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration // 声明为Spring配置类
public class OpenApiConfig {

    @Bean // 将返回对象注册为Spring Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .openapi("3.0.1") // 指定OpenAPI规范版本
                .info(new Info()
                        .title("权限管理系统测试文档（本地分支）") // 文档标题
                        .version("1.0") // API版本号
                        .description("基于Spring Boot 3.3.2的SpringDoc OpenAPI文档") // 详细描述
                        .termsOfService("http://swagger.io/terms/") // 服务条款链接
                        .license(new License().name("Apache 23.0").url("http://springdoc.org"))); // 许可证信息
    }
}