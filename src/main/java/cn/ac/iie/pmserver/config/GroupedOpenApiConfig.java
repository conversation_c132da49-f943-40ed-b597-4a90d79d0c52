package cn.ac.iie.pmserver.config;

import org.springdoc.core.models.GroupedOpenApi;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * OpenAPI 分组配置类
 * 用于将API接口按照功能模块分组展示在Swagger UI中
 */
@Configuration
public class GroupedOpenApiConfig {

    /**
     * 用户相关API分组配置
     * @return GroupedOpenApi 用户分组配置对象
     */
    @Bean
    public GroupedOpenApi userApi() {
        return GroupedOpenApi.builder()
                .group("users")  // 分组名称(显示在Swagger UI中)
                .pathsToMatch("/api/users/**")  // 匹配所有以/api/users/开头的路径
                .build();
    }

    /**
     * 管理员相关API分组配置
     * @return GroupedOpenApi 管理员分组配置对象
     */
    @Bean
    public GroupedOpenApi adminApi() {
        return GroupedOpenApi.builder()
                .group("admin")  // 分组名称(显示在Swagger UI中)
                .pathsToMatch("/api/admin/**")  // 匹配所有以/api/admin/开头的路径
                .build();
    }
}