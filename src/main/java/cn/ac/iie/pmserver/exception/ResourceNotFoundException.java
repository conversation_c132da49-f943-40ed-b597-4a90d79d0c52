package cn.ac.iie.pmserver.exception;

/**
 * 资源未找到异常
 * <p>表示请求的特定资源在系统中不存在的业务异常</p>
 *
 * <p>应该在被请求的资源（如数据库记录、文件等）不存在时抛出</p>
 *
 * @see RuntimeException
 */
public class ResourceNotFoundException extends RuntimeException {
    /**
     * 构造一个资源未找到异常
     *
     * @param message 异常详细信息，应清晰说明哪个资源未找到
     *                （例如："未找到ID为123的用户"）
     */
    public ResourceNotFoundException(String message) {
        super(message);
    }
}