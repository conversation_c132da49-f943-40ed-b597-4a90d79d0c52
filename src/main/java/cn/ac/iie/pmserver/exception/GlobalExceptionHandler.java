package cn.ac.iie.pmserver.exception;

import cn.ac.iie.pmserver.response.ApiResponse;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;
import java.util.Map;

/**
 * 全局异常处理类，统一处理应用中的异常。
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理通用的Exception异常。
     * @param e 异常对象
     * @return 封装好的错误响应
     */
    @ExceptionHandler(Exception.class)
    public ApiResponse<?> handleException(Exception e) {
        // 返回500错误，表示服务器发生错误，包含异常信息
        return ApiResponse.error(500, e.getMessage());
    }

    /**
     * 处理资源未找到的异常。
     * @param e 自定义异常：资源未找到
     * @return 封装好的错误响应，状态码404表示资源未找到
     */
    @ExceptionHandler(ResourceNotFoundException.class)
    public ApiResponse<?> handleResourceNotFound(ResourceNotFoundException e) {
        // 返回404错误，表示资源未找到，包含异常信息
        return ApiResponse.error(404, e.getMessage());
    }

    /**
     * 统一参数校验
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ApiResponse<?> handLeMethodArgumentNotValidException(MethodArgumentNotValidException e){
        BindingResult result = e.getBindingResult();
        Map<String, String> errorMap = new HashMap<>();
        result.getFieldErrors().forEach(fieldError -> {
            errorMap.put(fieldError.getField(), fieldError.getDefaultMessage());
        });
        return ApiResponse.error(400, "参数校验失败", errorMap);
    }

}
