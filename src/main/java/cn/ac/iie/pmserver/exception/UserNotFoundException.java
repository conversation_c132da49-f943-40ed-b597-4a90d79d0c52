package cn.ac.iie.pmserver.exception;

/**
 * 用户未找到异常
 * <p>表示请求的特定用户不存在的业务异常，是ResourceNotFoundException的特化版本</p>
 *
 * <p>应该在用户查询操作（如根据ID查询用户）但用户不存在时抛出</p>
 *
 * @see ResourceNotFoundException
 */
public class UserNotFoundException extends ResourceNotFoundException {
    /**
     * 构造一个用户未找到异常
     *
     * @param message 异常详细信息，应清晰说明哪个用户未找到
     *                （例如："未找到用户ID为123的用户"）
     */
    public UserNotFoundException(String message) {
        super(message);
    }
}