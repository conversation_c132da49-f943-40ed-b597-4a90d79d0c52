//package cn.ac.iie.pmserver.aop;
//
//import org.aspectj.lang.ProceedingJoinPoint;
//import org.aspectj.lang.annotation.Around;
//import org.aspectj.lang.annotation.Aspect;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.core.annotation.Order;
//import org.springframework.stereotype.Component;
//
///**
// * 全局日志切面类
// * 使用Spring AOP对Controller层方法进行环绕日志记录
// * 自动记录方法进入、退出和异常情况
// */
//@Aspect    // 声明这是一个切面类
//@Component // 将此类作为Spring组件纳入容器管理
////@Order(1) // 数字越小优先级越高
//public class LoggingAspect {
//
//    // 使用SLF4J日志框架
//    private static final Logger logger = LoggerFactory.getLogger(LoggingAspect.class);
//
//    /**
//     * 环绕通知 - 对Controller层所有方法进行日志记录
//     * @param joinPoint 连接点对象，包含被拦截方法的信息
//     * @return 被拦截方法的执行结果
//     * @throws Throwable 可能抛出的异常
//     *
//     * 切入点表达式说明：
//     * execution(* cn.ac.iie.pmserver.controller..*(..))
//     * - 第一个*表示任意返回类型
//     * - cn.ac.iie.pmserver.controller 指定包路径
//     * - .. 表示当前包及其子包
//     * - *(..) 表示任意方法名和任意参数
//     */
//    @Around("execution(* cn.ac.iie.pmserver.controller..*(..))")
//    public Object logMethodCall(ProceedingJoinPoint joinPoint) throws Throwable {
//        // 获取当前执行的方法名
//        String methodName = joinPoint.getSignature().getName();
//
//        // 记录方法进入日志 [级别INFO]
//        logger.info("进入方法-aop: {}", methodName);
//
//        try {
//            // 执行目标方法并获取返回值
//            Object result = joinPoint.proceed();
//
//            // 记录方法正常退出日志 [级别INFO]
//            logger.info("退出方法-aop: {}", methodName);
//
//            return result;
//        } catch (Exception e) {
//            // 记录方法异常日志 [级别ERROR]
//            logger.error("异常方法-aop: {}", methodName, e);
//
//            // 异常继续向上抛出
//            throw e;
//        }
//    }
//}