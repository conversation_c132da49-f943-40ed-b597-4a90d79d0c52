package cn.ac.iie.pmserver.request.authority;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 权限上传请求对象
 * 用于外部应用上传权限信息到统一权限管理平台
 */
@Data
@NoArgsConstructor
@Schema(description = "权限上传请求对象")
public class AuthorityUploadRequest {

    @NotBlank(message = "权限名称不能为空")
    @Schema(description = "权限名称", example = "用户管理")
    private String authorityName;

    @Schema(description = "权限编码（外部应用定义的唯一标识）", example = "USER_MANAGE")
    private String authorityCode;

    @NotBlank(message = "权限说明不能为空")
    @Schema(description = "权限说明", example = "用户信息的增删改查权限")
    private String authorityDescription;

    @NotNull(message = "操作列表不能为空")
    @Schema(description = "操作列表", example = "[\"create\", \"read\", \"update\", \"delete\"]")
    private List<String> actions;

    @Schema(description = "资源类型", example = "menu", allowableValues = {"menu", "button", "api"})
    private String resourceType;

    @Schema(description = "资源路径列表", example = "[\"/user/list\", \"/user/add\", \"/user/edit\"]")
    private List<String> resourcePaths;

    @Schema(description = "父权限ID（用于构建权限树）", example = "1")
    private Long parentId;

    @Schema(description = "权限层级", example = "2")
    private Integer level = 1;

    @Schema(description = "排序号", example = "10")
    private Integer sortOrder = 0;

    @NotNull(message = "应用ID不能为空")
    @Schema(description = "所属应用ID", example = "1")
    private Long appId;

    @NotNull(message = "模块ID不能为空")
    @Schema(description = "所属模块ID", example = "101")
    private Long moduleId;

    @Schema(description = "同步来源应用标识", example = "USER_CENTER")
    private String syncSource;

    @NotBlank(message = "创建人不能为空")
    @Schema(description = "创建人", example = "system")
    private String creatUser = "system";

    // ==================== 业务方法 ====================

    /**
     * 获取操作列表的JSON字符串
     */
    public String getActionsJson() {
        if (actions == null || actions.isEmpty()) {
            return "[\"read\"]"; // 默认只读权限
        }
        return "[\"" + String.join("\", \"", actions) + "\"]";
    }

    /**
     * 获取资源路径的JSON字符串
     */
    public String getResourcePathsJson() {
        if (resourcePaths == null || resourcePaths.isEmpty()) {
            return null;
        }
        return "[\"" + String.join("\", \"", resourcePaths) + "\"]";
    }

    /**
     * 验证请求参数的有效性
     */
    public boolean isValid() {
        return authorityName != null && !authorityName.trim().isEmpty()
                && authorityDescription != null && !authorityDescription.trim().isEmpty()
                && actions != null && !actions.isEmpty()
                && appId != null && appId > 0
                && moduleId != null && moduleId > 0;
    }

    /**
     * 设置默认值
     */
    public void setDefaults() {
        if (level == null || level < 1) {
            level = 1;
        }
        if (sortOrder == null) {
            sortOrder = 0;
        }
        if (creatUser == null || creatUser.trim().isEmpty()) {
            creatUser = "system";
        }
    }
}
