package cn.ac.iie.pmserver.request.authority;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 权限批量同步请求对象
 * 用于外部应用批量同步权限信息到统一权限管理平台
 */
@Data
@NoArgsConstructor
@Schema(description = "权限批量同步请求对象")
public class AuthoritySyncRequest {

    @NotNull(message = "应用ID不能为空")
    @Schema(description = "应用ID", example = "1")
    private Long appId;

    @NotBlank(message = "同步来源不能为空")
    @Schema(description = "同步来源应用标识", example = "USER_CENTER")
    private String syncSource;

    @NotNull(message = "同步类型不能为空")
    @Schema(description = "同步类型", example = "INCREMENTAL")
    private SyncType syncType = SyncType.INCREMENTAL;

    @NotEmpty(message = "权限列表不能为空")
    @Valid
    @Schema(description = "权限信息列表")
    private List<AuthorityUploadRequest> authorities;

    @Schema(description = "是否强制覆盖已存在的权限", example = "false")
    private Boolean forceOverride = false;

    @NotBlank(message = "操作人不能为空")
    @Schema(description = "操作人", example = "admin")
    private String operator = "system";

    /**
     * 同步类型枚举
     */
    public enum SyncType {
        @Schema(description = "增量同步")
        INCREMENTAL,
        
        @Schema(description = "全量同步")
        FULL
    }

    // ==================== 业务方法 ====================

    /**
     * 获取权限数量
     */
    public int getAuthorityCount() {
        return authorities != null ? authorities.size() : 0;
    }

    /**
     * 验证请求的有效性
     */
    public boolean isValid() {
        if (appId == null || appId <= 0) {
            return false;
        }
        if (syncSource == null || syncSource.trim().isEmpty()) {
            return false;
        }
        if (authorities == null || authorities.isEmpty()) {
            return false;
        }
        
        // 验证每个权限信息的有效性
        return authorities.stream().allMatch(AuthorityUploadRequest::isValid);
    }

    /**
     * 设置默认值
     */
    public void setDefaults() {
        if (syncType == null) {
            syncType = SyncType.INCREMENTAL;
        }
        if (forceOverride == null) {
            forceOverride = false;
        }
        if (operator == null || operator.trim().isEmpty()) {
            operator = "system";
        }
        
        // 为每个权限设置默认值
        if (authorities != null) {
            authorities.forEach(authority -> {
                authority.setAppId(this.appId);
                authority.setSyncSource(this.syncSource);
                authority.setCreatUser(this.operator);
                authority.setDefaults();
            });
        }
    }

    /**
     * 检查是否为全量同步
     */
    public boolean isFullSync() {
        return SyncType.FULL.equals(syncType);
    }

    /**
     * 检查是否强制覆盖
     */
    public boolean shouldForceOverride() {
        return Boolean.TRUE.equals(forceOverride);
    }
}
