package cn.ac.iie.pmserver.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Getter // 提供外部获取内部资源的方法
@Setter // 提供外部修改内部资源的方法
@NoArgsConstructor  // 提供一个无参构造方法
@RequiredArgsConstructor  // 自动生成构造方法：为类中所有 final 修饰的字段 或 标注了 @NonNull 的字段生成对应的构造方法参数。
@Entity // 标记为实体类
@Table(name = "roles")  // 用于指定实体类与数据库表的映射关系，name参数：指定映射的数据库表名称
@Schema(description = "Role entity")
public class Role {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @NonNull
    private Long roleId;

    @Column(unique = true, nullable = false)
    @NonNull
    private String roleCode;

    @Column(nullable = false)
    @NonNull
    private String roleName;

    @NonNull
    private String roleDescription;

    @NonNull
    private Integer delFlag;

    @NonNull
    private String creatUser;

    @NonNull
    private LocalDateTime creatTime;

    @NonNull
    private String updateUser;

    @NonNull
    private LocalDateTime updateTime;

    @NonNull
    private String sourceSystem;

    @NonNull
    private String resourceCode;

    @NonNull
    private String businessRoleCode;

    // 父角色关联 - 多对一关系
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_role_id")
    private Role parentRole;

    // 子角色集合 - 一对多关系
    @OneToMany(mappedBy = "parentRole", fetch = FetchType.LAZY, cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<Role> children = new HashSet<>();

    @Column(name = "parent_role_code", length = 100)
    private String parentRoleCode;

    @Column(name = "role_level", nullable = false)
    private Integer roleLevel = 10;

    // 其他关联关系保持不变...

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "roles_authorities",
            joinColumns = @JoinColumn(name = "role_id"),
            inverseJoinColumns = @JoinColumn(name = "authority_id")
    )
    private Set<Authority> authorities = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "roles_modules",
            joinColumns = @JoinColumn(name = "role_id"),
            inverseJoinColumns = @JoinColumn(name = "module_id")
    )
    private Set<Module> modules = new HashSet<>();

    @ManyToMany(mappedBy = "roles", fetch = FetchType.LAZY)
    private Set<App> apps = new HashSet<>();

    @ManyToMany(mappedBy = "roles", fetch = FetchType.LAZY)
    private Set<User> users = new HashSet<>();

    @ManyToMany(mappedBy = "roles", fetch = FetchType.LAZY)
    private Set<Policies> policies = new HashSet<>();


    // 在Role实体类中添加以下方法
    public void addChild(Role child) {
        if (this.children == null) {
            this.children = new HashSet<>();
        }

        // 避免循环引用
        if (isAncestor(child)) {
            throw new IllegalArgumentException("不能将祖先角色添加为子角色，会导致循环引用");
        }

        this.children.add(child);
        child.setParentRole(this);
        child.setParentRoleCode(this.roleCode);
        child.setRoleLevel(this.roleLevel + 1);
        child.setSourceSystem(this.sourceSystem);
    }

    // 检查是否会导致循环引用
    private boolean isAncestor(Role potentialChild) {
        Role current = this.parentRole;
        while (current != null) {
            if (current.equals(potentialChild)) {
                return true;
            }
            current = current.parentRole;
        }
        return false;
    }



}
