package cn.ac.iie.pmserver.model;

import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;
import java.util.List;

@NoArgsConstructor
@RequiredArgsConstructor
@Entity
@Table(name = "import_records")
@Getter
@Setter
public class ImportRecord {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(nullable = false)
    @NonNull
    private String fileName;          // 导入文件名

    @Column(nullable = false)
    @NonNull
    private String fileType;         // 文件类型（如：用户、部门、角色等）

    @NonNull
    private Integer totalCount;      // 总记录数

    @NonNull
    private Integer successCount;    // 成功数

    @NonNull
    private Integer failedCount;     // 失败数

    @NonNull
    @Enumerated(EnumType.STRING)
    private ImportMode mode;         // 导入模式（FULL全量/INCREMENTAL增量）

    @NonNull
    private String operator;         // 操作人

    @NonNull
    private LocalDateTime startTime; // 开始时间

    @NonNull
    private LocalDateTime endTime;   // 结束时间

    @NonNull
    @Enumerated(EnumType.STRING)
    private ImportStatus status;     // 状态（SUCCESS/FAILED/PARTIAL）

    @Column(columnDefinition = "VARCHAR(500) DEFAULT '无错误信息'")
    @NonNull
    private String errorSummary = "无错误信息";     // 错误摘要（前N条错误）

    // 两种数据来源，身份认证系统、业务系统
    @NonNull
    private String sourceSystem; // 来源系统标识：AUTH_CENTER / BUSINESS_SYSTEM

    @OneToMany(mappedBy = "importRecord", cascade = CascadeType.ALL)
    private List<ImportDetail> details; // 详细记录（可选）


    public enum ImportMode {
        FULL,       // 全量导入
        INCREMENTAL // 增量导入
    }

    public enum ImportStatus {
        PENDING,    // 待处理
        PROCESSING, // 处理中
        SUCCESS,    // 成功
        FAILED,     // 失败
        PARTIAL     // 部分成功
    }


    // 全参数构造函数
    public ImportRecord(
            String fileName,
            String fileType,
            int totalCount,
            int successCount,
            int failedCount,
            ImportMode mode,
            String operator,
            LocalDateTime startTime,
            LocalDateTime endTime,
            ImportStatus status,
            String errorSummary,
            String sourceSystem
    ) {
        this.fileName = fileName;
        this.fileType = fileType;
        this.totalCount = totalCount;
        this.successCount = successCount;
        this.failedCount = failedCount;
        this.mode = mode;
        this.operator = operator;
        this.startTime = startTime;
        this.endTime = endTime;
        this.status = status;
        this.errorSummary = errorSummary;
        this.sourceSystem = sourceSystem;
    }

}
