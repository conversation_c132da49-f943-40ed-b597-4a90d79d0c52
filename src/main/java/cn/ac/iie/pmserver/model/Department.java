package cn.ac.iie.pmserver.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

@Getter // 提供外部获取内部资源的方法
@Setter // 提供外部修改内部资源的方法
@NoArgsConstructor  // 提供一个无参构造方法
@RequiredArgsConstructor  // 自动生成构造方法：为类中所有 final 修饰的字段 或 标注了 @NonNull 的字段生成对应的构造方法参数。
@Entity // 标记为实体类
@Table(name = "departments")  // 用于指定实体类与数据库表的映射关系，name参数：指定映射的数据库表名称
@Schema(description = "Department entity")
public class Department {

    @Id // 表明该字段是表的主键
    @GeneratedValue(strategy = GenerationType.IDENTITY)  // 中用于指定主键生成策略的注解，表示主键值由数据库自动生成（如自增、序列等），此处为自增
    @NonNull  // 为@RequiredArgsConstructor注解做标记作用
    private Long departmentId; //部门Id, 对应表中的主键

    @Column(unique = true, nullable = false)  // unique = true	该列值必须唯一（不允许重复值），nullable = false	该列不允许为NULL（必须提供值）
    @NonNull
    private String departmentCode;   //部门编码，唯一，必填

    @Column(nullable = false)
    @NonNull
    private String departmentName;   //部门名称

    @NonNull
    private String departmentHead;   //部门负责人

    @NonNull
    private String departmentDescription; //部门描述

    @NonNull
    private String address;   //地址，非必填

    @NonNull
    private Integer delFlag;  //禁用状态，非必填

    @NonNull
    private Integer level;  // 部门层级，1表示一级部门，2表示二级部门等

    @Column(name = "parent_id")  // 显式指定列名
    private Long parentId;  // 父部门ID，顶级部门为null

    @NonNull
    private Integer orderNum;  // 同级部门排序号

    @NonNull
    private String path;  // 部门路径，如"0,1,2"表示该部门的父路径

    @NonNull
    private Boolean hasChildren = false;  // 是否有子部门

    @NonNull
    private String creatUser;  //创建人，非必填

    @NonNull
    private LocalDateTime creatTime;  //创建时间，非必填

    @NonNull
    private String updateUser;  //更新人，非必填

    @NonNull
    private LocalDateTime updateTime;  //更新时间，非必填

    @ManyToMany(mappedBy = "departments", fetch = FetchType.LAZY)
    private Set<Authority> authorities = new HashSet<>();

    @ManyToMany(mappedBy = "departments", fetch = FetchType.LAZY)
    private Set<User> users = new HashSet<>();

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent_id", insertable = false, updatable = false)
    private Department parent;
}
