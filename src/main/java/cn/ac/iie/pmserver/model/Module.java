package cn.ac.iie.pmserver.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@RequiredArgsConstructor
@Entity
@Table(name = "modules")
@Schema(description = "Module entity")
public class Module {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @NonNull   // 为@RequiredArgsConstructor注解做标记作用
    private Long moduleId; //模块id

    @Column(nullable = false)
    @NonNull
    private String moduleName;  //模块名称

    @Column(columnDefinition = "INT DEFAULT 0 ")
    private Long parentId;

    private String moduleDescription; //模块描述

    @Column(nullable = false)
    @NonNull
    private String moduleUrl; //模块url

    @Column(columnDefinition = "INT DEFAULT 0 ")
    private Integer delFlag;   //禁用状态，0-正常，1-禁用，默认0

    @Column(nullable = false)
    @NonNull
    private String creatUser; //创建人

    @Column(nullable = false)
    @NonNull
    private LocalDateTime creatTime; //创建时间

    private String updateUser; //更新人

    private LocalDateTime updateTime; //更新时间

    @Column(nullable = false)
    @NonNull
    private String pointUserPhone;//责任人电话

    @ManyToMany(mappedBy = "modules", fetch = FetchType.LAZY)
    private Set<Authority> authoritys = new HashSet<>();

    @ManyToMany(mappedBy = "modules", fetch = FetchType.LAZY)
    private Set<App> apps = new HashSet<>();

    @ManyToMany(mappedBy = "modules", fetch = FetchType.LAZY)
    private Set<Role> roles = new HashSet<>();

}
