package cn.ac.iie.pmserver.model;

import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;

@NoArgsConstructor
@RequiredArgsConstructor
@Entity
@Table(name = "import_details")
@Getter
@Setter
public class ImportDetail {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @NonNull
    private Long id;

    @ManyToOne
    @JoinColumn(name = "record_id", nullable = false)
    @NonNull
    private ImportRecord importRecord;

    @NonNull
    private String recordKey;      // 记录唯一标识（如用户ID/部门编码）

    @NonNull
    private Boolean isSuccess;     // 是否成功

    @Column
    @Enumerated(EnumType.STRING)
    @NonNull
    private OperationType operationType; // 操作类型：CREATE/UPDATE


    @Column(columnDefinition = "VARCHAR(500) DEFAULT '无错误信息'", nullable = false)
    @NonNull
    private String errorMessage = "无错误信息"; // 设置默认值

    @Column
    @NonNull
    private LocalDateTime processTime;

    public enum OperationType {
        CREATE, UPDATE
    }
}
