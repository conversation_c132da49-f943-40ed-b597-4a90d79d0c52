package cn.ac.iie.pmserver.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Getter // 提供外部获取内部资源的方法
@Setter // 提供外部修改内部资源的方法
@NoArgsConstructor  // 提供一个无参构造方法
@RequiredArgsConstructor  // 自动生成构造方法：为类中所有 final 修饰的字段 或 标注了 @NonNull 的字段生成对应的构造方法参数。
@Entity // 标记为实体类
@Table(name = "policies")  // 用于指定实体类与数据库表的映射关系，name参数：指定映射的数据库表名称
@Schema(description = "Policie entity")
public class Policies {

    @Id // 表明该字段是表的主键
    @GeneratedValue(strategy = GenerationType.IDENTITY)  // 中用于指定主键生成策略的注解，表示主键值由数据库自动生成（如自增、序列等），此处为自增
    @NonNull  // 为@RequiredArgsConstructor注解做标记作用
    private Long policyId;//(唯一标识)

    @Column(nullable = false)
    @NonNull
    private String policyName;

    @Column(nullable = false)
    @NonNull
    private String policyDescription;

    @Column(nullable = false)
    @NonNull
    private Integer delFlag;

    // 与授权相关策略为1，与接口同步相关为2，自定义为3
    @Column(nullable = false)
    @NonNull
    private Integer type;

    private String policyCode;          // 策略编码（唯一标识）
    private String policyType;          // 策略类型（RBAC/ABAC/ACL）
    private String effect;              // 策略效果（ALLOW/DENY）

    @Column(columnDefinition = "TEXT")
    private String conditions;          // 策略条件（JSON格式）

    @Column(columnDefinition = "TEXT")
    private String resources;           // 适用资源（JSON格式）

    @Column(columnDefinition = "TEXT")
    private String actions;             // 适用操作（JSON格式）

    private Integer priority;           // 策略优先级
    private Integer status;             // 策略状态（0-启用，1-禁用，2-草稿）
    private LocalDateTime effectiveTime; // 生效时间
    private LocalDateTime expireTime;   // 失效时间

    @NonNull
    private String creatUser;

    @NonNull
    private LocalDateTime creatTime;

    @NonNull
    private String updateUser;

    @NonNull
    private LocalDateTime updateTime;


    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "policies_roles",
            joinColumns = @JoinColumn(name = "policy_id"),
            inverseJoinColumns = @JoinColumn(name = "role_id"),
            uniqueConstraints = @UniqueConstraint(columnNames = {"policy_id", "role_id"})
    )
    private Set<Role> roles = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "policies_apps",
            joinColumns = @JoinColumn(name = "policy_id"),
            inverseJoinColumns = @JoinColumn(name = "app_id"),
            uniqueConstraints = @UniqueConstraint(columnNames = {"policy_id", "app_id"})
    )
    private Set<App> apps = new HashSet<>();

}
