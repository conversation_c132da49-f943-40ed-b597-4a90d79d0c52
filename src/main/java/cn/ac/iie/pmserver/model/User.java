package cn.ac.iie.pmserver.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Getter // 提供外部获取内部资源的方法
@Setter // 提供外部修改内部资源的方法
@NoArgsConstructor  // 提供一个无参构造方法
@RequiredArgsConstructor  // 自动生成构造方法：为类中所有 final 修饰的字段 或 标注了 @NonNull 的字段生成对应的构造方法参数。
@Entity // 标记为实体类
@Table(name = "users")  // 用于指定实体类与数据库表的映射关系，name参数：指定映射的数据库表名称
@Schema(description = "User entity")
public class User {

    @Id // 表明该字段是表的主键
    @GeneratedValue(strategy = GenerationType.IDENTITY)  // 中用于指定主键生成策略的注解，表示主键值由数据库自动生成（如自增、序列等），此处为自增
    @NonNull  // 为@RequiredArgsConstructor注解做标记作用
    private Long userId; //用户Id, 对应表中的主键

    @NonNull
    private String keyId;   //USB Key号

    // 用户唯一标识
    @Column(unique = true, nullable = false)
    @NonNull
    private String userCode; // 用户编码

    @Column(nullable = false)
    @NonNull
    private String name;   //姓名，可能有重名的情况，所以不唯一， 必填

    @Column(nullable = false)
    @NonNull
    private String password; //密码，必填

    @NonNull
    private String email;   //邮箱，非必填

    @NonNull
    private String phone;  //电话，非必填

    @NonNull
    private Integer sex;  //性别，非必填

    @Column(nullable = false)
    @NonNull
    private Integer delFlag;  //禁用状态

    @NonNull
    private String creatUser;  //创建人，非必填

    @NonNull
    private LocalDateTime creatTime;  //创建时间，非必填

    @NonNull
    private String updateUser;  //更新人，非必填

    @NonNull
    private LocalDateTime updateTime;  //更新时间，非必填

    // 两种数据来源，身份认证系统、业务系统
    @NonNull
    private String sourceSystem; // 来源系统标识：AUTH_CENTER / BUSINESS_SYSTEM

    // 业务系统标识码
    @NonNull
    private String resourceCode; // 外部系统ID（用于映射权威认证中心ID）



    // 身份认证系统用户唯一标识
    @NonNull
    private String authUserCode; // 身份认证中心ID（来源系统内唯一）

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "users_roles",
            joinColumns = @JoinColumn(name = "user_id"),
            inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    private Set<Role> roles = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "users_departments",
            joinColumns = @JoinColumn(name = "user_id"),
            inverseJoinColumns = @JoinColumn(name = "department_id")
    )
    private Set<Department> departments = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "users_apps",
            joinColumns = @JoinColumn(name = "user_id"),
            inverseJoinColumns = @JoinColumn(name = "app_id")
    )
    private Set<App> apps = new HashSet<>();


}
