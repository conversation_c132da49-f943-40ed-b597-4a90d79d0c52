package cn.ac.iie.pmserver.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;

import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@RequiredArgsConstructor
@Entity
@Table(name = "apps")
@Schema(description = "App entity")
public class App {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @NonNull   // 为@RequiredArgsConstructor注解做标记作用
    private Long appId;  //应用id,主键

    @Column(nullable = false)
    @NonNull
    private String appName;   //应用名称

    @Column(nullable = false)
    @NonNull
    private String appUrl;  //应用域名url

    private String appDescription;  //应用描述

    @Column(nullable = false)
    @NonNull
    private String appCallbackUrl; //应用回调地址(接收授权结果通知）

    @Column(nullable = false)
    @NonNull
    private String appLogo; //应用logo url

    @Column(columnDefinition = "INT DEFAULT 0 ")
    private Integer delFlag;   //禁用状态，0-正常，1-禁用，默认0

    @Column(nullable = false)
    @NonNull
    private String creatUser; //创建人

    @Column(nullable = false)
    @NonNull
    private LocalDateTime creatTime; //创建时间

    private String updateUser; //更新人

    private LocalDateTime updateTime; //更新时间

    @Column(nullable = false)
    @NonNull
    private String pointUserPhone;//创建人联系电话

    @Column(nullable = false)
    @NonNull
    private Integer aliveFlag;//应用的连通状态，0-正常，1-异常

    @ManyToMany(fetch = FetchType.LAZY, cascade = {CascadeType.PERSIST, CascadeType.MERGE})
    @JoinTable(
            name = "modules_apps",
            joinColumns = @JoinColumn(name = "app_id"),
            inverseJoinColumns = @JoinColumn(name = "module_id")
    )
    private Set<Module> modules = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "roles_apps",
            joinColumns = @JoinColumn(name = "app_id"),
            inverseJoinColumns = @JoinColumn(name = "role_id")
    )
    private Set<Role> roles = new HashSet<>();

    @ManyToMany(mappedBy = "apps", fetch = FetchType.LAZY)
    private Set<User> users = new HashSet<>();
}
