package cn.ac.iie.pmserver.model;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.*;
import lombok.*;
import java.time.LocalDateTime;
import java.util.HashSet;
import java.util.Set;

@Getter
@Setter
@NoArgsConstructor
@RequiredArgsConstructor
@Entity
@Table(name = "authorities")
@Schema(description = "Authority entity")
public class Authority {

    @Id // 表明该字段是表的主键
    @GeneratedValue(strategy = GenerationType.IDENTITY)  // 中用于指定主键生成策略的注解，表示主键值由数据库自动生成（如自增、序列等），此处为自增
    @NonNull  // 为@RequiredArgsConstructor注解做标记作用
    private Long authorityId; //权限Id（唯一标识）, 对应表中的主键

    @Column(nullable = false)
    @NonNull
    private String authorityName;   //权限名称

    @Column(unique = true)
    private String authorityCode;   //权限编码（外部应用定义的唯一标识）

    @NonNull
    private String authorityDescription; //权限说明

    @Column(nullable = false)
    @NonNull
    private String actions; //操作列表（JSON格式）

    private String resourceType;       // 资源类型（menu/button/api）

    @Column(columnDefinition = "TEXT")
    private String resourcePaths;       // 资源路径配置（JSON格式）

    private Long parentId;              // 父权限ID
    private Integer level;              // 权限层级
    private Integer sortOrder;          // 排序号

    // 同步相关字段
    private String syncSource;          // 同步来源应用
    private LocalDateTime syncTime;     // 同步时间
    private String syncStatus;          // 同步状态（SUCCESS/FAILED/PENDING）

    @NonNull
    private String creatUser;  //创建人，非必填

    @NonNull
    private LocalDateTime creatTime;  //创建时间，非必填

    @NonNull
    private String updateUser;  //更新人，非必填

    @NonNull
    private LocalDateTime updateTime;  //更新时间，非必填

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "modules_authorities",
            joinColumns = @JoinColumn(name = "authority_id"),
            inverseJoinColumns = @JoinColumn(name = "module_id")
    )
    private Set<Module> modules = new HashSet<>();

    @ManyToMany(fetch = FetchType.LAZY)
    @JoinTable(
            name = "departments_authorities",
            joinColumns = @JoinColumn(name = "authority_id"),
            inverseJoinColumns = @JoinColumn(name = "department_id")
    )
    private Set<Department> departments = new HashSet<>();

    @ManyToMany(mappedBy = "authorities", fetch = FetchType.LAZY)
    private Set<Role> roles = new HashSet<>();

    // ==================== 构造方法 ====================

    /**
     * 用于外部权限上传的构造方法
     */
    public Authority(String authorityName, String authorityCode, String authorityDescription,
                    String actions, String resourceType, String resourcePaths,
                    Long parentId, Integer level, Integer sortOrder, String syncSource) {
        this.authorityName = authorityName;
        this.authorityCode = authorityCode;
        this.authorityDescription = authorityDescription;
        this.actions = actions;
        this.resourceType = resourceType != null ? resourceType : "menu";
        this.resourcePaths = resourcePaths;
        this.parentId = parentId;
        this.level = level != null ? level : 1;
        this.sortOrder = sortOrder != null ? sortOrder : 0;
        this.syncSource = syncSource;
        this.syncTime = LocalDateTime.now();
        this.syncStatus = "PENDING";
        this.creatUser = "system";//TODO
        this.updateUser = "system";
        this.creatTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    // ==================== 业务方法 ====================

    /**
     * 更新同步状态
     */
    public void updateSyncStatus(String status) {
        this.syncStatus = status;
        this.syncTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

}