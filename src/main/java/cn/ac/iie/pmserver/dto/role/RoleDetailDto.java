package cn.ac.iie.pmserver.dto.role;

import cn.ac.iie.pmserver.model.Role;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RoleDetailDto {
    private Long roleId;
    private String roleName;
    private String roleDescription;
    private Integer delFlag;
    private String creatUser;
    private LocalDateTime creatTime;
    private String updateUser;
    private LocalDateTime updateTime;
    private Integer roleLevel;

    public RoleDetailDto(Role role) {
        this.roleId = role.getRoleId();
        this.roleName = role.getRoleName();
        this.roleDescription = role.getRoleDescription();
        this.delFlag = role.getDelFlag();
        this.creatUser = role.getCreatUser();
        this.creatTime = role.getCreatTime();
        this.updateUser = role.getUpdateUser();
        this.updateTime = role.getUpdateTime();
        this.roleLevel = role.getRoleLevel();
    }
}
