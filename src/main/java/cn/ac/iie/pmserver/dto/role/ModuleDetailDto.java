package cn.ac.iie.pmserver.dto.role;

import lombok.Data;
import cn.ac.iie.pmserver.model.Module;

import java.time.LocalDateTime;

@Data
public class ModuleDetailDto {
    private Long moduleId;
    private String moduleName;
    private String moduleDescription;
    private String creatUser;
    private LocalDateTime creatTime;
    private String updateUser;
    private LocalDateTime updateTime;

    public ModuleDetailDto(Module module) {
        this.moduleId = module.getModuleId();
        this.moduleName = module.getModuleName();
        this.moduleDescription = module.getModuleDescription();
        this.creatUser = module.getCreatUser();
        this.creatTime = module.getCreatTime();
        this.updateUser = module.getUpdateUser();
        this.updateTime = module.getUpdateTime();
    }
}
