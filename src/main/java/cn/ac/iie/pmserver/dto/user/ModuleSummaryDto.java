package cn.ac.iie.pmserver.dto.user;

import cn.ac.iie.pmserver.model.Module;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * description: 用户首页
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class ModuleSummaryDto {
    private Long moduleId;
    private String moduleName;
    private String moduleDescription;
    private String moduleUrl;
    private Long parentId;
    private Integer delFlag;
    private String pointUserPhone;
    private LocalDateTime updateTime;
    private List<Long> appIds;
    private List<Long> roleIds;

    public ModuleSummaryDto() {
    }

    public ModuleSummaryDto(Module module) {
        this.moduleId = module.getModuleId();
        this.moduleName = module.getModuleName();
        this.moduleDescription = module.getModuleDescription();
        this.moduleUrl = module.getModuleUrl();
        this.parentId = module.getParentId();
        this.delFlag = module.getDelFlag();
        this.pointUserPhone = module.getPointUserPhone();
        this.updateTime = module.getUpdateTime();

        // 设置关联的应用ID
        this.appIds = module.getApps().stream()
                .map(app -> app.getAppId())
                .collect(Collectors.toList());

        // 设置关联的角色ID
        this.roleIds = module.getRoles().stream()
                .map(role -> role.getRoleId())
                .collect(Collectors.toList());
    }
}