package cn.ac.iie.pmserver.dto.authority;

import cn.ac.iie.pmserver.dto.role.RoleAppDTO;
import cn.ac.iie.pmserver.dto.user.UserAppDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 资源权限分配状态DTO
 * 用于表示应用下特定资源类型的权限分配给用户和角色的情况
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "资源权限分配状态DTO")
public class ResourceAuthorityAssignmentDto {
    
    // 应用信息
    @Schema(description = "应用ID")
    private Long appId;
    @Schema(description = "应用名称")
    private String appName;
    
    // 查询条件
    @Schema(description = "资源类型")
    private String resourceType;
    
    // 权限分配信息
    @Schema(description = "权限分配列表")
    private List<AuthorityAssignmentInfo> authorities;
    
    /**
     * 权限分配信息
     * 表示单个权限及其分配给的用户和角色
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class AuthorityAssignmentInfo {
        // 权限基本信息
        @Schema(description = "权限ID")
        private Long authorityId;
        @Schema(description = "权限名称")
        private String authorityName;
        @Schema(description = "权限编码")
        private String authorityCode;
        @Schema(description = "权限描述")
        private String authorityDescription;
        @Schema(description = "资源类型")
        private String resourceType;
        @Schema(description = "操作列表")
        private String actions;
        @Schema(description = "模块ID")
        private Long moduleId;
        @Schema(description = "模块名称")
        private String moduleName;
        @Schema(description = "模块描述")
        private String moduleDescription;
        
        // 分配信息
        @Schema(description = "分配给该权限的角色列表")
        private List<RoleAppDTO> assignedRoles;
        @Schema(description = "分配给该权限的用户列表（通过角色获得）")
        private List<UserAppDTO> assignedUsers;
        
        // 统计信息
        @Schema(description = "分配的角色数量")
        private Integer roleCount;
        @Schema(description = "分配的用户数量")
        private Integer userCount;
    }
}
