package cn.ac.iie.pmserver.dto.role;

import cn.ac.iie.pmserver.model.User;
import lombok.Data;

@Data
public class UserSimpleDto {
    private Long userId;
    private String name;
    private Integer sex;
    private String phone;
    private String email;
    private Integer delFlag;

    public UserSimpleDto(User user) {
        this.userId = user.getUserId();
        this.name = user.getName();
        this.sex = user.getSex();
        this.phone = user.getPhone();
        this.email = user.getEmail();
        this.delFlag = user.getDelFlag();
    }
}
