package cn.ac.iie.pmserver.dto.authority;

import cn.ac.iie.pmserver.model.Authority;
import cn.ac.iie.pmserver.utils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 权限展示DTO
 * 用于统一权限管理平台的权限数据展示
 */
@Data
@NoArgsConstructor
@Schema(description = "权限展示DTO")
public class AuthorityDisplayDto {
    
    @Schema(description = "权限ID")
    private Long authorityId;
    
    @Schema(description = "权限名称")
    private String authorityName;
    
    @Schema(description = "权限编码")
    private String authorityCode;
    
    @Schema(description = "权限说明")
    private String authorityDescription;
    
    @Schema(description = "资源类型")
    private String resourceType;

    @Schema(description = "操作列表")
    private List<String> actions;

    @Schema(description = "资源路径列表")
    private List<String> resourcePaths;
    
    @Schema(description = "父权限ID")
    private Long parentId;
    
    @Schema(description = "权限层级")
    private Integer level;
    
    @Schema(description = "排序号")
    private Integer sortOrder;
    
    // 关联信息
    @Schema(description = "所属应用名称")
    private String appName;
    
    @Schema(description = "所属模块名称")
    private String moduleName;
    
    @Schema(description = "所属模块ID")
    private Long moduleId;
    
    // 同步信息
    @Schema(description = "同步来源应用")
    private String syncSource;
    
    @Schema(description = "同步时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime syncTime;
    
    @Schema(description = "同步状态")
    private String syncStatus;
    
    // 基础信息
    @Schema(description = "创建人")
    private String creatUser;
    
    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime creatTime;
    
    @Schema(description = "更新人")
    private String updateUser;
    
    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    // 子权限列表（用于树形结构）
    @Schema(description = "子权限列表")
    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<AuthorityDisplayDto> children;
    
    /**
     * 从Authority实体构造DTO
     */
    public AuthorityDisplayDto(Authority authority) {
        this.authorityId = authority.getAuthorityId();
        this.authorityName = authority.getAuthorityName();
        this.authorityCode = authority.getAuthorityCode();
        this.authorityDescription = authority.getAuthorityDescription();
        this.resourceType = authority.getResourceType();
        this.parentId = authority.getParentId();
        this.level = authority.getLevel();
        this.sortOrder = authority.getSortOrder();
        
        // 解析操作列表
        this.actions = parseJsonArray(authority.getActions());
        
        // 解析资源路径列表
        this.resourcePaths = parseJsonArray(authority.getResourcePaths());
        
        // 获取关联的应用和模块信息（取第一个）
        if (authority.getModules() != null && !authority.getModules().isEmpty()) {
            var module = authority.getModules().iterator().next();
            this.moduleId = module.getModuleId();
            this.moduleName = module.getModuleName();
            
            if (module.getApps() != null && !module.getApps().isEmpty()) {
                this.appName = module.getApps().iterator().next().getAppName();
            }
        }
        
        // 同步信息
        this.syncSource = authority.getSyncSource();
        this.syncTime = authority.getSyncTime();
        this.syncStatus = authority.getSyncStatus();
        
        // 基础信息
        this.creatUser = authority.getCreatUser();
        this.creatTime = authority.getCreatTime();
        this.updateUser = authority.getUpdateUser();
        this.updateTime = authority.getUpdateTime();
    }
    
    /**
     * 解析JSON数组字符串
     */
    private List<String> parseJsonArray(String jsonStr) {
        if (jsonStr == null || jsonStr.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        try {
            @SuppressWarnings("unchecked")
            List<String> result = JsonUtils.fromJson(jsonStr, List.class);
            return result != null ? result : new ArrayList<>();
        } catch (Exception e) {
            // 如果JSON解析失败，尝试按逗号分割
            return List.of(jsonStr.split(","));
        }
    }
    
    /**
     * 添加子权限
     */
    public void addChild(AuthorityDisplayDto child) {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        this.children.add(child);
    }

    /**
     * 获取子权限列表
     */
    public List<AuthorityDisplayDto> getChildren() {
        return this.children;
    }

    /**
     * 设置子权限列表
     */
    public void setChildren(List<AuthorityDisplayDto> children) {
        this.children = children;
    }
    
    /**
     * 检查是否为根权限
     */
    public boolean isRoot() {
        return this.parentId == null || this.parentId == 0;
    }
    
    /**
     * 检查是否有子权限
     */
    public boolean hasChildren() {
        return this.children != null && !this.children.isEmpty();
    }
    

    
    /**
     * 获取同步状态描述
     */
    public String getSyncStatusDescription() {
        if (syncStatus == null) return "未同步";
        switch (syncStatus.toUpperCase()) {
            case "SUCCESS": return "同步成功";
            case "FAILED": return "同步失败";
            case "PENDING": return "同步中";
            default: return syncStatus;
        }
    }
}
