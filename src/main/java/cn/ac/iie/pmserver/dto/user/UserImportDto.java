package cn.ac.iie.pmserver.dto.user;

import lombok.Data;

import java.util.List;

@Data
public class UserImportDto {
    private String name;         // 姓名
    private String keyId;        // key号
    private String password;     // 密码
    private String userCode;     // 用户编码
    private String email;        // 邮箱
    private String phone;        // 电话号
    private String departmentCode; // 部门编码
    private String departmentName; // 部门名称
    private Integer sex;         // 性别(0女/1男)
    private Integer delFlag;     // 状态(0启用/1禁用)
    private String appCode;      // 应用编码

    private List<String> roleCodes; // 新增：角色编码列表
}
