package cn.ac.iie.pmserver.dto.user;

import cn.ac.iie.pmserver.model.App;
import lombok.Data;
import java.util.List;
import java.util.stream.Collectors;


/**
 * <p>
 * description: 用户首页
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class AppSummaryDto {
    private Long appId;
    private String appName;
    private String appDescription;
    private String appLogo;
    private Integer delFlag;
    private Integer aliveFlag;
    private String appUrl;
    private List<Long> roleIds;
    private List<Long> moduleIds;

    public AppSummaryDto() {
    }

    public AppSummaryDto(App app) {
        this.appId = app.getAppId();
        this.appName = app.getAppName();
        this.appDescription = app.getAppDescription();
        this.appLogo = app.getAppLogo();
        this.delFlag = app.getDelFlag();
        this.aliveFlag = app.getAliveFlag();
        this.appUrl = app.getAppUrl();

        // 设置关联的角色ID
        this.roleIds = app.getRoles().stream()
                .map(role -> role.getRoleId())
                .collect(Collectors.toList());

        // 设置关联的模块ID
        this.moduleIds = app.getModules().stream()
                .map(module -> module.getModuleId())
                .collect(Collectors.toList());
    }
}