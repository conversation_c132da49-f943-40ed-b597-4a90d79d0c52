package cn.ac.iie.pmserver.dto.role;

import cn.ac.iie.pmserver.model.App;
import cn.ac.iie.pmserver.model.Role;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class RoleAppDetailDto {
    private Long roleId;
    private String roleName;
    private String roleDescription;
    private Integer delFlag;
    private String creatUser;
    private LocalDateTime creatTime;
    private String updateUser;
    private LocalDateTime updateTime;
    private String parentRoleCode;
    private String parentRoleName;  // 新增父角色名称字段
    private Integer roleLevel;

    private Long appId;
    private String appName;
    private String appUrl;

    public RoleAppDetailDto(Role role, App app, String parentRoleName) {  // 修改构造函数
        this.roleId = role.getRoleId();
        this.roleName = role.getRoleName();
        this.roleDescription = role.getRoleDescription();
        this.delFlag = role.getDelFlag();
        this.creatUser = role.getCreatUser();
        this.creatTime = role.getCreatTime();
        this.updateUser = role.getUpdateUser();
        this.updateTime = role.getUpdateTime();
        this.parentRoleCode = role.getParentRoleCode();
        this.parentRoleName = parentRoleName;  // 设置父角色名称
        this.roleLevel = role.getRoleLevel();

        this.appId = app.getAppId();
        this.appName = app.getAppName();
        this.appUrl = app.getAppUrl();
    }
}