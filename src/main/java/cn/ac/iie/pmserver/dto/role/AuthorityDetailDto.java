package cn.ac.iie.pmserver.dto.role;

import cn.ac.iie.pmserver.model.Authority;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AuthorityDetailDto {
    private Long authorityId;
    private String authorityName;
    private String authorityDescription;
//    private Integer delFlag;
    private String creatUser;
    private LocalDateTime creatTime;

    public AuthorityDetailDto(Authority authority) {
        this.authorityId = authority.getAuthorityId();
        this.authorityName = authority.getAuthorityName();
        this.authorityDescription = authority.getAuthorityDescription();
//        this.delFlag = authority.getDelFlag();
        this.creatUser = authority.getCreatUser();
        this.creatTime = authority.getCreatTime();
    }
}
