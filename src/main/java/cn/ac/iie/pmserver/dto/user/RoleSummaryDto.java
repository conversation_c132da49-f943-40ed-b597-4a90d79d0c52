package cn.ac.iie.pmserver.dto.user;

import cn.ac.iie.pmserver.model.Role;
import lombok.Data;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * description: 用户首页
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class RoleSummaryDto {
    private Long roleId;
    private String roleName;
    private String roleDescription;
    private Integer delFlag;
    private Integer memberCount;
    private List<AuthoritySummaryDto> authorities;
    private List<Long> appIds;
    private List<Long> moduleIds;

    public RoleSummaryDto() {
    }

    public RoleSummaryDto(Role role) {
        this.roleId = role.getRoleId();
        this.roleName = role.getRoleName();
        this.roleDescription = role.getRoleDescription();
        this.delFlag = role.getDelFlag();

        // 设置权限信息
        this.authorities = role.getAuthorities().stream()
                .map(auth -> new AuthoritySummaryDto(
                        auth.getAuthorityId(),
                        auth.getAuthorityName(),
                        auth.getAuthorityDescription(),
                        auth.getActions()))
                .collect(Collectors.toList());

        // 设置关联的应用ID
        this.appIds = role.getApps().stream()
                .map(app -> app.getAppId())
                .collect(Collectors.toList());

        // 设置关联的模块ID
        this.moduleIds = role.getModules().stream()
                .map(module -> module.getModuleId())
                .collect(Collectors.toList());
    }
}