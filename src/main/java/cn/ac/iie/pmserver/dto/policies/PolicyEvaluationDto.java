package cn.ac.iie.pmserver.dto.policies;

import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.util.Map;

/**
 * 策略评估请求DTO
 * 用于策略评估接口的参数传递
 */
@Data
public class PolicyEvaluationDto {
    
    /**
     * 用户ID
     */
    @NotNull(message = "用户ID不能为空")
    private Long userId;
    
    /**
     * 资源路径
     * 例如：/api/users/create、/api/roles/delete
     */
    @NotBlank(message = "资源路径不能为空")
    private String resource;
    
    /**
     * 操作类型
     * 例如：read、create、update、delete
     */
    @NotBlank(message = "操作不能为空")
    private String action;
    
    /**
     * 上下文信息
     * 包含IP地址、时间、用户代理等额外信息
     */
    private Map<String, Object> context;
    
    /**
     * 角色ID列表
     * 可选，如果不提供则从用户信息中获取
     */
    private java.util.List<Long> roleIds;
    
    /**
     * 应用ID
     * 可选，用于应用级别的策略评估
     */
    private Long appId;
    
    /**
     * 是否详细模式
     * true-返回详细的评估过程，false-仅返回结果
     */
    private Boolean verbose = false;
}
