package cn.ac.iie.pmserver.dto.app;


import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;
@Data
public class AppQueryDTO {

    private String keywords;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime creatTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime endTime;

    private Integer delFlag;  //禁用状态，0-正常，1-禁用，默认0

    private Integer aliveFlag;//应用的连通状态，0-正常，1-异常
}
