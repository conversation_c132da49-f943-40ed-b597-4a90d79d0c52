package cn.ac.iie.pmserver.dto.policies;

import cn.ac.iie.pmserver.dto.app.AppDto;
import cn.ac.iie.pmserver.dto.role.RoleDto;
import cn.ac.iie.pmserver.dto.user.UserDto;
import cn.ac.iie.pmserver.model.Policies;
import cn.ac.iie.pmserver.utils.JsonUtils;
import cn.ac.iie.pmserver.utils.LogUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 策略数据传输对象（DTO）
 * 用于在系统各层之间传输策略数据
 */
@Data
@NoArgsConstructor
public class PoliciesDto {

    private static final Logger logger = LogUtils.get(PoliciesDto.class);
    
    private Long policyId;
    
    @NotBlank(message = "策略名称不能为空")
    private String policyName;
    
    @NotBlank(message = "策略描述不能为空")
    private String policyDescription;
    
    @NotBlank(message = "策略编码不能为空")
    private String policyCode;
    
    @NotBlank(message = "策略类型不能为空")
    private String policyType;
    
    @NotBlank(message = "策略效果不能为空")
    private String effect;
    
    /**
     * 策略条件（JSON格式）
     * 包含角色、时间范围、IP白名单等条件
     */
    private Map<String, Object> conditions;
    
    /**
     * 适用资源列表
     */
    private List<String> resources;
    
    /**
     * 适用操作列表
     */
    private List<String> actions;
    
    @NotNull(message = "策略优先级不能为空")
    private Integer priority;
    
    /**
     * 策略状态
     * 0-启用，1-禁用，2-草稿
     */
    private Integer status;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime effectiveTime;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime expireTime;
    
    private Integer delFlag;
    
    private String creatUser;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime creatTime;
    
    private String updateUser;
    
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime;
    
    // 关联数据
    private List<RoleDto> roles;
    private List<UserDto> users;
    private List<AppDto> apps;
    
    /**
     * 从Policies实体构造DTO
     * @param policies 策略实体
     */
    public PoliciesDto(Policies policies) {
        this.policyId = policies.getPolicyId();
        this.policyName = policies.getPolicyName();
        this.policyDescription = policies.getPolicyDescription();

        // 优先使用实体中的字段，如果为空则生成默认值
        this.policyCode = policies.getPolicyCode() != null ?
            policies.getPolicyCode() : generatePolicyCode(policies);
        this.policyType = policies.getPolicyType() != null ?
            policies.getPolicyType() : determinePolicyType(policies);
        this.effect = policies.getEffect() != null ?
            policies.getEffect() : determineEffect(policies);

        // 解析JSON字段
        this.conditions = parseConditions(policies.getConditions());
        this.resources = parseResources(policies.getResources());
        this.actions = parseActions(policies.getActions());

        // 使用实体字段或生成默认值
        this.priority = policies.getPriority() != null ?
            policies.getPriority() : determinePriority(policies);
        this.status = policies.getStatus() != null ?
            policies.getStatus() : determineStatus(policies);
        this.effectiveTime = policies.getEffectiveTime() != null ?
            policies.getEffectiveTime() : determineEffectiveTime(policies);
        this.expireTime = policies.getExpireTime() != null ?
            policies.getExpireTime() : determineExpireTime(policies);

        this.delFlag = policies.getDelFlag();
        this.creatUser = policies.getCreatUser();
        this.creatTime = policies.getCreatTime();
        this.updateUser = policies.getUpdateUser();
        this.updateTime = policies.getUpdateTime();

        // 转换关联数据
        this.roles = policies.getRoles() != null ?
            policies.getRoles().stream()
                .map(RoleDto::new)
                .collect(Collectors.toList()) : new ArrayList<>();

        // ❌ 删除：策略不应该直接关联用户，应该通过角色关联（符合RBAC原则）
        this.users = new ArrayList<>();

        this.apps = policies.getApps() != null ?
            policies.getApps().stream()
                .map(AppDto::new)
                .collect(Collectors.toList()) : new ArrayList<>();
    }
    
    /**
     * 生成策略编码
     */
    private String generatePolicyCode(Policies policies) {
        if (policies.getPolicyName() == null) {
            return "policy_" + policies.getPolicyId();
        }
        
        String code = policies.getPolicyName()
            .toLowerCase()
            .replaceAll("[\\s\\u4e00-\\u9fa5]+", "_")
            .replaceAll("[^a-z0-9_]", "")
            .replaceAll("_{2,}", "_")
            .replaceAll("^_|_$", "");
            
        return code.isEmpty() ? "policy_" + policies.getPolicyId() : code + "_policy";
    }
    
    /**
     * 确定策略类型
     */
    private String determinePolicyType(Policies policies) {
        // 基于策略名称或描述判断类型
        String name = policies.getPolicyName();
        String desc = policies.getPolicyDescription();
        
        if ((name != null && name.contains("角色")) || 
            (desc != null && desc.contains("角色"))) {
            return "RBAC";
        } else if ((name != null && name.contains("属性")) || 
                   (desc != null && desc.contains("属性"))) {
            return "ABAC";
        } else {
            return "ACL";
        }
    }
    
    /**
     * 确定策略效果
     */
    private String determineEffect(Policies policies) {
        String name = policies.getPolicyName();
        String desc = policies.getPolicyDescription();
        
        if ((name != null && (name.contains("拒绝") || name.contains("禁止"))) ||
            (desc != null && (desc.contains("拒绝") || desc.contains("禁止")))) {
            return "DENY";
        } else {
            return "ALLOW";
        }
    }
    
    /**
     * 解析策略条件
     */
    private Map<String, Object> parseConditions(String conditionsJson) {
        if (conditionsJson != null && !conditionsJson.trim().isEmpty()) {
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> result = JsonUtils.fromJson(conditionsJson, Map.class);
                return result;
            } catch (Exception e) {
                logger.warn("解析策略条件失败: {}", conditionsJson);
            }
        }

        // 返回默认条件
        return Map.of(
            "roles", List.of(),
            "timeRange", Map.of("start", "00:00", "end", "23:59"),
            "ipWhitelist", List.of()
        );
    }

    /**
     * 解析适用资源
     */
    private List<String> parseResources(String resourcesJson) {
        if (resourcesJson != null && !resourcesJson.trim().isEmpty()) {
            try {
                @SuppressWarnings("unchecked")
                List<String> result = JsonUtils.fromJson(resourcesJson, List.class);
                return result;
            } catch (Exception e) {
                logger.warn("解析策略资源失败: {}", resourcesJson);
            }
        }

        // 返回默认资源
        return List.of("/**");
    }

    /**
     * 解析适用操作
     */
    private List<String> parseActions(String actionsJson) {
        if (actionsJson != null && !actionsJson.trim().isEmpty()) {
            try {
                @SuppressWarnings("unchecked")
                List<String> result = JsonUtils.fromJson(actionsJson, List.class);
                return result;
            } catch (Exception e) {
                logger.warn("解析策略操作失败: {}", actionsJson);
            }
        }

        // 返回默认操作
        return List.of("read");
    }
    
    /**
     * 确定策略优先级
     */
    private Integer determinePriority(Policies policies) {
        // 基于策略名称确定优先级
        String name = policies.getPolicyName();
        if (name != null) {
            if (name.contains("超级") || name.contains("系统")) {
                return 1000;
            } else if (name.contains("管理")) {
                return 500;
            } else {
                return 100;
            }
        }
        return 100;
    }
    
    /**
     * 确定策略状态
     */
    private Integer determineStatus(Policies policies) {
        // 基于delFlag确定状态
        return policies.getDelFlag() != null && policies.getDelFlag() == 1 ? 1 : 0;
    }
    
    /**
     * 确定生效时间
     */
    private LocalDateTime determineEffectiveTime(Policies policies) {
        // 如果创建时间存在，使用创建时间，否则使用当前时间
        return policies.getCreatTime() != null ? policies.getCreatTime() : LocalDateTime.now();
    }
    
    /**
     * 确定失效时间
     */
    private LocalDateTime determineExpireTime(Policies policies) {
        // 默认一年后失效
        LocalDateTime effectiveTime = determineEffectiveTime(policies);
        return effectiveTime.plusYears(1);
    }
}
