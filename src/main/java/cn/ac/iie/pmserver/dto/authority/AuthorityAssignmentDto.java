package cn.ac.iie.pmserver.dto.authority;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 权限分配状态DTO
 * 用于表示应用下用户或角色在各个模块的权限分配情况
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "权限分配状态DTO")
public class AuthorityAssignmentDto {
    
    // 应用信息
    @Schema(description = "应用ID")
    private Long appId;
    @Schema(description = "应用名称")
    private String appName;
    
    // 用户信息（查询用户权限时使用）
    @Schema(description = "用户ID")
    private Long userId;
    @Schema(description = "用户名称")
    private String userName;
    
    // 角色信息（查询角色权限时使用）
    @Schema(description = "角色ID")
    private Long roleId;
    @Schema(description = "角色名称")
    private String roleName;
    
    // 按模块组织的权限分配信息
    @Schema(description = "模块权限列表")
    private List<ModuleAuthorityDto> modules;
    
    /**
     * 模块权限DTO
     * 表示单个模块下的权限分配情况
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ModuleAuthorityDto {
        @Schema(description = "模块ID")
        private Long moduleId;
        @Schema(description = "模块名称")
        private String moduleName;
        @Schema(description = "模块描述")
        private String moduleDescription;
        
        // 该模块下的权限分配情况
        @Schema(description = "已分配的权限")
        private List<AuthorityDisplayDto> assignedAuthorities;    // 已分配的权限
        @Schema(description = "未分配的权限")
        private List<AuthorityDisplayDto> unassignedAuthorities;  // 未分配的权限
    }
}
