package cn.ac.iie.pmserver.dto.role;

import cn.ac.iie.pmserver.model.Authority;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.stream.Collectors;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuthorityInfoDto {
    private Long authorityId;
    private String authorityKey;
    private String authorityName;
    private String authorityDescription;
    private String authorityType;
    private String scope;
    private String actions;
    private Integer status;
    private String creator;
    private String createTime;
    private String updater;
    private String updateTime;
//    private String roleNames;

    // 在AuthorityInfoDTO中添加
    public static AuthorityInfoDto fromEntity(Authority authority) {
        return new AuthorityInfoDto(
                authority.getAuthorityId(),
                authority.getAuthorityName().toLowerCase().replace(" ", ":"),
                authority.getAuthorityName(),
                authority.getAuthorityDescription(),
                "operation",
                "global",
                authority.getActions(),
                1,
                authority.getCreatUser(),
                authority.getCreatTime().toString(),
                authority.getUpdateUser(),
                authority.getUpdateTime() != null ? authority.getUpdateTime().toString() : null
        );
    }
}