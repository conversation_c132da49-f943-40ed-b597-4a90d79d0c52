package cn.ac.iie.pmserver.dto.role;

import cn.ac.iie.pmserver.model.App;
import cn.ac.iie.pmserver.model.Role;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NonNull;

import java.time.LocalDateTime;

@Data
@Schema(description = "角色列表精简DTO")
public class RoleListDto {
    @Schema(description = "角色ID")
    private Long roleId;

    @Schema(description = "角色名称")
    private String roleName;

    @Schema(description = "角色描述")
    private String roleDescription;

    @Schema(description = "删除标志")
    private Integer delFlag;

    private String creatUser;  //创建人，非必填

    private LocalDateTime creatTime;  //创建时间，非必填

    private String updateUser;  //更新人，非必填

    private LocalDateTime updateTime;  //更新时间，非必填

    @Schema(description = "所属应用ID")
    private Long appId;

    @Schema(description = "所属应用名称")
    private String appName;

    @Schema(description = "权限数量")
    private Integer authorityCount;

    @Schema(description = "策略数量")
    private Integer policyCount;

    @Schema(description = "用户数量")
    private Integer userCount;

    public RoleListDto(Role role, App app, Integer authorityCount, Integer policyCount) {
        this.roleId = role.getRoleId();
        this.roleName = role.getRoleName();
        this.roleDescription = role.getRoleDescription();
        this.delFlag = role.getDelFlag();
        this.creatUser = role.getCreatUser();
        this.creatTime = role.getCreatTime();
        this.updateUser = role.getUpdateUser();
        this.updateTime = role.getUpdateTime();
        this.userCount = role.getUsers() != null ? role.getUsers().size() : 0;
        this.authorityCount = authorityCount;
        this.policyCount = policyCount;

        if(app != null){
            this.appId = app.getAppId();
            this.appName = app.getAppName();
        }
    }

    public RoleListDto(Role role, App app) {
        this(role, app,
                role.getAuthorities() != null ? role.getAuthorities().size() : 0,
                role.getPolicies() != null ? role.getPolicies().size() : 0);
    }

    public RoleListDto(Role role, App app, Integer authorityCount) {
        this(role, app, authorityCount,
                role.getPolicies() != null ? role.getPolicies().size() : 0);
    }
}
