package cn.ac.iie.pmserver.dto.role;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AuthorityDto {
    private Long authorityId;
    private String authorityKey;
    private String authorityName;
    private String authorityDescription;
    private String authorityType;
    private String scope;
    private String actions;
    private Integer status;
    private String creator;
    private LocalDateTime createTime;
    private String updater;
    private LocalDateTime updateTime;
    private String roleNames;

    public AuthorityDto(Long authorityId, String authorityKey, String authorityName,
                        String authorityDescription, String authorityType, String scope,
                        String actions, Integer status, String creatUser, LocalDateTime creatTime,
                        String updateUser, LocalDateTime updateTime, String roleNames) {
        // 字段赋值逻辑
    }
}