package cn.ac.iie.pmserver.dto.app;

import cn.ac.iie.pmserver.model.App;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;


@Data
public class AppDto {
    @NotNull(message = "appId不能为空")
    private Long appId;  //应用id

    @NotNull(message = "appName不能为空")
    private String appName;   //应用名称

    @NotNull(message = "appUrl不能为空")
    private String appUrl;  //应用域名url

    private String appDescription;  //应用描述

    @NotNull(message = "appCallbackUrl不能为空")
    private String appCallbackUrl; //应用回调地址(接收授权结果通知）

    @NotNull(message = "appLogo不能为空")
    private String appLogo; //应用logo url

    private Integer delFlag;  //禁用状态，0-正常，1-禁用，默认0

    @NotNull(message = "创建人不能为空")
    private String creatUser; //创建人

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime creatTime; //创建时间

    private String updateUser; //更新人

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime; //更新时间

    @NotNull(message = "pointUserPhone不能为空")
    private String pointUserPhone;//创建人联系电话

    @NotNull(message = "aliveFlag不能为空")
    private Integer aliveFlag;//应用的连通状态，0-正常，1-异常

    public AppDto() {
    }

    public AppDto(App app) {
        if(app == null){
            throw new IllegalArgumentException("App实体不能为null");
        }
        this.appId = app.getAppId();
        this.appName = app.getAppName();
        this.appUrl = app.getAppUrl();
        this.appDescription = app.getAppDescription();
        this.appCallbackUrl = app.getAppCallbackUrl();
        this.appLogo = app.getAppLogo();
        this.delFlag = app.getDelFlag();
        this.creatUser = app.getCreatUser();
        this.creatTime = app.getCreatTime();
        this.updateUser = app.getUpdateUser();
        this.updateTime = app.getUpdateTime();
        this.pointUserPhone = app.getPointUserPhone();
        this.aliveFlag = app.getAliveFlag();
    }

}
