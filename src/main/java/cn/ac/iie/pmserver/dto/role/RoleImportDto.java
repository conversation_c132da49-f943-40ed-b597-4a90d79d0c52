package cn.ac.iie.pmserver.dto.role;

import lombok.Data;

import java.util.List;

@Data
public class RoleImportDto {
    private String roleCode;
    private String roleName;
    private Integer delFlag;
    private String appCode;      // 对应resourceCode
    private String parentRoleCode;
    private String roleDescription;
    private Integer roleLevel;
    // 新增子角色编码集合
    private List<String> childRoleCodes;
}
