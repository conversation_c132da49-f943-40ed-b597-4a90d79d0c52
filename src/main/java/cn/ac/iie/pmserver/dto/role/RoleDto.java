package cn.ac.iie.pmserver.dto.role;

import cn.ac.iie.pmserver.model.Role;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Schema(description = "Role DTO")
public class RoleDto {
    @Schema(description = "Role ID")
    private Long roleId;
    
    @Schema(description = "Role name")
    private String roleName;
    
    @Schema(description = "Role description")
    private String roleDescription;
    
    @Schema(description = "Delete flag (0: normal, 1: deleted)")
    private Integer delFlag;
    
    @Schema(description = "Creation user")
    private String creatUser;
    
    @Schema(description = "Creation time")
    private LocalDateTime creatTime;
    
    @Schema(description = "Update user")
    private String updateUser;
    
    @Schema(description = "Update time")
    private LocalDateTime updateTime;
    
    // 角色所属的应用列表
    @Schema(description = "Associated applications")
    private List<AppInfo> apps;
    
    // 已分配的权限策略
    @Schema(description = "Associated authorities")
    private List<AuthorityInfo> authorities;
    
    @Schema(description = "Associated policies")
    private List<PolicyInfo> policies;

    private Integer userCount; // 新增字段，表示该角色下的用户数量
    
    public RoleDto() {
    }

    public RoleDto(Role role) {
        this.roleId = role.getRoleId();
        this.roleName = role.getRoleName();
        this.roleDescription = role.getRoleDescription();
        this.delFlag = role.getDelFlag();
        this.creatUser = role.getCreatUser();
        this.creatTime = role.getCreatTime();
        this.updateUser = role.getUpdateUser();
        this.updateTime = role.getUpdateTime();
        this.userCount = role.getUsers() != null ? role.getUsers().size() : 0; // 计算用户数量
        
        if (role.getApps() != null) {
            this.apps = role.getApps().stream()
                .map(app -> {
                    AppInfo appInfo = new AppInfo();
                    appInfo.setAppId(app.getAppId());
                    appInfo.setAppName(app.getAppName());
                    return appInfo;
                })
                .collect(Collectors.toList());
        }
        
        if (role.getAuthorities() != null) {
            this.authorities = role.getAuthorities().stream()
                .map(auth -> {
                    AuthorityInfo authInfo = new AuthorityInfo();
                    authInfo.setAuthorityId(auth.getAuthorityId());
                    authInfo.setAuthorityName(auth.getAuthorityName());
                    authInfo.setAuthorityDescription(auth.getAuthorityDescription());
                    return authInfo;
                })
                .collect(Collectors.toList());
        }
        
        if (role.getPolicies() != null) {
            this.policies = role.getPolicies().stream()
                .map(policy -> {
                    PolicyInfo policyInfo = new PolicyInfo();
                    policyInfo.setPolicyId(policy.getPolicyId());
                    policyInfo.setPolicyName(policy.getPolicyName());
                    policyInfo.setPolicyDescription(policy.getPolicyDescription());
                    policyInfo.setDelFlag(policy.getDelFlag());
                    return policyInfo;
                })
                .collect(Collectors.toList());
        }
    }
    
    @Data
    @Schema(description = "Application information")
    public static class AppInfo {
        @Schema(description = "Application ID")
        private Long appId;
        
        @Schema(description = "Application name")
        private String appName;
    }
    
    @Data
    @Schema(description = "Authority information")
    public static class AuthorityInfo {
        @Schema(description = "Authority ID")
        private Long authorityId;
        
        @Schema(description = "Authority name")
        private String authorityName;
        
        @Schema(description = "Authority description")
        private String authorityDescription;
    }

    @Data
    @Schema(description = "Policy information")
    public static class PolicyInfo {
        @Schema(description = "Policy ID")
        private Long policyId;
        
        @Schema(description = "Policy name")
        private String policyName;
        
        @Schema(description = "Policy description")
        private String policyDescription;
        
        @Schema(description = "Delete flag (0: normal, 1: deleted)")
        private Integer delFlag;
    }
} 