package cn.ac.iie.pmserver.dto.policies;

import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 策略评估结果DTO
 * 用于返回策略评估的结果信息
 */
@Data
public class PolicyEvaluationResultDto {
    
    /**
     * 决策结果
     * ALLOW-允许，DENY-拒绝
     */
    private String decision;
    
    /**
     * 匹配的策略列表
     */
    private List<PolicyMatchDto> matchedPolicies;
    
    /**
     * 评估耗时（毫秒）
     */
    private Long evaluationTime;
    
    /**
     * 决策原因
     */
    private String reason;
    
    /**
     * 评估详情
     * 详细模式下返回评估过程信息
     */
    private Map<String, Object> details;
    
    /**
     * 策略匹配信息
     */
    @Data
    public static class PolicyMatchDto {
        /**
         * 策略ID
         */
        private Long policyId;
        
        /**
         * 策略名称
         */
        private String policyName;
        
        /**
         * 策略效果
         */
        private String effect;
        
        /**
         * 策略优先级
         */
        private Integer priority;
        
        /**
         * 匹配原因
         */
        private String matchReason;
        
        /**
         * 是否最终决策策略
         */
        private Boolean isFinalDecision;
    }
}
