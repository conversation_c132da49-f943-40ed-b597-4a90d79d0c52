package cn.ac.iie.pmserver.dto.user;

import cn.ac.iie.pmserver.model.User;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * description: 用户首页
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class UserDto {
    private Long userId;
    private String name;
    private String phone;
    private String email;
    private Integer delFlag;
    private LocalDateTime creatTime;
    // 部门信息
    private Long departmentId;
    private String departmentName;
    // 统计信息
    private Integer appCount;
    private Integer roleCount;
    private Integer moduleCount;
    private List<String> roleNames; // 新增角色名称列表

    public UserDto() {
    }

    public UserDto(User user) {
        this.userId = user.getUserId();
        this.name = user.getName();
        this.phone = user.getPhone();
        this.email = user.getEmail();
        this.delFlag = user.getDelFlag();
        this.creatTime = user.getCreatTime();
        
        // 获取第一个部门信息
        user.getDepartments().stream().findFirst().ifPresent(dept -> {
            this.departmentId = dept.getDepartmentId();
            this.departmentName = dept.getDepartmentName();
        });
        
        // 统计信息
        this.roleCount = user.getRoles().size();
        this.appCount = 0; // 需要单独计算
        this.moduleCount = 0; // 需要单独计算
    }
}