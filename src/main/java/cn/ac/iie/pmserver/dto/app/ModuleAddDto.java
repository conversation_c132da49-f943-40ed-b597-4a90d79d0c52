package cn.ac.iie.pmserver.dto.app;

import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ModuleAddDto {

    @NotNull(message = "appId不能为空")
    private Long appId;

    @NotNull(message = "moduleName不能为空")
    private String moduleName;  //模块名称

    private String moduleDescription; //模块描述

    @NotNull(message = "moduleUrl不能为空")
    private String moduleUrl; //模块url

    @NotNull(message = "delFlag不能为空")
    private Integer delFlag; ;  //禁用状态，0-正常，1-禁用，默认0

    @NotNull(message = "creatUser不能为空")
    private String creatUser; //创建人

//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private LocalDateTime creatTime; //创建时间

    private String updateUser; //更新人

//    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
//    private LocalDateTime updateTime; //更新时间

    @NotNull(message = "parentId不能为空")
    private Long parentId;

    @NotNull(message = "pointUserPhone不能为空")
    private String pointUserPhone;//责任人电话

}
