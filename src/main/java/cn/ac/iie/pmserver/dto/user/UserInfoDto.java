package cn.ac.iie.pmserver.dto.user;

import cn.ac.iie.pmserver.model.User;
import cn.ac.iie.pmserver.model.Module;
import cn.ac.iie.pmserver.dto.user.DepartmentSummaryDto;
import cn.ac.iie.pmserver.dto.user.ModuleSummaryDto;
import cn.ac.iie.pmserver.dto.user.RoleSummaryDto;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <p>
 * description: 用户详情页信息
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class UserInfoDto {
    private Long userId;
    private String name;
    private String email;
    private String phone;
    private Integer sex;
    private Integer delFlag;
    private String creatUser;
    private LocalDateTime creatTime;
    private String updateUser;
    private LocalDateTime updateTime;

    private List<DepartmentSummaryDto> departments;
    private Long appCount;
    private List<AppSummaryDto> apps;
    private Long roleCount;
    private List<RoleSummaryDto> roles;
    private Long moduleCount;
    private List<ModuleSummaryDto> modules;

    public UserInfoDto() {
    }

    public UserInfoDto(User user) {
        this.userId = user.getUserId();
        this.name = user.getName();
        this.email = user.getEmail();
        this.phone = user.getPhone();
        this.sex = user.getSex();
        this.delFlag = user.getDelFlag();
        this.creatUser = user.getCreatUser();
        this.creatTime = user.getCreatTime();
        this.updateUser = user.getUpdateUser();
        this.updateTime = user.getUpdateTime();

        // 设置部门信息
        this.departments = user.getDepartments().stream()
                .map(dept -> new DepartmentSummaryDto(
                        dept.getDepartmentId(),
                        dept.getDepartmentName(),
                        dept.getDepartmentDescription()))
                .collect(Collectors.toList());

        // 设置应用信息
        this.apps = user.getApps().stream()
                .map(app -> {
                    AppSummaryDto appSummaryDto = new AppSummaryDto();
                    appSummaryDto.setAppId(app.getAppId());
                    appSummaryDto.setAppName(app.getAppName());
                    appSummaryDto.setAppDescription(app.getAppDescription());
                    appSummaryDto.setAppLogo(app.getAppLogo());
                    appSummaryDto.setDelFlag(app.getDelFlag());
                    appSummaryDto.setAliveFlag(app.getAliveFlag());
                    appSummaryDto.setAppUrl(app.getAppUrl());

                    
                    // 设置关联的角色ID
                    appSummaryDto.setRoleIds(app.getRoles().stream()
                            .map(role -> role.getRoleId())
                            .collect(Collectors.toList()));
                    
                    // 设置关联的模块ID
                    appSummaryDto.setModuleIds(app.getModules().stream()
                            .map(module -> module.getModuleId())
                            .collect(Collectors.toList()));
                    
                    return appSummaryDto;
                })
                .collect(Collectors.toList());
        this.appCount = (long) this.apps.size();

        // 设置角色信息
        this.roles = user.getRoles().stream()
                .map(role -> {
                    RoleSummaryDto roleSummaryDto = new RoleSummaryDto();
                    roleSummaryDto.setRoleId(role.getRoleId());
                    roleSummaryDto.setRoleName(role.getRoleName());
                    roleSummaryDto.setRoleDescription(role.getRoleDescription());
                    roleSummaryDto.setDelFlag(role.getDelFlag());
                    roleSummaryDto.setMemberCount(role.getUsers()!= null? role.getUsers().size() : 0);

                    // 设置权限信息
                    roleSummaryDto.setAuthorities(role.getAuthorities().stream()
                            .map(auth -> new AuthoritySummaryDto(
                                    auth.getAuthorityId(),
                                    auth.getAuthorityName(),
                                    auth.getAuthorityDescription(),
                                    auth.getActions()))
                            .collect(Collectors.toList()));
                    
                    // 设置关联的应用ID
                    roleSummaryDto.setAppIds(role.getApps().stream()
                            .map(app -> app.getAppId())
                            .collect(Collectors.toList()));
                    
                    // 设置关联的模块ID
                    roleSummaryDto.setModuleIds(role.getModules().stream()
                            .map(module -> module.getModuleId())
                            .collect(Collectors.toList()));
                    
                    return roleSummaryDto;
                })
                .collect(Collectors.toList());
        this.roleCount = (long) this.roles.size();

        // 设置模块信息（通过角色获取模块）
        Set<Module> userModules = user.getRoles().stream()
                .flatMap(role -> role.getModules().stream())
                .collect(Collectors.toSet());

        this.modules = userModules.stream()
                .map(module -> {
                    ModuleSummaryDto moduleSummaryDto = new ModuleSummaryDto();
                    moduleSummaryDto.setModuleId(module.getModuleId());
                    moduleSummaryDto.setModuleName(module.getModuleName());
                    moduleSummaryDto.setModuleDescription(module.getModuleDescription());
                    moduleSummaryDto.setModuleUrl(module.getModuleUrl());
                    moduleSummaryDto.setParentId(module.getParentId());
                    moduleSummaryDto.setDelFlag(module.getDelFlag());
                    moduleSummaryDto.setPointUserPhone(module.getPointUserPhone());
                    moduleSummaryDto.setUpdateTime(module.getUpdateTime());
                    
                    // 设置关联的应用ID
                    moduleSummaryDto.setAppIds(module.getApps().stream()
                            .map(app -> app.getAppId())
                            .collect(Collectors.toList()));
                    
                    // 设置关联的角色ID
                    moduleSummaryDto.setRoleIds(module.getRoles().stream()
                            .map(role -> role.getRoleId())
                            .collect(Collectors.toList()));
                    
                    return moduleSummaryDto;
                })
                .collect(Collectors.toList());
        this.moduleCount = (long) this.modules.size();
    }
}