package cn.ac.iie.pmserver.dto.user;

import cn.ac.iie.pmserver.model.Department;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * description: 用户首页
 * </p>
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DepartmentSummaryDto {
    private Long departmentId;
    private String departmentName;
    private String departmentDescription;

    public DepartmentSummaryDto(Department department) {
        this.departmentId = department.getDepartmentId();
        this.departmentName = department.getDepartmentName();
        this.departmentDescription = department.getDepartmentDescription();
    }
}