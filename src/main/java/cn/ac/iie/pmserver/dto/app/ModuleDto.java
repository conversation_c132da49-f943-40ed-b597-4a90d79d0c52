package cn.ac.iie.pmserver.dto.app;

import cn.ac.iie.pmserver.model.Module;
import com.fasterxml.jackson.annotation.JsonFormat;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Data  // Lombok注解，自动生成getter/setter/toString等方法
public class ModuleDto {

    @NotNull(message = "moduleId不能为空")
    private Long moduleId; //模块id

    @NotNull(message = "moduleName不能为空")
    private String moduleName;  //模块名称

    private String moduleDescription; //模块描述

    @NotNull(message = "moduleUrl不能为空")
    private String moduleUrl; //模块url

    private Integer delFlag; ;  //禁用状态，0-正常，1-禁用，默认0

    @NotNull(message = "creatUser不能为空")
    private String creatUser; //创建人

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime creatTime; //创建时间

    private String updateUser; //更新人

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updateTime; //更新时间

    @NotNull(message = "parentId不能为空")
    private Long parentId;

    @NotNull(message = "pointUserPhone不能为空")
    private String pointUserPhone;//责任人电话

    public ModuleDto() {
    }

    public ModuleDto(Module module) {
        if(module == null){
            throw new IllegalArgumentException("module实体不能为null");
        }
        this.moduleId = module.getModuleId();
        this.moduleName = module.getModuleName();
        this.moduleDescription = module.getModuleDescription();
        this.moduleUrl = module.getModuleUrl();
        this.delFlag = module.getDelFlag();
        this.creatUser = module.getCreatUser();
        this.creatTime = module.getCreatTime();
        this.updateUser = module.getUpdateUser();
        this.updateTime = module.getUpdateTime();
        this.pointUserPhone = module.getPointUserPhone();
        this.parentId = module.getParentId();
    }

}
