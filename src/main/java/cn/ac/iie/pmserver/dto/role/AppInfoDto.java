package cn.ac.iie.pmserver.dto.role;

import cn.ac.iie.pmserver.model.App;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppInfoDto {
    private Long appId;
    private String appName;
    private String appUrl;
    private String appDescription;
    private String appCallbackUrl;
    private String appLogo;
    private Integer status;
    private Integer aliveFlag;
    private String creator;
    private String createTime;
    private String updater;
    private String updateTime;
    private String pointUserPhone;

    public static AppInfoDto fromEntity(App app) {
        return new AppInfoDto(
                app.getAppId(),
                app.getAppName(),
                app.getAppUrl(),
                app.getAppDescription(),
                app.getAppCallbackUrl(),
                app.getAppLogo(),
                app.getDelFlag() == 0 ? 1 : 0,
                app.getAliveFlag(),
                app.getCreatUser(),
                app.getCreatTime().toString(),
                app.getUpdateUser(),
                app.getUpdateTime() != null ? app.getUpdateTime().toString() : null,
                app.getPointUserPhone()
        );
    }

}
