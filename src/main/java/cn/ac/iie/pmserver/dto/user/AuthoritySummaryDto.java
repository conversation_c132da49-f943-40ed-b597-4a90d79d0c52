package cn.ac.iie.pmserver.dto.user;

import cn.ac.iie.pmserver.model.Authority;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <p>
 * description: 用户首页
 * </p>
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AuthoritySummaryDto {
    private Long authorityId;
    private String authorityName;
    private String authorityDescription;
    private String actions;

    public AuthoritySummaryDto(Authority authority) {
        this.authorityId = authority.getAuthorityId();
        this.authorityName = authority.getAuthorityName();
        this.authorityDescription = authority.getAuthorityDescription();
        this.actions = authority.getActions();
    }
}