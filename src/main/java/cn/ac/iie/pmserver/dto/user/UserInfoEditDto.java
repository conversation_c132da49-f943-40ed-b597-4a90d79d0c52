package cn.ac.iie.pmserver.dto.user;

import cn.ac.iie.pmserver.model.User;
import cn.ac.iie.pmserver.model.App;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * description: 用户编辑页信息
 * </p>
 *
 * <AUTHOR>
 */
@Data
public class UserInfoEditDto {
    private Long userId;
    private String name;
    private String phone;
    private String email;
    private Integer delFlag;
    private Integer sex;
    private String creatUser;
    private LocalDateTime creatTime;
    private String updateUser;
    private LocalDateTime updateTime;

    private List<AppRoleDTO> apps;

    public UserInfoEditDto() {
    }

    public UserInfoEditDto(User user) {
        this.userId = user.getUserId();
        this.name = user.getName();
        this.email = user.getEmail();
        this.phone = user.getPhone();
        this.sex = user.getSex();
        this.delFlag = user.getDelFlag();
        this.creatUser = user.getCreatUser();
        this.creatTime = user.getCreatTime();
        this.updateUser = user.getUpdateUser();
        this.updateTime = user.getUpdateTime();

        // 获取用户的所有应用
        List<App> userApps = user.getApps().stream()
                .filter(app -> app.getDelFlag() == 0)
                .collect(Collectors.toList());

        // 获取用户在每个应用下的角色信息
        this.apps = userApps.stream()
                .map(app -> {
                    AppRoleDTO appRoleDTO = new AppRoleDTO();
                    appRoleDTO.setAppId(app.getAppId());
                    appRoleDTO.setAppName(app.getAppName());

                    // 获取用户在该应用下已分配的角色
                    List<RoleDTO> assignedRoles = user.getRoles().stream()
                            .filter(role -> role.getApps().contains(app) && role.getDelFlag() == 0)
                            .map(role -> {
                                RoleDTO roleDTO = new RoleDTO();
                                roleDTO.setRoleId(role.getRoleId());
                                roleDTO.setRoleName(role.getRoleName());
                                roleDTO.setRoleDescription(role.getRoleDescription());
                                return roleDTO;
                            })
                            .collect(Collectors.toList());
                    appRoleDTO.setAssignedRoles(assignedRoles);

                    // 获取用户在该应用下未分配的角色
                    List<RoleDTO> unassignedRoles = app.getRoles().stream()
                            .filter(role -> !user.getRoles().contains(role) && role.getDelFlag() == 0)
                            .map(role -> {
                                RoleDTO roleDTO = new RoleDTO();
                                roleDTO.setRoleId(role.getRoleId());
                                roleDTO.setRoleName(role.getRoleName());
                                roleDTO.setRoleDescription(role.getRoleDescription());
                                return roleDTO;
                            })
                            .collect(Collectors.toList());
                    appRoleDTO.setUnassignedRoles(unassignedRoles);

                    return appRoleDTO;
                })
                .collect(Collectors.toList());
    }

    @Data
    public static class AppRoleDTO {
        private Long appId;
        private String appName;
        private List<RoleDTO> assignedRoles;
        private List<RoleDTO> unassignedRoles;
    }

    @Data
    public static class RoleDTO {
        private Long roleId;
        private String roleName;
        private String roleDescription;
    }
} 