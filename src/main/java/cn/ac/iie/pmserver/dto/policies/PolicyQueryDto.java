package cn.ac.iie.pmserver.dto.policies;

import lombok.Data;

/**
 * 策略查询条件DTO
 * 用于封装策略查询的各种条件参数
 */
@Data
public class PolicyQueryDto {
    
    /**
     * 搜索关键字
     * 可以匹配策略名称、策略描述、策略编码
     */
    private String keyword;
    
    /**
     * 策略类型
     * 可选值：RBAC（基于角色）、ABAC（基于属性）、ACL（访问控制列表）
     */
    private String policyType;
    
    /**
     * 策略效果
     * 可选值：ALLOW（允许）、DENY（拒绝）
     */
    private String effect;
    
    /**
     * 策略状态
     * 0-启用，1-禁用，2-草稿
     */
    private Integer status;
    
    /**
     * 创建人
     * 用于查询指定用户创建的策略
     */
    private String creatUser;
    
    /**
     * 创建时间开始
     * 格式：yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss
     */
    private String createTimeStart;
    
    /**
     * 创建时间结束
     * 格式：yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss
     */
    private String createTimeEnd;
    
    /**
     * 更新人
     * 用于查询指定用户更新的策略
     */
    private String updateUser;
    
    /**
     * 更新时间开始
     * 格式：yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss
     */
    private String updateTimeStart;
    
    /**
     * 更新时间结束
     * 格式：yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss
     */
    private String updateTimeEnd;
    
    /**
     * 生效时间开始
     * 格式：yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss
     */
    private String effectiveTimeStart;
    
    /**
     * 生效时间结束
     * 格式：yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss
     */
    private String effectiveTimeEnd;
    
    /**
     * 失效时间开始
     * 格式：yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss
     */
    private String expireTimeStart;
    
    /**
     * 失效时间结束
     * 格式：yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss
     */
    private String expireTimeEnd;
    
    /**
     * 优先级范围开始
     */
    private Integer priorityStart;
    
    /**
     * 优先级范围结束
     */
    private Integer priorityEnd;
    
    /**
     * 角色ID
     * 用于查询分配给指定角色的策略
     */
    private Long roleId;
    
    /**
     * 用户ID
     * 用于查询分配给指定用户的策略
     */
    private Long userId;
    
    /**
     * 应用ID
     * 用于查询关联指定应用的策略
     */
    private Long appId;
    
    /**
     * 资源路径
     * 用于查询适用于指定资源的策略
     */
    private String resourcePath;
    
    /**
     * 操作类型
     * 用于查询适用于指定操作的策略
     */
    private String action;
    
    /**
     * 是否包含已过期策略
     * true-包含，false-不包含
     */
    private Boolean includeExpired = false;
    
    /**
     * 排序字段
     * 可选值：policyName、priority、creatTime、updateTime、effectiveTime
     */
    private String sortBy;
    
    /**
     * 排序方向
     * 可选值：asc（升序）、desc（降序）
     */
    private String sortDirection;
}
