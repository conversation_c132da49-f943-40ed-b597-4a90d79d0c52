package cn.ac.iie.pmserver.dto.app;

import cn.ac.iie.pmserver.model.App;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AppListDTO {

    @NotNull(message = "appId不能为空")
    private Long appId;  //应用id

    @NotNull(message = "appName不能为空")
    private String appName;   //应用名称

    public AppListDTO(App app) {
        this.appId = app.getAppId();
        this.appName = app.getAppName();
    }
}
