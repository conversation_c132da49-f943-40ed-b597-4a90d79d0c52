package cn.ac.iie.pmserver.repository;

import cn.ac.iie.pmserver.dto.user.UserAppDTO;
import cn.ac.iie.pmserver.model.Role;
import cn.ac.iie.pmserver.model.User;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 用户数据访问仓库接口
 * 继承自JpaRepository，提供了基本的CRUD操作
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long>, JpaSpecificationExecutor<User> {
    // 根据用户USB Key的Key号查找用户
    // 使用Optional包装返回结果，避免NullPointerException
    // 方法名遵循Spring Data JPA的命名约定，会自动实现查询逻辑
    Optional<User> findByUserCode(String keyId);
    boolean existsByUserCode(String userCode);

    /**
     * <p>
     * description: 根据用户ID统计关联的应用数量
     * </p>
     *
     * @param userId 用户ID
     * @return: java.lang.Long 应用数量
     */
    @Query("SELECT COUNT(DISTINCT a) FROM User u JOIN u.apps a WHERE u.userId = :userId AND u.delFlag = 0")
    Long countAppsByUserId(@Param("userId") Long userId);

    /**
     * <p>
     * description: 根据用户ID统计关联的模块数量（通过角色获取）
     * </p>
     *
     * @param userId 用户ID
     * @return: java.lang.Long 模块数量
     */
    @Query("SELECT COUNT(DISTINCT m) FROM User u JOIN u.roles r JOIN r.modules m WHERE u.userId = :userId AND u.delFlag = 0")
    Long countModulesByUserId(@Param("userId") Long userId);


    /**
     * <p>
     * description: 根据用户ID查找用户及其所有关联信息（符合RBAC模型）
     * </p>
     *
     * @param userId 用户ID
     * @return: java.util.Optional<cn.ac.iie.pmserver.model.User> 包含所有关联信息的用户对象
     */
    @Query("SELECT DISTINCT u FROM User u " +
           "LEFT JOIN FETCH u.departments " +
           "LEFT JOIN FETCH u.roles r " +
           "LEFT JOIN FETCH r.authorities " +
           "LEFT JOIN FETCH r.modules m " +
           "LEFT JOIN FETCH m.apps " +
           "LEFT JOIN FETCH u.apps " +
           "WHERE u.userId = :userId AND u.delFlag = 0")
    Optional<User> findByIdWithAllRelations(@Param("userId") Long userId);

    /**
     * 根据应用ID查询该应用下的所有用户
     */
    @Query("SELECT new cn.ac.iie.pmserver.dto.user.UserAppDTO(u.userId, u.name, u.delFlag, u.sex) " +
           "FROM User u JOIN u.apps a WHERE a.appId = :appId AND u.delFlag = 0")
    List<UserAppDTO> findUsersByAppId(@Param("appId") Long appId);

    // 在UserRepository接口中添加
    @Query("SELECT DISTINCT u FROM User u " +
            "JOIN u.roles r " +
            "JOIN r.apps a " +  // 通过角色关联应用
            "WHERE r.roleId = :roleId AND a.appId = :appId")
    List<User> findByRoles_RoleIdAndApps_AppId(@Param("roleId") Long roleId, @Param("appId") Long appId);
}