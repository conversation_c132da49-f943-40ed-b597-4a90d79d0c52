package cn.ac.iie.pmserver.repository;

import cn.ac.iie.pmserver.dto.role.RoleAppDTO;
import cn.ac.iie.pmserver.model.App;
import cn.ac.iie.pmserver.model.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface RoleRepository extends JpaRepository<Role, Long>, JpaSpecificationExecutor<Role> {
    @Query("SELECT r FROM Role r " +
           "LEFT JOIN FETCH r.authorities " +
           "LEFT JOIN FETCH r.modules " +
           "LEFT JOIN FETCH r.apps " +
           "LEFT JOIN FETCH r.users " +
           "LEFT JOIN FETCH r.policies " +
           "WHERE r.roleId = :id")
    Optional<Role> findByIdWithAllRelations(@Param("id") Long id);


    /**
     * 根据应用ID查询该应用下的所有角色
     */
    @Query("SELECT new cn.ac.iie.pmserver.dto.role.RoleAppDTO(r.roleId, r.roleName, r.roleDescription, r.delFlag) " +
           "FROM Role r JOIN r.apps a WHERE a.appId = :appId AND r.delFlag = 0")
    List<RoleAppDTO> findRolesByAppId(@Param("appId") Long appId);

    @Query("SELECT r.roleName FROM Role r WHERE r.roleId = :roleId")
    Optional<String> findRoleNameById(@Param("roleId") Long roleId);

    @Query("SELECT DISTINCT r FROM Role r " +
            "LEFT JOIN FETCH r.authorities a " +
            "LEFT JOIN FETCH a.modules m " +
            "LEFT JOIN FETCH m.apps " +
            "LEFT JOIN FETCH r.apps " +
            "WHERE r.roleId = :roleId")
    Optional<Role> findByIdWithAuthoritiesAndApps(@Param("roleId") Long roleId);

    List<Role> findByRoleCodeIn(List<String> roleCodes);

    Optional<Role> findByRoleCode(String roleCode);

    @Query("SELECT r FROM Role r JOIN r.apps a WHERE r.roleCode = :roleCode AND a.appName = :appName")
    Optional<Role> findByRoleCodeAndAppName(@Param("roleCode") String roleCode, @Param("appName") String appName);

    @Query("SELECT r.roleName FROM Role r WHERE r.roleCode = :roleCode")
    Optional<String> findRoleNameByCode(@Param("roleCode") String roleCode);

}
