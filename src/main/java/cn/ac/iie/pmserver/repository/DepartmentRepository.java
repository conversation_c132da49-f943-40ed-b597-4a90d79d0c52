package cn.ac.iie.pmserver.repository;

import cn.ac.iie.pmserver.model.Department;
import cn.ac.iie.pmserver.model.Role;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DepartmentRepository extends JpaRepository<Department, Long>, JpaSpecificationExecutor<Department> {
    // 显式声明查询，返回orderNum的最大值
    @Query("SELECT MAX(d.orderNum) FROM Department d WHERE d.parentId = :parentId")
    Integer findMaxOrderNumByParentId(Long parentId);

    List<Department> findByParentId(Long parentId);

    List<Department> findByParentIdOrderByOrderNum(Long parentId);

    Optional<Department> findByDepartmentCode(String departmentCode);

}
