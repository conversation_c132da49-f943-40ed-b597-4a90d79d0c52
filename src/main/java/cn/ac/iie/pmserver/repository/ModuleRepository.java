package cn.ac.iie.pmserver.repository;

import cn.ac.iie.pmserver.model.Module;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 模块数据访问接口
 */
@Repository
public interface ModuleRepository extends JpaRepository<Module, Long> {

    //  根据appId查询模块列表
    @Query("SELECT m FROM Module m JOIN m.apps a WHERE a.appId = :appId")
    List<Module> findAllByAppId(Long appId);

    // 根据appId分页查询模块列表
    @Query("SELECT m FROM Module m JOIN m.apps a WHERE a.appId = :appId")
    Page<Module> findAllByAppId(Long appId, Pageable pageable);

    // 根据关键字和appId查询模块,关键字(模块名称、模块id、模块url，模块描述),且appId和传入的需要一致
    @Query("SELECT m FROM Module m JOIN m.apps a WHERE " +
            "(m.moduleName LIKE %:keywords% OR " +
            "CAST(m.moduleId AS string) LIKE %:keywords% OR " +
            "m.moduleUrl LIKE %:keywords% OR " +
            "m.moduleDescription LIKE %:keywords%) AND " +
            "a.appId = :appId")
    List<Module> searchModule(String keywords, Long appId);
}
