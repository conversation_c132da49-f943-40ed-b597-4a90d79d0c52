package cn.ac.iie.pmserver.repository;

import cn.ac.iie.pmserver.model.Policies;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

@Repository
public interface PoliciesRepository extends JpaRepository<Policies, Long>, JpaSpecificationExecutor<Policies> {

    /**
     * 根据策略名称检查是否存在
     * @param policyName 策略名称
     * @return 是否存在
     */
    boolean existsByPolicyName(String policyName);
}
