package cn.ac.iie.pmserver.repository;

import cn.ac.iie.pmserver.model.Authority;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface AuthorityRepository extends JpaRepository<Authority, Long>, JpaSpecificationExecutor<Authority> {

    /**
     * 根据权限编码查找权限
     */
    Optional<Authority> findByAuthorityCode(String authorityCode);



    /**
     * 根据应用ID查找权限列表
     */
    @Query("SELECT DISTINCT a FROM Authority a JOIN a.modules m JOIN m.apps app WHERE app.appId = :appId")
    List<Authority> findByAppId(@Param("appId") Long appId);

    /**
     * 根据模块ID查找权限列表
     */
    @Query("SELECT a FROM Authority a JOIN a.modules m WHERE m.moduleId = :moduleId")
    List<Authority> findByModuleId(@Param("moduleId") Long moduleId);



    /**
     * 检查权限编码是否存在
     */
    boolean existsByAuthorityCode(String authorityCode);



    /**
     * 根据应用ID和同步来源查找权限列表
     */
    @Query("SELECT DISTINCT a FROM Authority a JOIN a.modules m JOIN m.apps app WHERE app.appId = :appId AND a.syncSource = :syncSource")
    List<Authority> findByAppIdAndSyncSource(@Param("appId") Long appId, @Param("syncSource") String syncSource);

    /**
     * 根据应用ID和资源类型查找权限列表（包含角色和模块关联信息）
     */
    @Query("SELECT DISTINCT a FROM Authority a " +
           "LEFT JOIN FETCH a.roles r " +
           "LEFT JOIN FETCH a.modules m " +
           "JOIN a.modules mod " +
           "JOIN mod.apps app " +
           "WHERE app.appId = :appId " +
           "AND (:resourceType IS NULL OR a.resourceType = :resourceType)")
    List<Authority> findByAppIdAndResourceTypeWithRoles(@Param("appId") Long appId, @Param("resourceType") String resourceType);

    /**
     * 批量删除角色权限关联
     */
    @Modifying
    @Query(value = "DELETE FROM roles_authorities WHERE authority_id IN :authorityIds", nativeQuery = true)
    void deleteRoleAuthoritiesByAuthorityIds(@Param("authorityIds") List<Long> authorityIds);

    /**
     * 批量删除部门权限关联
     */
    @Modifying
    @Query(value = "DELETE FROM departments_authorities WHERE authority_id IN :authorityIds", nativeQuery = true)
    void deleteDepartmentAuthoritiesByAuthorityIds(@Param("authorityIds") List<Long> authorityIds);

    /**
     * 批量删除模块权限关联
     */
    @Modifying
    @Query(value = "DELETE FROM modules_authorities WHERE authority_id IN :authorityIds", nativeQuery = true)
    void deleteModuleAuthoritiesByAuthorityIds(@Param("authorityIds") List<Long> authorityIds);

}
