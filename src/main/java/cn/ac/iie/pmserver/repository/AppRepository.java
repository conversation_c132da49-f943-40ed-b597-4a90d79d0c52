package cn.ac.iie.pmserver.repository;

import cn.ac.iie.pmserver.dto.app.AppQueryDTO;
import cn.ac.iie.pmserver.dto.app.AppStatisticsDTO;
import cn.ac.iie.pmserver.model.App;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.Optional;

/**
 * 应用数据访问仓库接口
 * 继承自JpaRepository，提供了基本的CRUD操作
 */
@Repository
public interface AppRepository extends JpaRepository<App,Long>, JpaSpecificationExecutor<App> {

    /**
     * 根据多个条件信息搜索应用 (应用名称、应用域名url，应用状态、连通性、应用创建时间范围)
     * @param appQueryDTO 查询条件对象
     * @return 应用列表
     */
    @Query("SELECT a FROM App a WHERE " +
            // 关键词模糊匹配
            "(COALESCE(:#{#appQueryDTO.keywords}) IS NULL OR " +
            "a.appName LIKE %:#{#appQueryDTO.keywords}% OR " +
            "CAST(a.appId AS string) LIKE %:#{#appQueryDTO.keywords}% OR " +
            "a.appUrl LIKE %:#{#appQueryDTO.keywords}% OR " +
            "a.appDescription LIKE %:#{#appQueryDTO.keywords}%) " +

            "AND (COALESCE(:#{#appQueryDTO.delFlag}) IS NULL OR a.delFlag = :#{#appQueryDTO.delFlag}) " +
            "AND (COALESCE(:#{#appQueryDTO.aliveFlag}) IS NULL OR a.aliveFlag = :#{#appQueryDTO.aliveFlag}) " +
            "AND (:#{#appQueryDTO.creatTime} IS NULL OR a.creatTime >= :#{#appQueryDTO.creatTime}) " +
            "AND (:#{#appQueryDTO.endTime} IS NULL OR a.creatTime <= :#{#appQueryDTO.endTime})")
    Page<App> searchApps(@Param("appQueryDTO") AppQueryDTO appQueryDTO,  Pageable pageable);


    /**
     * 获取所有应用的统计信息
     * @return 应用统计信息
     */
    @Query("SELECT new cn.ac.iie.pmserver.dto.app.AppStatisticsDTO(" +
            "COUNT(a), " +
            "SUM(CASE WHEN a.aliveFlag = 0 THEN 1 ELSE 0 END), " +
            "SUM(CASE WHEN a.aliveFlag = 1 THEN 1 ELSE 0 END)) " +
            "FROM App a")
    AppStatisticsDTO  getAllAppsStatistics();


    /**
     * 获取带有分页信息的应用实体集合
     * @param pageable 封装分页参数，如页码、每页记录数
     * @return
     */
    @Query("SELECT a FROM App a")
    Page<App> findAllApps(Pageable pageable);

    Optional<App> findByAppName(String appName);
}
