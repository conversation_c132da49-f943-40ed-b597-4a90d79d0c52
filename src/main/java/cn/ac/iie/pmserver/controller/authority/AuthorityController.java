package cn.ac.iie.pmserver.controller.authority;

import cn.ac.iie.pmserver.dto.authority.AuthorityDisplayDto;
import cn.ac.iie.pmserver.dto.authority.AuthorityAssignmentDto;
import cn.ac.iie.pmserver.dto.authority.ResourceAuthorityAssignmentDto;

import cn.ac.iie.pmserver.request.authority.AuthoritySyncRequest;
import cn.ac.iie.pmserver.response.ApiResponse;
import cn.ac.iie.pmserver.service.AuthorityService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

import org.springframework.data.domain.Page;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.*;


/**
 * 权限管理控制器
 * 提供权限相关的所有API接口
 */
@Slf4j
@RestController
@RequestMapping("/api/authorities")
@Tag(name = "权限管理", description = "权限信息的上传、同步和展示")
@RequiredArgsConstructor
public class AuthorityController {

    private final AuthorityService authorityService;

    /**
     * 批量权限同步
     */
    @PostMapping("/sync")
    @Operation(summary = "批量权限同步", description = "外部应用批量同步权限信息到统一权限管理平台")
    public ApiResponse<?> syncAuthorities(
            @Valid @RequestBody AuthoritySyncRequest request) {
        log.info("接收批量权限同步请求, 应用ID: {}, 权限数量: {}",
            request.getAppId(), request.getAuthorityCount());

        try {
            Map<String, Object> result = authorityService.syncAuthorities(request);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("权限同步失败: {}", e.getMessage(), e);
            return ApiResponse.error(500, "权限同步失败: " + e.getMessage());
        }
    }

    /**
     * 查询权限列表（支持分页和列表两种模式）
     */
    @GetMapping("/search")
    @Operation(summary = "查询权限列表", description = "根据条件查询权限信息，支持分页和列表两种模式")
    public ApiResponse<?> getAuthorities(
            @Parameter(description = "应用ID") @RequestParam(name = "appId", required = false) Long appId,
            @Parameter(description = "模块ID") @RequestParam(name = "moduleId", required = false) Long moduleId,
            @Parameter(description = "关键字（权限名称、编码）") @RequestParam(name = "keyword", required = false) String keyword,
            @Parameter(description = "资源类型") @RequestParam(name = "resourceType", required = false) String resourceType,
            @Parameter(description = "同步来源") @RequestParam(name = "syncSource", required = false) String syncSource,

            @Parameter(description = "页码，不传则返回全部") @RequestParam(name = "page", required = false) Integer page,
            @Parameter(description = "每页大小") @RequestParam(name = "size", defaultValue = "20") Integer size) {

        log.info("查询权限列表, appId: {}, moduleId: {}, keyword: {}, page: {}",
                appId, moduleId, keyword, page);

        try {
            if (page != null) {
                // 分页查询
                Page<AuthorityDisplayDto> result = authorityService.getAuthorities(
                    appId, moduleId, keyword, resourceType, syncSource, page, size);
                return ApiResponse.success(result);
            } else {
                // 列表查询
                List<AuthorityDisplayDto> result = authorityService.getAuthorityList(appId, moduleId, keyword, resourceType, syncSource);
                return ApiResponse.success(result);
            }
        } catch (Exception e) {
            log.error("查询权限列表失败: {}", e.getMessage(), e);
            return ApiResponse.error(500, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 获取权限树形结构
     */
    @GetMapping("/tree")
    @Operation(summary = "获取权限树形结构", description = "获取权限的层级树形结构")
    public ApiResponse<?> getAuthorityTree(
            @Parameter(description = "应用ID") @RequestParam(required = false) Long appId,
            @Parameter(description = "模块ID") @RequestParam(required = false) Long moduleId) {
        log.info("获取权限树形结构, appId: {}, moduleId: {}", appId, moduleId);

        try {
            List<AuthorityDisplayDto> result = authorityService.getAuthorityTree(appId, moduleId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取权限树失败: {}", e.getMessage(), e);
            return ApiResponse.error(500, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID获取权限详情
     */
    @GetMapping("/detail/{id}")
    @Operation(summary = "获取权限详情", description = "根据权限ID获取权限的详细信息")
    public ApiResponse<?> getAuthorityById(
            @Parameter(description = "权限ID") @PathVariable Long id) {
        log.info("获取权限详情, ID: {}", id);

        try {
            AuthorityDisplayDto result = authorityService.getAuthorityById(id);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("获取权限详情失败: {}", e.getMessage(), e);
            return ApiResponse.error(500, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询用户在指定应用下的权限分配情况
     */
    @GetMapping("/assignment/user")
    @Operation(summary = "查询用户权限分配情况", description = "查询用户在指定应用下的权限分配情况，用于前端穿梭框组件")
    public ApiResponse<?> getUserAuthorityAssignment(
            @Parameter(description = "应用ID", required = true) @RequestParam Long appId,
            @Parameter(description = "用户ID", required = true) @RequestParam Long userId) {
        
        try {
            AuthorityAssignmentDto result = authorityService.getUserAuthorityAssignment(appId, userId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("查询用户权限分配情况失败: {}", e.getMessage(), e);
            return ApiResponse.error(500, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询角色在指定应用下的权限分配情况
     */
    @GetMapping("/assignment/role")
    @Operation(summary = "查询角色权限分配情况", description = "查询角色在指定应用下的权限分配情况，用于前端穿梭框组件")
    public ApiResponse<?> getRoleAuthorityAssignment(
            @Parameter(description = "应用ID", required = true) @RequestParam Long appId,
            @Parameter(description = "角色ID", required = true) @RequestParam Long roleId) {
        
        try {
            AuthorityAssignmentDto result = authorityService.getRoleAuthorityAssignment(appId, roleId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("查询角色权限分配情况失败: {}", e.getMessage(), e);
            return ApiResponse.error(500, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询资源在指定应用下的权限分配情况
     */
    @GetMapping("/assignment/resource")
    @Operation(summary = "查询资源权限分配情况", description = "查询指定应用下资源类型的权限分配情况，显示权限分配给哪些用户和角色")
    public ApiResponse<?> getResourceAuthorityAssignment(
            @Parameter(description = "应用ID", required = true) @RequestParam Long appId,
            @Parameter(description = "资源类型（menu/button/api等，可选）") @RequestParam(required = false) String resourceType) {
        
        try {
            ResourceAuthorityAssignmentDto result = authorityService.getResourceAuthorityAssignment(appId, resourceType);
            return ApiResponse.success(result);
        } catch (Exception e) {
            log.error("查询资源权限分配情况失败: {}", e.getMessage(), e);
            return ApiResponse.error(500, "查询失败: " + e.getMessage());
        }
    }

    /**
     * 删除权限
     */
    @PostMapping("/delete")
    @Operation(summary = "删除权限", description = "批量删除指定的权限，同时删除关联表中的数据")
    public ApiResponse<?> deleteAuthorities(
            @Parameter(description = "权限ID列表", required = true)
            @RequestBody List<Long> authorityIds) {

        if (authorityIds == null || authorityIds.isEmpty()) {
            return ApiResponse.error(400, "权限ID列表不能为空");
        }

        log.info("删除权限请求, 权限ID列表: {}", authorityIds);

        try {
            authorityService.deleteAuthorities(authorityIds);
            return ApiResponse.success("删除成功，共删除 " + authorityIds.size() + " 个权限");

        } catch (Exception e) {
            log.error("删除权限失败: {}", e.getMessage(), e);
            return ApiResponse.error(500, "删除权限失败: " + e.getMessage());
        }
    }

    /**
     * 文件导入权限
     */
    @PostMapping("/import")
    @Operation(summary = "文件导入权限", description = "通过文件批量导入权限信息，支持Excel、CSV、JSON格式。每个权限的应用ID和模块ID从文件中读取。")
    public ApiResponse<?> importAuthorities(
            @Parameter(description = "导入文件", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "是否覆盖已存在的权限", required = false)
            @RequestParam(value = "overrideExisting", required = false, defaultValue = "false") Boolean overrideExisting,
            @Parameter(description = "操作人", required = false)
            @RequestParam(value = "operator", required = false, defaultValue = "system") String operator) {

        log.info("接收权限文件导入请求: {}", file.getOriginalFilename());

        try {
            if (file == null || file.isEmpty()) {
                return ApiResponse.error(400, "导入文件不能为空");
            }

            Map<String, Object> result = authorityService.importAuthoritiesFromFile(
                    file, overrideExisting, operator);

            return ApiResponse.success(result);

        } catch (Exception e) {
            log.error("权限文件导入失败: {}", e.getMessage(), e);
            return ApiResponse.error(500, "权限文件导入失败，请重试 " + e.getMessage());
        }
    }

    /**
     * 下载权限导入模板 - 支持多种格式
     */
    @GetMapping("/import/template")
    @Operation(summary = "下载权限导入模板", description = "下载权限导入模板文件，支持xlsx、csv、json格式")
    public void downloadAuthorityTemplate(
            @Parameter(description = "文件格式", example = "xlsx")
            @RequestParam(value = "format", defaultValue = "xlsx") String format,
            HttpServletResponse response) throws IOException {

        log.info("下载权限导入模板，格式: {}", format);

        try {
            authorityService.generateAuthorityTemplate(format, response);
        } catch (Exception e) {
            log.error("下载权限模板失败: {}", e.getMessage(), e);
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "模板下载失败");
        }
    }
}
