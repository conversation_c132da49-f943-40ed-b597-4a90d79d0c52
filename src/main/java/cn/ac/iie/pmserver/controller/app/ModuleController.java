package cn.ac.iie.pmserver.controller.app;

import cn.ac.iie.pmserver.dto.app.ModuleAddDto;
import cn.ac.iie.pmserver.dto.app.ModuleDto;
import cn.ac.iie.pmserver.model.Module;
import cn.ac.iie.pmserver.service.ModuleService;
import cn.ac.iie.pmserver.response.ApiResponse;
import cn.ac.iie.pmserver.utils.LogUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/modules")
@Tag(name = "模块管理", description = "模块管理api")
@RequiredArgsConstructor
public class ModuleController {

    private static final Logger logger = LogUtils.get(ModuleController.class);

    private final ModuleService moduleService;

    @PostMapping("add")
    @Operation(summary = "创建模块",
            description = "创建一个新的模块",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "创建模块成功"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "创建模块失败请检查输入")
            },
            parameters = {
                    @Parameter(name = "module", description = "模块对象请求体", required = true)
            }
    )
    public ApiResponse addModule(@Validated @RequestBody ModuleAddDto moduleAddDto) {
        logger.info("新建模块: {}", moduleAddDto);
        moduleService.addModule(moduleAddDto);
        return ApiResponse.success();
    }

    //获取一个应用的所有模块信息
    @GetMapping("{appId}")
    @Operation(
            summary = "获取一个应用的所有模块信息",
            description = "根据appId获取一个应用的所有模块信息",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功返回模块列表"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "暂无相关模块信息")
            },
            parameters = {
                    @Parameter(name = "appId", description = "应用的唯一标识符", required = true)
            }
    )
    public ApiResponse getModulesByAppId(@PathVariable Long appId) {
        if (appId == null) {
            return ApiResponse.error(400, "appId不能为空");
        }
        if (!appId.toString().matches("^[0-9]+$")) {
            return ApiResponse.error(400, "appId有误" + appId + "请输入数字");
        }
        logger.info("获取一个应用的所有模块信息: appId:{}", appId);
        List<ModuleDto> modules = moduleService.getModulesByAppId(appId);
        if (modules.isEmpty()) {
            return ApiResponse.error(404, "未查询到相关模块信息");
        }
        return ApiResponse.success(modules);
    }

    //分页获取一个应用的所有模块列表
    @GetMapping("page/{appId}")
    @Operation(
            summary = "分页获取一个应用的所有模块列表",
            description = "分页获取一个应用所有模块列表",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功返回模块列表"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "未查询到相关模块信息")
            },
            parameters = {
                    @Parameter()
            }
    )
    public ApiResponse getModulesByAppIdAndPage(@PathVariable Long appId, @RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "5") int size) {
        if (appId == null) {
            logger.info("appId不能为空");
            return ApiResponse.error(400, "appId不能为空");
        }
        if (!appId.toString().matches("^[0-9]+$")) {
            return ApiResponse.error(400, "appId有误" + appId + "请输入数字");
        }
        Pageable pageable = PageRequest.of(page, size);
        Page<ModuleDto> modules = moduleService.getModulesByAppIdAndPage(appId, pageable);
        if (modules.isEmpty()) {
            return ApiResponse.error(404, "未查询到相关模块信息");
        }
        return ApiResponse.success(modules);
    }

    //禁用模块
    @PutMapping("/disable/{moduleId}")
    @Operation(
            summary = "禁用模块",
            description = "禁用模块",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "禁用模块成功"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "禁用模块失败")
            },
            parameters = {
                    @Parameter(name = "moduleId", description = "模块的唯一标识符", required = true)
            }
    )
    public ApiResponse disableModule(@PathVariable Long moduleId) {
        if (moduleId == null) {
            return ApiResponse.error(400, "moduleId不能为空");
        }
        if (!moduleId.toString().matches("^[0-9]+$")) {
            return ApiResponse.error(400, "moduleId有误" + moduleId + "请输入数字");
        }
        logger.info("禁用模块: moduleId:{}", moduleId);
        if (moduleId == null) {
            logger.info("moduleId不能为空");
            return ApiResponse.error(400, "moduleId不能为空");
        }
        boolean result = moduleService.disableModule(moduleId);
        if (!result) {
            return ApiResponse.error(400, "禁用模块失败,暂无id为" + moduleId + "的模块信息");
        }
        return ApiResponse.success(result);
    }
    //修改模块状态

    @PutMapping("/{moduleId}/status")
    @Operation(
            summary = "修改模块状态",
            description = "修改模块状态",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "修改模块状态成功"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "修改模块状态失败")
            },
            parameters = {
                    @Parameter(name = "moduleId", description = "模块的唯一标识符", required = true)
            }
    )
    public ApiResponse updateModuleStatus(@PathVariable Long moduleId) {
        if (moduleId == null) {
            return ApiResponse.error(400, "moduleId不能为空");
        }
        if (!moduleId.toString().matches("^[0-9]+$")) {
            return ApiResponse.error(400, "moduleId有误" + moduleId + "请输入数字");
        }
        logger.info("修改模块状态: moduleId:{}", moduleId);
        moduleService.updateModuleStatus(moduleId);
        return ApiResponse.success();

    }

    //批量禁用模块
    @PutMapping("/BatchDisable")
    @Operation(
            summary = "批量禁用模块",
            description = "批量禁用模块",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "批量禁用模块成功")
            },
            parameters = {
                    @Parameter(name = "moduleIds", description = "模块的唯一标识符列表", required = true)
            }
    )
    public ApiResponse batchDisableModule(@RequestBody List<Long> moduleIds) {
        if (moduleIds == null || moduleIds.size() == 0) {
            return ApiResponse.error(400, "moduleIds不能为空");
        }
        logger.info("批量禁用模块: moduleIds:{}", moduleIds);
        moduleService.batchDisableModule(moduleIds);
        return ApiResponse.success();
    }

    //修改模块信息
    @PutMapping("/{moduleId}")
    @Operation(
            summary = "修改模块信息",
            description = "修改模块信息",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "修改模块信息成功"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "修改模块信息失败")
            },
            parameters = {
                    @Parameter(name = "moduleId", description = "模块的唯一标识符", required = true),
            }
    )
    public ApiResponse updateModule(@PathVariable Long moduleId, @RequestBody @Validated ModuleDto moduleDto) {
        if (moduleId == null) {
            return ApiResponse.error(400, "moduleId不能为空");
        }
        if (!moduleId.toString().matches("^[0-9]+$")) {
            return ApiResponse.error(400, "moduleId有误" + moduleId + "请输入数字");
        }
        if (moduleId != moduleDto.getModuleId()) {
            return ApiResponse.error(400, "moduleId有误,moduleId不一致");
        }
        logger.info("修改模块信息: moduleId:{}", moduleId);
        moduleService.updateModule(moduleId, moduleDto);
        return ApiResponse.success();
    }

    //根据关键字查询模块信息
    @GetMapping("/search")
    @Operation(
            summary = "根据关键字查询模块信息",
            description = "根据关键字查询模块信息",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "查询模块信息成功"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "查询模块信息失败")
            },
            parameters = {
                    @Parameter(name = "keywords", description = "关键字", required = true),
            }
    )
    public ApiResponse searchModule(@RequestParam String keywords, @RequestParam Long appId) {
        if (appId == null) {
            return ApiResponse.error(400, "appId不能为空");
        }
        if (keywords == null || keywords.isBlank()) {
            Page<ModuleDto> modules = moduleService.getModulesByAppIdAndPage(appId, PageRequest.of(0, 5));
            return ApiResponse.success(modules);
        }
        List<ModuleDto> modules = moduleService.searchModule(keywords, appId);
        return ApiResponse.success(modules);
    }

}
