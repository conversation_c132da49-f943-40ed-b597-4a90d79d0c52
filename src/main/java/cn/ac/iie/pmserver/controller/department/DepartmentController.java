package cn.ac.iie.pmserver.controller.department;

import cn.ac.iie.pmserver.model.Department;
import cn.ac.iie.pmserver.service.DepartmentService;
import cn.ac.iie.pmserver.controller.department.vo.DepartmentTreeVo;
import cn.ac.iie.pmserver.response.ApiResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * 部门管理接口
 */
@Tag(name = "部门管理", description = "部门信息维护、层级查询及位置调整接口")
@RestController
@RequestMapping("/api/departments")
@RequiredArgsConstructor
public class DepartmentController {
    private final DepartmentService departmentService;

    /**
     * 获取部门树结构（层级关系）
     * @return 部门树结构列表（包含子部门嵌套信息）
     */
    @Operation(summary = "获取部门树结构", description = "查询所有部门并按层级关系组织为树状结构，用于前端层级展示")
    @GetMapping("/tree")
    public Object getDepartmentTree() {
        return ApiResponse.success(departmentService.getDepartmentTree());
    }

    /**
     * 关键字过滤查询部门（分页）
     * @param keyword 可选查询关键字（匹配部门名称/负责人/描述等字段）
     * @param pageable 分页参数（包含页码、每页大小、排序规则）
     * @return 分页结果（包含当前页部门数据及分页元信息）
     */
//    @Operation(summary = "分页查询部门", description = "根据关键字模糊过滤部门信息，支持分页展示")
//    @GetMapping("/search")
//    public Page<Department> searchDepartments(
//            @Parameter(description = "搜索关键字（如部门名称、负责人）")
//            @RequestParam(required = false) String keyword,
//            @Parameter(description = "分页参数（自动解析page、size、sort等）")
//            Pageable pageable
//    ) {
//        return departmentService.searchDepartments(keyword, pageable);
//    }

    /**
     * 添加新部门（支持顶级或子部门）
     * @param parentId 可选父部门ID（null表示添加为顶级部门）
     * @param departmentName 部门名称（必填，唯一标识）
     * @return 新创建的部门信息（包含自动生成的ID）
     */
    @Operation(summary = "添加部门", description = "创建新部门，支持作为顶级部门或某个父部门的子部门")
    @PostMapping("/add")
    public Object addDepartment(
            @Parameter(description = "父部门ID（可选，null表示顶级部门）")
            @RequestParam(required = false) Long parentId,
            @Parameter(description = "部门名称（必填）", required = true)
            @RequestParam String departmentName
    ) {
        return ApiResponse.success(departmentService.addDepartment(parentId, departmentName));
    }

    /**
     * 编辑部门名称
     * @param id 待修改的部门ID（路径参数）
     * @param newName 新的部门名称（必填）
     */
    @Operation(summary = "修改部门名称", description = "更新指定部门的名称信息")
    @PutMapping("/{id}/name")
    public Object updateDepartmentName(
            @Parameter(description = "部门ID（必填）", required = true)
            @PathVariable Long id,
            @Parameter(description = "新部门名称（必填）", required = true)
            @RequestParam String newName
    ) {
        departmentService.updateDepartmentName(id, newName);
        return ApiResponse.success();
    }

    /**
     * 逻辑删除部门
     * @param id 待删除的部门ID（路径参数）
     */
    @Operation(summary = "删除部门")
    @DeleteMapping("/{id}")
    public Object deleteDepartment(
            @Parameter(description = "部门ID（必填）", required = true)
            @PathVariable Long id
    ) {
        departmentService.deleteDepartment(id);
        return ApiResponse.success();
    }

    /**
     * 调整部门位置（拖拽操作）
     * @param sourceId 被移动的部门ID
     * @param targetId 目标参考部门ID（移动的基准位置）
     * @param position 移动位置标识（before：目标前；after：目标后；inner：目标子级）
     */
    @Operation(summary = "调整部门位置", description = "通过拖拽操作调整部门在层级中的位置（同级前后或作为子部门）")
    @PostMapping("/move")
    public Object moveDepartment(
            @Parameter(description = "被移动的部门ID（必填）", required = true)
            @RequestParam Long sourceId,
            @Parameter(description = "目标参考部门ID（必填）", required = true)
            @RequestParam Long targetId,
            @Parameter(description = "移动位置（before/after/inner）", required = true)
            @RequestParam String position // "before"/"after"/"inner"
    ) {
        departmentService.moveDepartment(sourceId, targetId, position);
        return ApiResponse.success();
    }
}