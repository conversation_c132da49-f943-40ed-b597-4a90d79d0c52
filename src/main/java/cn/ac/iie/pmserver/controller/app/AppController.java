package cn.ac.iie.pmserver.controller.app;

import cn.ac.iie.pmserver.dto.app.*;
import cn.ac.iie.pmserver.model.App;
import cn.ac.iie.pmserver.service.AppService;
import cn.ac.iie.pmserver.response.ApiResponse;
import cn.ac.iie.pmserver.utils.LogUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/apps")
@Tag(name = "应用管理", description = "应用管理api")
@RequiredArgsConstructor
public class AppController {
    // 调用日志工具
    private static final Logger logger = LogUtils.get(AppController.class);

    private final AppService appService;

    /**
     * 新建应用
     *
     * @param app
     * @return
     */
    @PostMapping("add")
    @Operation(
            summary = "新建应用",
            description = "新建应用",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功新建应用"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "参数错误")
            },
            parameters = {
                    @Parameter(name = "app", description = "应用对象", required = true),
            }
    )
    public ApiResponse addApp(@Validated @RequestBody App app) {
        logger.info("新建应用: {}", app);
        appService.addApp(app);
        return ApiResponse.success();
    }

    /**
     * 删除应用
     *
     * @param appIds
     * @return
     */
    @PostMapping("delete")
    @Operation(
            summary = "删除应用",
            description = "根据应用ID列表删除应用（支持批量删除）",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功删除应用"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "参数错误")
            },
            parameters = {
                    @Parameter(name = "appIds", description = "应用唯一标识列表（JSON数组格式）", required = true),
            })
    public ApiResponse deleteApp(@Validated @RequestBody List<Long> appIds) {
        logger.info("删除应用: {}", appIds);
        if (appIds == null || appIds.isEmpty()) {
            return ApiResponse.error(400, "参数错误");
        }
        appService.deleteApp(appIds);
        return ApiResponse.success();
    }
    /**
     * 根据条件信息搜索应用列表
     * @param appQueryDTO
     * @return
     */
    @PostMapping("/search")
    @Operation(
            summary = "根据条件信息搜索应用列表",
            description = "支持分页和动态条件过滤",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功返回应用列表"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "未查询到相关信息"),
            },
            parameters = {
                    @Parameter(name = "appQueryDTO", description = "搜索条件对象", required = false)
            }
    )
    public ApiResponse searchApps(@Validated @RequestBody(required = false) AppQueryDTO appQueryDTO,@RequestParam(defaultValue = "0") int page,  @RequestParam(defaultValue = "5") int size) {
        logger.info("根据关键字搜索应用列表: {},{},{}", appQueryDTO,page,size);
        Pageable pageable = PageRequest.of(page, size);
        Page<AppDto> appDtos = appService.searchApps(appQueryDTO,pageable);
        return ApiResponse.success(appDtos);
    }

    @GetMapping("/get")
    @Operation(
            summary = "获取应用列表",
            description = "用于权限模块应用的列表展示",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功返回应用列表"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "未查询到相关信息"),
            },
            parameters = {
                    @Parameter(name = "AppListDTO", description = "查询返回对象", required = false)
            }
    )
    public ApiResponse getApps() {
        logger.info("获取应用列表");
        List<AppListDTO> appListDTOS = appService.getAppList();
        return ApiResponse.success(appListDTOS);
    }


    /**
     * 分页查询所有应用列表
     *
     * @param page
     * @return Page<AppDto>
     */
    @GetMapping("/page")
    @Operation(
            summary = "分页查询所有应用列表",
            description = "分页查询所有应用列表",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功返回应用列表")
            },
            parameters = {
                    @Parameter(name = "page", description = "页码", required = false),
                    @Parameter(name = "size", description = "每页数量", required = false)
            }
    )
    public ApiResponse getAllAppsByPage(@RequestParam(defaultValue = "0") int page, @RequestParam(defaultValue = "10") int size) {
        logger.info("根据分页参数查询应用: page:{}, size:{}", page, size);
        ;
        Pageable pageable = PageRequest.of(page, size);
        Page<AppDto> allAppsByPage = appService.getAllAppsByPage(pageable);
        return ApiResponse.success(allAppsByPage);

    }

    /**
     * 获取所有应用的统计信息
     *
     * @return
     */
    @GetMapping("statistics")
    @Operation(
            summary = "获取应用统计",
            description = "获取应用统计",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功返回应用统计数据")
            }
    )
    public ApiResponse<AppStatisticsDTO> getAllAppsStatistics() {
        AppStatisticsDTO allAppsStatistics = appService.getAllAppsStatistics();
        return ApiResponse.success(allAppsStatistics);
    }

    /**
     * 获取应用详情信息
     *
     * @param appId
     * @return
     */
    @GetMapping("/{appId}")
    @Operation(
            summary = "获取应用详情信息",
            description = "获取应用详情信息",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "成功返回应用详情信息"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "未查询到相关应用信息")
            },
            parameters = {
                    @Parameter(name = "appId", description = "应用的唯一标识符", required = true)
            }
    )
    public ApiResponse getAppInfoById(@PathVariable Long appId) {
        logger.info("获取应用详情信息: appId:{}", appId);
        if (appId == null || !appId.toString().matches("^[0-9]+$")) {
            return ApiResponse.error(400, "appId不能为空或格式错误");
        }
        AppDetailDto appDetailDto = appService.getAppInfoById(appId);
        return ApiResponse.success(appDetailDto);
    }

    /**
     * 根据appID禁用应用
     *
     * @param appId
     * @return ApiResponse
     */

    @PutMapping({"/disable/{appId}"})
    @Operation(
            summary = "禁用应用",
            description = "禁用应用",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "禁用应用成功"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "禁用应用失败")
            },
            parameters = {
                    @Parameter(name = "appId", description = "应用的唯一标识符", required = true)
            }
    )
    public ApiResponse disableAppByAppId(@PathVariable Long appId) {
        if (appId == null || !appId.toString().matches("^[0-9]+$")) {
            return ApiResponse.error(400, "appId不能为空或格式错误");
        }
        boolean b = appService.disableApp(appId);
        if (!b) {
            return ApiResponse.error(400, String.format("禁用应用失败，未查询到id为%s的相关应用信息", appId));
        }
        return ApiResponse.success();
    }

    //启用应用
    @PutMapping({"/enable/{appId}"})
    @Operation(
            summary = "启用应用",
            description = "启用应用",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "启用应用成功"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "启用应用失败")
            },
            parameters = {
                    @Parameter(name = "appId", description = "应用的唯一标识符", required = true)
            }
    )
    public ApiResponse enableAppByAppId(@PathVariable Long appId) {
        if (appId == null) {
            return ApiResponse.error(400, "appId不能为空");
        }
        if (!appId.toString().matches("^[0-9]+$")) {
            return ApiResponse.error(400, "appId有误" + appId + "请输入数字");
        }
        boolean b = appService.enableApp(appId);
        if (!b) {
            return ApiResponse.error(400, String.format("启用应用失败,暂无id为%s的应用", appId));
        }
        return ApiResponse.success();
    }

    //批量禁用应用
    @PutMapping("/BatchDisable")
    @Operation(
            summary = "批量禁用应用",
            description = "批量禁用应用",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "批量禁用应用成功")

            },
            parameters = {
                    @Parameter(name = "appIds", description = "应用的唯一标识符列表", required = true)
            }
    )
    public ApiResponse batchDisableApp(@RequestBody List<Long> appIds) {
        appService.batchDisableApp(appIds);
        return ApiResponse.success();
    }

    @PostMapping("/{appId}")
    @Operation(
            summary = "修改应用信息",
            description = "修改应用信息",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "修改应用信息成功"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "修改应用信息失败")
            },
            parameters = {
                    @Parameter(name = "appId", description = "应用的唯一标识符", required = true)
            }
    )
    public ApiResponse updateApp(@PathVariable Long appId, @RequestBody @Validated AppDto appDto) {
        if (appId == null) {
            return ApiResponse.error(400, "appId不能为空", appId);
        }
        if (appDto.getAppId() != appId) {
            return ApiResponse.error(400, "appId有误" + appId + "请输入数字");
        }
        appService.updateApp(appId, appDto);
        return ApiResponse.success();
    }

    //修改应用状态
    @PutMapping("/status/{appId}")
    @Operation(
            summary = "修改应用状态",
            description = "修改应用状态",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "修改应用状态成功"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "修改应用状态失败")
            },
            parameters = {
                    @Parameter(name = "appId", description = "应用的唯一标识符", required = true)
            }
    )
    public ApiResponse updateAppStatus(@PathVariable Long appId) {
        if (appId == null) {
            return ApiResponse.error(400, "appId不能为空");
        }
        if (!appId.toString().matches("^[0-9]+$")) {
            return ApiResponse.error(400, "appId有误" + appId + "请输入数字");
        }
        appService.updateAppStatus(appId);
        return ApiResponse.success();
    }

    //同步应用信息

}
