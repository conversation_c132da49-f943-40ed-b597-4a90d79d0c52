package cn.ac.iie.pmserver.controller.department.vo;

import cn.ac.iie.pmserver.model.Department;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

@Data
public class DepartmentTreeVo {
    private Long id;
    private String label;
    private List<DepartmentTreeVo> children = new ArrayList<>(); // 初始化children列表

    public DepartmentTreeVo(Department dept) {
        this.id = dept.getDepartmentId();
        this.label = dept.getDepartmentName();
    }
}