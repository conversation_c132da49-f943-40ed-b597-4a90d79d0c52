package cn.ac.iie.pmserver.controller.user;

import cn.ac.iie.pmserver.dto.role.RoleAppDTO;
import cn.ac.iie.pmserver.dto.user.*;
import cn.ac.iie.pmserver.request.user.RoleAssignmentRequest;
import cn.ac.iie.pmserver.request.user.UserBasicInfoRequest;
import cn.ac.iie.pmserver.request.user.UserSyncRequest;
import cn.ac.iie.pmserver.service.UserService;
import cn.ac.iie.pmserver.response.ApiResponse;
import cn.ac.iie.pmserver.utils.LogUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.List;
import java.util.List;
import java.util.Map;
import java.util.Set;

@RestController
@RequestMapping("/api/users")
@Tag(name = "用户管理", description = "用户相关操作接口")
public class UserController {

    private final UserService userService;

    private static final Logger logger = LogUtils.get(UserService.class);

    public UserController(UserService userService) {
        this.userService = userService;
    }

    @GetMapping("/findAll")
    @Operation(summary = "搜索用户", description = "获取用户基本信息、所属信息（所属部门、拥有角色、应用、模块的个数）、多条件查询、分页展示")
    public Object searchUsers(
            @Parameter(description = "搜索关键字（用户名/邮箱/手机号）")
            @RequestParam(required = false) String keyword,
            @Parameter(description = "用户状态（0：正常，1：禁用）")
            @RequestParam(required = false) Integer delFlag,
            @Parameter(description = "开始时间")
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime startTime,
            @Parameter(description = "结束时间")
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME) LocalDateTime endTime,
            @Parameter(description = "角色ID")
            @RequestParam(required = false) Long roleId,
            @Parameter(description = "分页参数")
            @PageableDefault(sort = "creatTime", direction = Sort.Direction.DESC) Pageable pageable) {
        try {
            Page<UserDto> result = userService.searchUsers(keyword, delFlag, startTime, endTime, roleId, pageable);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error(400, "搜索用户失败: " + e.getMessage());
        }
    }

    @GetMapping("/{userId}/details")
    @Operation(summary = "获取用户详情", description = "获取用户所属模块、关联应用和拥有角色的信息")
    public Object getUserDetail(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        try {
            UserInfoDto userModules = userService.getUserDetail(userId);
            return ApiResponse.success(userModules);
        } catch (Exception e) {
            return ApiResponse.error(400, "获取用户详情失败: " + e.getMessage());
        }
    }

    @GetMapping("/{userId}/editDetails")
    @Operation(summary = "用户编辑-信息展示", description = "获取用户的基本信息、所属应用列表，以及每个应用下已分配和未分配的角色")
    public Object getUserAppRoles(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId) {
        try {
            UserInfoEditDto userAppRoles = userService.getUserAppRoles(userId);
            return ApiResponse.success(userAppRoles);
        } catch (Exception e) {
            return ApiResponse.error(400, "获取用户应用角色信息失败: " + e.getMessage());
        }
    }

    @PutMapping("/{userId}/basicInfo")
    @Operation(summary = "用户编辑-基本信息", description = "更新用户的电话和邮箱信息")
    public Object updateUserBasicInfo(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId,
            @Parameter(description = "用户基本信息", required = true)
            @RequestBody UserBasicInfoRequest request) {
        try {
            userService.updateUserBasicInfo(userId, request.getPhone(), request.getEmail());
            return ApiResponse.success();
        } catch (Exception e) {
            return ApiResponse.error(400, "更新用户基本信息失败: " + e.getMessage());
        }
    }

    @PutMapping("/{userId}/status")
    @Operation(summary = "更新用户状态", description = "更新用户的启用/禁用状态")
    public Object updateUserStatus(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId,
            @Parameter(description = "状态值(0:启用,1:禁用)", required = true)
            @RequestParam Integer status) {
        try {
            userService.updateUserStatus(userId, status);
            return ApiResponse.success();
        } catch (Exception e) {
            return ApiResponse.error(400, "更新用户状态失败: " + e.getMessage());
        }
    }

    @PutMapping("/batchStatus")
    @Operation(summary = "批量更新用户状态", description = "批量更新用户的启用/禁用状态")
    public Object batchUpdateUserStatus(
            @Parameter(description = "用户ID列表", required = true)
            @RequestBody List<Long> userIds,
            @Parameter(description = "状态值(0:启用,1:禁用)", required = true)
            @RequestParam Integer status) {
        try {
            userService.batchUpdateUserStatus(userIds, status);
            return ApiResponse.success();
        } catch (Exception e) {
            return ApiResponse.error(400, "批量更新用户状态失败: " + e.getMessage());
        }
    }

    @PostMapping("/{userId}/apps/{appId}/roles")
    @Operation(summary = "用户编辑-分配角色", description = "为用户在指定应用下批量分配角色")
    public Object assignRolesToUser(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId,
            @Parameter(description = "应用ID", required = true)
            @PathVariable Long appId,
            @Parameter(description = "角色分配信息", required = true)
            @RequestBody RoleAssignmentRequest request) {
        try {
            userService.assignRolesToUser(userId, appId, request.getRoleIds());
            return ApiResponse.success();
        } catch (Exception e) {
            return ApiResponse.error(400, "分配角色失败: " + e.getMessage());
        }
    }

    @DeleteMapping("/{userId}/apps/{appId}/roles")
    @Operation(summary = "用户编辑-移除角色", description = "为用户指定应用下批量移除角色")
    public Object removeRolesFromUser(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long userId,
            @Parameter(description = "应用ID", required = true)
            @PathVariable Long appId,
            @Parameter(description = "角色分配信息", required = true)
            @RequestBody RoleAssignmentRequest request) {
        try {
            userService.removeRolesFromUser(userId, appId, request.getRoleIds());
            return ApiResponse.success();
        } catch (Exception e) {
            return ApiResponse.error(400, "移除角色失败: " + e.getMessage());
        }
    }

    @GetMapping("/apps/{appId}/users")
    @Operation(summary = "查询应用下所有用户", description = "获取指定应用下的所有用户信息")
    public ApiResponse getUsersByApp(
            @Parameter(description = "应用ID", required = true)
            @PathVariable Long appId) {
        try {
            List<UserAppDTO> users = userService.getUsersByAppId(appId);
            return ApiResponse.success(users);
        } catch (Exception e) {
            return ApiResponse.error(400, "查询应用用户失败: " + e.getMessage());
        }
    }

    @GetMapping("/import/template")
    @Operation(summary = "下载用户导入模板")
    public void downloadUserTemplate(HttpServletResponse response) throws IOException {
        // 1. 设置响应头 - 使用RFC 5987编码处理中文文件名
        String encodedFilename = "filename*=UTF-8''" + URLEncoder.encode("用户导入模板.xlsx", "UTF-8")
                .replaceAll("\\+", "%20");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; " + encodedFilename);

        // 2. 创建Excel模板
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("用户数据");

        // 创建绿色单元格样式
        XSSFCellStyle greenHeaderStyle = ((XSSFWorkbook) workbook).createCellStyle();
        greenHeaderStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(198, 239, 206), null)); // 浅绿色
        greenHeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 3. 创建表头行
        Row headerRow = sheet.createRow(0);
        String[] headers = {"姓名", "key号", "密码", "用户编码", "邮箱", "电话号",
                            "部门名称", "部门编码", "性别(0:女,1:男)", "用户状态(0:启用,1:禁用)",
                            "应用编码", "角色编码(多个用逗号分隔)"};

        // 设置列宽（单位：1/256个字符宽度）
        int[] columnWidths = {4000, 8000, 4000, 4000, 5000, 4000,
                4000, 5000, 3000, 3000, 4000, 8000};

        for (int i = 0; i < headers.length; i++) {
            headerRow.createCell(i).setCellValue(headers[i]);
            sheet.setColumnWidth(i, columnWidths[i]); // 设置每列宽度
        }

        // 需要设置绿色背景的列索引（姓名、用户编码、角色编码）
        Set<Integer> greenColumns = Set.of(0, 1, 2, 3, 6, 7, 8, 10, 11);

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            if (greenColumns.contains(i)) {
                cell.setCellStyle(greenHeaderStyle); // 设置绿色背景
            }
            sheet.setColumnWidth(i, columnWidths[i]);
        }

        // 4. 添加示例数据行
        Row exampleRow = sheet.createRow(1);
        exampleRow.createCell(0).setCellValue("张三");
        exampleRow.createCell(1).setCellValue("550e8400-e29b-41d4-a716-446655440000");
        exampleRow.createCell(2).setCellValue("Abcd1234!");
        exampleRow.createCell(3).setCellValue("QXGL_USER1");
        exampleRow.createCell(4).setCellValue("<EMAIL>");
        exampleRow.createCell(5).setCellValue("15543772161");
        exampleRow.createCell(6).setCellValue("信息部");
        exampleRow.createCell(7).setCellValue("QXGL_DEPARTMENT1");
        exampleRow.createCell(8).setCellValue(1);
        exampleRow.createCell(9).setCellValue(0);
        exampleRow.createCell(10).setCellValue("QXGL");
        exampleRow.createCell(11).setCellValue("QXGL_ROLE1，QXGL_ROLE2");

        // 5. 写入响应流
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    @PostMapping("/import")
    @Operation(summary = "导入用户数据", description = "通过Excel文件导入用户数据")
    public Object importUsersFromFile(
            @Parameter(description = "Excel文件", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "操作人", required = true)
            @RequestParam(value = "operator", required = false) String operator) {
        try {
            // 检查文件类型
            if (!file.getOriginalFilename().toLowerCase().endsWith(".xlsx")) {
                return ApiResponse.error(400, "只支持.xlsx格式的Excel文件");
            }

            Map<String, Object> result = userService.importUsersFromFile(file, operator);
            return ApiResponse.success(result);
        } catch (IOException e) {
            logger.error("用户导入失败", e);
            return ApiResponse.error(500, "文件处理失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("用户导入失败", e);
            return ApiResponse.error(500, "用户导入失败: " + e.getMessage());
        }
    }

    @PostMapping("/sync")
    @Operation(summary = "同步用户信息", description = "同步用户、部门、角色的基本信息")
    public Object syncUserInfo(
            @Parameter(description = "同步请求数据", required = true)
            @RequestBody UserSyncRequest request) {
        try {
            userService.syncUserInfo(request.getResourceCode(), request.getUserList());
            return ApiResponse.success();
        } catch (Exception e) {
            logger.error("同步用户信息失败", e);
            return ApiResponse.error(400, "同步用户到统一权限管理系统失败");
        }
    }
}