package cn.ac.iie.pmserver.controller.role;

import cn.ac.iie.pmserver.dto.role.RoleAppDTO;
import cn.ac.iie.pmserver.dto.role.RoleDto;
import cn.ac.iie.pmserver.dto.role.RoleEditDto;
import cn.ac.iie.pmserver.dto.role.*;
import cn.ac.iie.pmserver.model.Role;
import cn.ac.iie.pmserver.request.role.RoleSyncRequest;
import cn.ac.iie.pmserver.service.RoleService;
import cn.ac.iie.pmserver.response.ApiResponse;
import cn.ac.iie.pmserver.service.UserService;
import cn.ac.iie.pmserver.utils.LogUtils;
import io.micrometer.common.util.StringUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.web.PageableDefault;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.Set;

import java.util.List;

@RestController
@RequestMapping("/api/roles")
@Tag(name = "角色管理", description = "角色相关操作接口")
public class RoleController {

    private final RoleService roleService;

    public RoleController(RoleService roleService) {
        this.roleService = roleService;
    }
    private static final Logger logger = LogUtils.get(UserService.class);

    @Operation(summary = "获取所有角色ID和名称", description = "获取系统中所有角色的ID和名称")
    @GetMapping("/allRoles")
    public Object getAllRoles() {
        try {
            List<RoleSimpleDto> roles = roleService.getAllRoles();
            return ApiResponse.success(roles);
        } catch (Exception e) {
            return ApiResponse.error(400, "获取角色列表失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取所有应用及其角色统计", description = "获取系统中所有应用的ID和名称，并统计每个应用的角色数量")
    @GetMapping("/appRoleStats")
    public Object getAppRoleStatistics() {
        try {
            // 调用RoleService中的新方法获取应用统计信息
            Map<Long, Map<String, Object>> appStats = roleService.getAppRoleStatistics();
            return ApiResponse.success(appStats);
        } catch (Exception e) {
            return ApiResponse.error(400, "获取应用角色统计失败: " + e.getMessage());
        }
    }

    @Operation(summary = "搜索角色", description = "获取角色基本信息、所属信息（拥有策略、所属应用），根据关键字、多条件查询、分页展示")
    @GetMapping("/findAll")
    public Object searchRoles(
            @Parameter(description = "搜索关键字（角色名称/角色说明）")
            @RequestParam(required = false) String keyword,
            @Parameter(description = "角色状态（0：正常，1：禁用）")
            @RequestParam(required = false) Integer delFlag,
            @Parameter(description = "应用ID")
            @RequestParam(required = false) Long appId,
            @Parameter(description = "分页参数")
            @PageableDefault(page = 0, size = 10, sort = "creatTime", direction = Sort.Direction.DESC) Pageable pageable) {
        try {
            return ApiResponse.success(roleService.searchRoles(keyword, delFlag, appId, pageable));
        } catch (Exception e) {
            return ApiResponse.error(400, "搜索角色失败: " + e.getMessage());
        }
    }

    @Operation(summary = "创建角色", description = "创建新的角色，包含角色基本信息和所属应用")
    @PostMapping("/save")
    public Object addRole(
            @Parameter(description = "角色信息", required = true)
            @Valid @RequestBody RoleEditDto roleEditDto) {
        try {
            Role role = roleService.addRole(roleEditDto);
            return ApiResponse.success(new RoleDto(role));
        } catch (Exception e) {
            return ApiResponse.error(400, "创建角色失败: " + e.getMessage());
        }
    }

    @Operation(summary = "更新角色", description = "更新指定角色的信息，包括基本信息和所属应用")
    @PutMapping("/{id}/edit")
    public Object updateRole(
            @Parameter(description = "角色ID", required = true)
            @PathVariable Long id,
            @Parameter(description = "角色信息", required = true)
            @Valid @RequestBody RoleEditDto roleEditDto) {
        try {
            int result = roleService.updateRole(id, roleEditDto);
            if (result > 0) {
                Role role = roleService.getRoleById(id);
                return ApiResponse.success(new RoleDto(role));
            }
            return ApiResponse.error(400, "更新角色失败");
        } catch (Exception e) {
            return ApiResponse.error(400, "更新角色失败: " + e.getMessage());
        }
    }
    @GetMapping("/apps/{appId}/roles")
    @Operation(summary = "查询应用下所有角色", description = "获取指定应用下的所有角色信息")
    public ApiResponse getRolesByApp(
            @Parameter(description = "应用ID", required = true)
            @PathVariable Long appId) {
        try {
            List<RoleAppDTO> roles = roleService.getRolesByAppId(appId);
            return ApiResponse.success(roles);
        } catch (Exception e) {
            return ApiResponse.error(400, "查询应用角色失败: " + e.getMessage());
        }
    }

    @PutMapping("/batchStatus")
    @Operation(summary = "批量更新角色状态", description = "批量更新角色的启用/禁用状态")
    public Object batchUpdateRoleStatus(
            @Parameter(description = "角色ID列表", required = true)
            @RequestBody List<Long> roleIds,
            @Parameter(description = "状态值(0:启用,1:禁用)", required = true)
            @RequestParam Integer status) {
        try {
            roleService.batchUpdateRoleStatus(roleIds, status);
            return ApiResponse.success();
        } catch (Exception e) {
            return ApiResponse.error(400, "批量更新角色状态失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取角色在应用下的详细信息", description = "根据角色ID和应用ID获取角色在指定应用下的完整信息")
    @GetMapping("/{roleId}/apps/{appId}/details")
    public Object getRoleAppDetails(
            @Parameter(description = "角色ID", required = true) @PathVariable Long roleId,
            @Parameter(description = "应用ID", required = true) @PathVariable Long appId) {
        try {
            return ApiResponse.success(roleService.getRoleAppDetails(roleId, appId));
        } catch (Exception e) {
            return ApiResponse.error(400, "获取详情失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取角色在应用下的权限信息",
            description = "根据角色ID和应用ID获取应用信息和权限列表")
    @GetMapping("/{roleId}/apps/{appId}/authorities")
    public Object getRoleAppAuthorities(
            @Parameter(description = "角色ID", required = true) @PathVariable Long roleId,
            @Parameter(description = "应用ID", required = true) @PathVariable Long appId) {
        try {
            Map<String, Object> result = roleService.getRoleAppAuthorities(roleId, appId);
            return ApiResponse.success(result);
        } catch (Exception e) {
            return ApiResponse.error(400, "获取数据失败: " + e.getMessage());
        }
    }

    @Operation(summary = "获取角色关联用户列表", description = "根据角色ID和应用ID获取关联用户列表")
    @GetMapping("/{roleId}/apps/{appId}/users")
    public Object getRoleUsersInApp(
            @Parameter(description = "角色ID", required = true) @PathVariable Long roleId,
            @Parameter(description = "应用ID", required = true) @PathVariable Long appId) {
        try {
            List<UserSimpleDto> users = roleService.getRoleUsersInApp(roleId, appId);
            return ApiResponse.success(users);
        } catch (Exception e) {
            return ApiResponse.error(400, "获取用户列表失败: " + e.getMessage());
        }
    }


    @GetMapping("/import/template")
    @Operation(summary = "下载角色导入模板")
    public void downloadRoleTemplate(HttpServletResponse response) throws IOException {
        try {
            // 1. 设置响应头 - 使用RFC 5987编码处理中文文件名
            String encodedFilename = "filename*=UTF-8''" + URLEncoder.encode("角色导入模板.xlsx", "UTF-8")
                    .replaceAll("\\+", "%20");
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; " + encodedFilename);

            // 2. 创建Excel模板
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("角色数据");

            // 创建绿色单元格样式（用于必填字段）
            XSSFCellStyle greenHeaderStyle = ((XSSFWorkbook) workbook).createCellStyle();
            greenHeaderStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(198, 239, 206), null)); // 浅绿色
            greenHeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

            // 3. 创建表头行
            Row headerRow = sheet.createRow(0);

            // 表头定义
            String[] headers = {
                    "角色名称",
                    "角色编码",
                    "角色说明",
                    "禁用状态(0:启用,1:禁用)",
                    "父级角色编码",
                    "角色等级(10-顶级, 2-次级, 以此类推)",
                    "应用编码",
                    "子角色编码(多个用逗号分隔)"
            };

            // 必填字段的列索引
            Set<Integer> requiredColumns = Set.of(0, 1, 5, 6);

            // 设置列宽（单位：1/256个字符宽度）
            int[] columnWidths = {5000, 5000, 5000, 5000, 8000, 3000, 3000, 8000};

            // 创建表头并设置样式
            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                if (requiredColumns.contains(i)) {
                    cell.setCellStyle(greenHeaderStyle); // 设置绿色背景
                }
                sheet.setColumnWidth(i, columnWidths[i]);
            }

            // 4. 添加示例数据行
            Row exampleRow = sheet.createRow(1);
            exampleRow.createCell(0).setCellValue("管理员");
            exampleRow.createCell(1).setCellValue("QXGL_ROLE2");
            exampleRow.createCell(2).setCellValue("系统管理员，拥有所有权限");
            exampleRow.createCell(3).setCellValue(0);
            exampleRow.createCell(4).setCellValue("QXGL_ROLE1");
            exampleRow.createCell(5).setCellValue(2);
            exampleRow.createCell(6).setCellValue("QXGL");
            exampleRow.createCell(7).setCellValue("QXGL_ROLE3,QXGL_ROLE4");

            // 5. 写入响应流
            workbook.write(response.getOutputStream());
            workbook.close();
            response.flushBuffer();
        } catch (Exception e) {
            logger.error("下载角色模板失败", e);
            response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "下载角色模板失败");
        }
    }

    @PostMapping("/import")
    @Operation(summary = "导入角色数据", description = "通过Excel文件导入角色数据")
    public Object importRoles(
            @Parameter(description = "Excel文件", required = true)
            @RequestParam("file") MultipartFile file,
            @Parameter(description = "操作人", required = false)
            @RequestParam(value = "operator", required = false) String operator) {
        try {
            // 检查文件类型
            if (!file.getOriginalFilename().toLowerCase().endsWith(".xlsx")) {
                return ApiResponse.error(400, "只支持.xlsx格式的Excel文件");
            }

            // 检查文件内容是否为空
            if (file.isEmpty()) {
                return ApiResponse.error(400, "上传的文件为空");
            }

            Map<String, Object> result = roleService.importRolesFromFile(file, operator);
            return ApiResponse.success(result);
        } catch (IOException e) {
            logger.error("角色导入失败 - IO异常", e);
            return ApiResponse.error(500, "文件处理失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("角色导入失败", e);
            return ApiResponse.error(500, "角色导入失败: " + e.getMessage());
        }
    }

    @PostMapping("/sync")
    @Operation(summary = "同步角色数据", description = "批量同步角色数据")
    public Object syncRoles(
            @Parameter(description = "同步请求数据", required = true)
            @RequestBody RoleSyncRequest request) {
        try {
            String finalOperator =  "admin" ;
            Map<String, Object> result = roleService.syncRoles(request.getResourceCode(), request.getRoleList(), finalOperator);
            return ApiResponse.success(result);
        } catch (Exception e) {
            logger.error("同步角色数据失败", e);
            return ApiResponse.error(500, "同步角色数据失败: " + e.getMessage());
        }
    }
}