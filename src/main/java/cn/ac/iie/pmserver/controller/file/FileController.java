package cn.ac.iie.pmserver.controller.file;

import cn.ac.iie.pmserver.response.ApiResponse;
import cn.ac.iie.pmserver.service.FileService;
import cn.ac.iie.pmserver.utils.LogUtils;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.core.io.Resource;

import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/api/files")
@Tag(name = "文件上传", description = "文件上传api")
@RequiredArgsConstructor
public class FileController {

    @Value("${app.upload.allowed-types}")
    private List<String> allowedTypes;

    @Value("${app.upload.max-size}")
    private long maxFileSize;

    private final FileService fileService;


    private static final Logger logger = LogUtils.get(FileController.class);

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    @Operation(
            summary = "上传文件",
            description = "上传文件",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "上传文件成功"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "文件大小、类型不符"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "文件上传失败")
            },
            parameters = {
                    @Parameter(name = "file", description = "上传的文件", required = true)
            }
    )
    public ApiResponse uploadFile(@RequestParam("file") MultipartFile file) {
        logger.info("上传文件:{}", file.getContentType());
        // 参数验证
        if (file == null || file.isEmpty()) {
            return ApiResponse.error(400, "文件不能为空", null);
        }

        // 文件类型检查
        String contentType = file.getContentType();
        if (!isValidImageType(contentType, allowedTypes)) {
            return ApiResponse.error(400, "不支持的文件类型", null);
        }

        // 文件大小检查
        if (file.getSize() > maxFileSize) {
            return ApiResponse.error(400, "文件大小超过限制", null);
        }
        ApiResponse apiResponse = fileService.uploadFile(file);
        return apiResponse;
    }

    /**
     * 删除文件
     */
    @PostMapping("/delete")
    @Operation(
            summary = "删除文件",
            description = "删除文件",
            responses = {
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "200", description = "删除文件成功"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "400", description = "文件名无效"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "404", description = "找不到文件"),
                    @io.swagger.v3.oas.annotations.responses.ApiResponse(responseCode = "500", description = "删除文件失败")
            },
            parameters = {
                    @Parameter(name = "file", description = "上传的文件", required = true)
            }
    )
    public ApiResponse deleteFile(@RequestParam("fileName") String fileName) {
        logger.info("删除文件:{}", fileName);
        if (!isValidFileName(fileName)) {
            return ApiResponse.error(400, "无效的文件名", null);
        }
        ApiResponse apiResponse = fileService.deleteFile(fileName);
        return apiResponse;
    }
    /**
     * 验证图片类型是否合法
     *
     * @param contentType  内容类型
     * @param allowedTypes 允许的类型列表
     * @return 是否合法
     */
    private boolean isValidImageType(String contentType, List<String> allowedTypes) {
        return contentType != null && allowedTypes.contains(contentType);
    }

    /**
     * 验证文件名安全性
     */
    private boolean isValidFileName(String fileName) {
        return fileName != null && !fileName.contains("..") && !fileName.contains("/") && !fileName.contains("\\");
    }


//    /**
//     * 获取文件
//     */
//    @GetMapping("/get")
//    public ResponseEntity<Resource> getFile(@RequestParam("fileName") String fileName) {
//        logger.info("获取文件:{}", fileName);
//        if (!isValidFileName(fileName)) {
//            return ResponseEntity.badRequest().body(null);
//        }
//        ApiResponse response = fileService.getFile(fileName);
//        // 从响应中获取资源和内容类型
//        Map<String, Object> data = (Map<String, Object>) response;
//        Resource resource = (Resource) data.get("resource");
//        String contentType = (String) data.get("contentType");
//
//        // 构建响应头
//        return ResponseEntity.ok()
//                .contentType(MediaType.parseMediaType(contentType))
//                .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + fileName + "\"")
//                .body(resource);
//    }

//    /**
//     * 获取文件列表
//     */
//    @GetMapping("/list")
//    public ApiResponse getFileList() {
//        ApiResponse response = fileService.getFileList();
//        return ApiResponse.success(response.getData());
//    }


}
