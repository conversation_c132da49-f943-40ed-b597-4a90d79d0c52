package cn.ac.iie.pmserver.controller.policies;

import cn.ac.iie.pmserver.dto.policies.PoliciesDto;
import cn.ac.iie.pmserver.dto.policies.PolicyEvaluationDto;
import cn.ac.iie.pmserver.dto.policies.PolicyEvaluationResultDto;
import cn.ac.iie.pmserver.dto.policies.PolicyQueryDto;
import cn.ac.iie.pmserver.response.ApiResponse;
import cn.ac.iie.pmserver.service.PoliciesService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 策略管理控制器
 * 提供策略相关的所有API接口
 */
@RestController
@RequestMapping("/api/policies")
@Tag(name = "策略管理", description = "策略定义、执行、评估等功能接口")
@RequiredArgsConstructor
public class PoliciesController {

    private final PoliciesService policiesService;

    // ==================== 策略基础管理 ====================

    /**
     * 获取策略列表
     * @param page 页码
     * @param size 每页大小
     * @param keyword 搜索关键字
     * @param policyType 策略类型
     * @param effect 策略效果
     * @param status 策略状态
     * @return 策略分页列表
     */
    @Operation(summary = "获取策略列表", description = "分页查询策略信息，支持关键字搜索和条件过滤")
    @GetMapping
    public Object getPolicies(
            @Parameter(description = "页码，从0开始") @RequestParam(defaultValue = "0") int page,
            @Parameter(description = "每页大小") @RequestParam(defaultValue = "10") int size,
            @Parameter(description = "搜索关键字") @RequestParam(required = false) String keyword,
            @Parameter(description = "策略类型") @RequestParam(required = false) String policyType,
            @Parameter(description = "策略效果") @RequestParam(required = false) String effect,
            @Parameter(description = "策略状态") @RequestParam(required = false) Integer status,
            @Parameter(description = "创建人") @RequestParam(required = false) String creatUser,
            @Parameter(description = "是否包含已过期策略") @RequestParam(defaultValue = "false") Boolean includeExpired,
            @Parameter(description = "排序字段") @RequestParam(defaultValue = "creatTime") String sortBy,
            @Parameter(description = "排序方向") @RequestParam(defaultValue = "desc") String sortDirection
    ) {
        // 构建查询条件
        PolicyQueryDto queryDto = new PolicyQueryDto();
        queryDto.setKeyword(keyword);
        queryDto.setPolicyType(policyType);
        queryDto.setEffect(effect);
        queryDto.setStatus(status);
        queryDto.setCreatUser(creatUser);
        queryDto.setIncludeExpired(includeExpired);
        queryDto.setSortBy(sortBy);
        queryDto.setSortDirection(sortDirection);

        // 构建分页参数
        Sort sort = Sort.by("desc".equalsIgnoreCase(sortDirection) ?
            Sort.Direction.DESC : Sort.Direction.ASC, sortBy);
        Pageable pageable = PageRequest.of(page, size, sort);

        Page<PoliciesDto> result = policiesService.searchPolicies(queryDto, pageable);
        return ApiResponse.success(result);
    }

    /**
     * 获取策略详情
     * @param policyId 策略ID
     * @return 策略详情信息
     */
    @Operation(summary = "获取策略详情", description = "根据策略ID查询策略的详细信息")
    @GetMapping("/{policyId}")
    public Object getPolicyDetail(
            @Parameter(description = "策略ID", required = true) @PathVariable Long policyId
    ) {
        PoliciesDto result = policiesService.getPolicyById(policyId);
        return ApiResponse.success(result);
    }

    /**
     * 创建策略
     * @param policyDto 策略信息
     * @return 创建结果
     */
    @Operation(summary = "创建策略", description = "创建新的访问控制策略")
    @PostMapping
    public Object createPolicy(
            @Parameter(description = "策略信息", required = true) @Valid @RequestBody PoliciesDto policyDto
    ) {
        PoliciesDto result = policiesService.createPolicy(policyDto);
        return ApiResponse.success(result);
    }

    /**
     * 更新策略信息
     * @param policyId 策略ID
     * @param policyDto 策略信息
     * @return 更新结果
     */
    @Operation(summary = "更新策略信息", description = "更新指定策略的信息")
    @PutMapping("/{policyId}")
    public Object updatePolicy(
            @Parameter(description = "策略ID", required = true) @PathVariable Long policyId,
            @Parameter(description = "策略信息", required = true) @Valid @RequestBody PoliciesDto policyDto
    ) {
        PoliciesDto result = policiesService.updatePolicy(policyId, policyDto);
        return ApiResponse.success(result);
    }

    /**
     * 更新策略状态
     * @param policyId 策略ID
     * @param request 状态更新请求
     * @return 更新结果
     */
    @Operation(summary = "更新策略状态", description = "更新策略的启用/禁用状态")
    @PatchMapping("/{policyId}/status")
    public Object updatePolicyStatus(
            @Parameter(description = "策略ID", required = true) @PathVariable Long policyId,
            @Parameter(description = "状态信息", required = true) @RequestBody Map<String, Integer> request
    ) {
        Integer status = request.get("status");
        boolean result = policiesService.updatePolicyStatus(policyId, status);
        return ApiResponse.success(Map.of("success", result));
    }

    /**
     * 删除策略
     * @param request 策略ID列表
     * @return 删除结果
     */
    @Operation(summary = "删除策略", description = "批量删除指定的策略")
    @DeleteMapping
    public Object deletePolicies(
            @Parameter(description = "策略ID列表", required = true) @RequestBody Map<String, List<Long>> request
    ) {
        List<Long> policyIds = request.get("policyIds");
        int deletedCount = policiesService.deletePolicies(policyIds);
        return ApiResponse.success(Map.of("deletedCount", deletedCount));
    }

    // ==================== 策略执行与评估 ====================

    /**
     * 策略评估
     * @param evaluationDto 评估参数
     * @return 评估结果
     */
    @Operation(summary = "策略评估", description = "评估用户对指定资源的访问权限")
    @PostMapping("/evaluate")
    public Object evaluatePolicy(
            @Parameter(description = "评估参数", required = true) @Valid @RequestBody PolicyEvaluationDto evaluationDto
    ) {
        PolicyEvaluationResultDto result = policiesService.evaluatePolicy(evaluationDto);
        return ApiResponse.success(result);
    }

    /**
     * 批量策略评估
     * @param request 批量评估请求
     * @return 评估结果列表
     */
    @Operation(summary = "批量策略评估", description = "批量评估多个访问权限请求")
    @PostMapping("/evaluate/batch")
    public Object evaluatePoliciesBatch(
            @Parameter(description = "批量评估请求", required = true) @RequestBody Map<String, List<PolicyEvaluationDto>> request
    ) {
        List<PolicyEvaluationDto> requests = request.get("requests");
        List<PolicyEvaluationResultDto> results = policiesService.evaluatePoliciesBatch(requests);
        return ApiResponse.success(results);
    }

    // ==================== 策略规则管理 ====================

    /**
     * 获取策略规则模板
     * @return 模板列表
     */
    @Operation(summary = "获取策略规则模板", description = "获取预定义的策略规则模板")
    @GetMapping("/templates")
    public Object getPolicyTemplates() {
        // 返回预定义的策略模板
        List<Map<String, Object>> templates = List.of(
            Map.of(
                "templateId", 1,
                "templateName", "基于角色的访问控制",
                "templateType", "RBAC",
                "template", Map.of(
                    "conditions", Map.of(
                        "roles", List.of("${role_name}"),
                        "timeRange", Map.of(
                            "start", "${start_time}",
                            "end", "${end_time}"
                        )
                    ),
                    "resources", List.of("${resource_pattern}"),
                    "actions", List.of("${actions}")
                )
            ),
            Map.of(
                "templateId", 2,
                "templateName", "基于属性的访问控制",
                "templateType", "ABAC",
                "template", Map.of(
                    "conditions", Map.of(
                        "userAttributes", Map.of(
                            "department", "${department}",
                            "level", "${level}"
                        ),
                        "resourceAttributes", Map.of(
                            "classification", "${classification}",
                            "owner", "${owner}"
                        ),
                        "environment", Map.of(
                            "time", "${time_condition}",
                            "location", "${location_condition}"
                        )
                    ),
                    "resources", List.of("${resource_pattern}"),
                    "actions", List.of("${actions}")
                )
            ),
            Map.of(
                "templateId", 3,
                "templateName", "访问控制列表",
                "templateType", "ACL",
                "template", Map.of(
                    "conditions", Map.of(
                        "users", List.of("${user_list}"),
                        "ipWhitelist", List.of("${ip_whitelist}")
                    ),
                    "resources", List.of("${resource_pattern}"),
                    "actions", List.of("${actions}")
                )
            )
        );

        return ApiResponse.success(templates);
    }



    // ==================== 策略统计与监控 ====================

    /**
     * 获取策略统计信息
     * @return 统计信息
     */
    @Operation(summary = "获取策略统计信息", description = "查询策略的各种统计数据")
    @GetMapping("/statistics")
    public Object getPolicyStatistics() {
        Map<String, Object> result = policiesService.getPolicyStatistics();
        return ApiResponse.success(result);
    }


}