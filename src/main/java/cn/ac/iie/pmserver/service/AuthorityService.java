package cn.ac.iie.pmserver.service;

import cn.ac.iie.pmserver.dto.authority.AuthorityAssignmentDto;
import cn.ac.iie.pmserver.dto.authority.AuthorityDisplayDto;
import cn.ac.iie.pmserver.dto.authority.ResourceAuthorityAssignmentDto;
import cn.ac.iie.pmserver.dto.role.RoleAppDTO;
import cn.ac.iie.pmserver.dto.user.UserAppDTO;
import cn.ac.iie.pmserver.exception.ResourceNotFoundException;
import cn.ac.iie.pmserver.model.App;
import cn.ac.iie.pmserver.model.Authority;
import cn.ac.iie.pmserver.model.Module;
import cn.ac.iie.pmserver.model.Role;
import cn.ac.iie.pmserver.model.User;
import cn.ac.iie.pmserver.repository.AppRepository;
import cn.ac.iie.pmserver.repository.AuthorityRepository;
import cn.ac.iie.pmserver.repository.ModuleRepository;
import cn.ac.iie.pmserver.repository.RoleRepository;
import cn.ac.iie.pmserver.repository.UserRepository;
import cn.ac.iie.pmserver.request.authority.AuthoritySyncRequest;
import cn.ac.iie.pmserver.request.authority.AuthorityUploadRequest;
import cn.ac.iie.pmserver.specification.AuthoritySpecification;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.dao.DataAccessException;
import org.springframework.web.multipart.MultipartFile;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import jakarta.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class AuthorityService {

    private final AuthorityRepository authorityRepository;
    private final ModuleRepository moduleRepository;
    private final AppRepository appRepository;
    private final RoleRepository roleRepository;
    private final UserRepository userRepository;

    /**
     * 文件导入权限数据
     */
    @Transactional
    public Map<String, Object> importAuthoritiesFromFile(MultipartFile file, Boolean overrideExisting, String operator) {
        log.info("开始导入权限文件: {}", file.getOriginalFilename());

        Map<String, Object> result = new HashMap<>();
        int totalCount = 0, successCount = 0, failedCount = 0, skippedCount = 0;
        List<String> successList = new ArrayList<>();
        List<String> failedList = new ArrayList<>();
        List<String> skippedList = new ArrayList<>();

        try {
            // 解析文件
            List<Map<String, Object>> authorities = parseFile(file);
            totalCount = authorities.size();

            if (authorities.isEmpty()) {
                throw new IllegalArgumentException("文件中没有有效的权限数据");
            }

            // 批量处理权限
            for (Map<String, Object> authorityData : authorities) {
                try {
                    // 构建权限请求对象
                    AuthorityUploadRequest request = buildAuthorityRequest(authorityData, operator);

                    // 验证请求参数
                    if (!request.isValid()) {
                        failedList.add(request.getAuthorityName() + " (数据不完整)");
                        failedCount++;
                        continue;
                    }

                    // 检查权限是否已存在
                    boolean exists = request.getAuthorityCode() != null &&
                        authorityRepository.existsByAuthorityCode(request.getAuthorityCode());

                    if (exists && !overrideExisting) {
                        skippedList.add(request.getAuthorityName() + " (权限编码已存在)");
                        skippedCount++;
                        continue;
                    }

                    // 创建或更新权限
                    Authority authority;
                    if (exists && overrideExisting) {
                        authority = authorityRepository.findByAuthorityCode(request.getAuthorityCode()).get();
                        updateAuthorityFromRequest(authority, request);
                    } else {
                        authority = createAuthorityFromRequest(request);
                    }

                    // 关联模块
                    Module module = moduleRepository.findById(request.getModuleId())
                        .orElseThrow(() -> new IllegalArgumentException("模块不存在: " + request.getModuleId()));
                    authority.getModules().clear();
                    authority.getModules().add(module);

                    // 保存权限
                    Authority savedAuthority = authorityRepository.save(authority);
                    savedAuthority.updateSyncStatus("SUCCESS");
                    authorityRepository.save(savedAuthority);

                    successList.add(savedAuthority.getAuthorityName());
                    successCount++;

                } catch (Exception e) {
                    log.error("权限导入失败: {}", e.getMessage());
                    failedList.add(String.valueOf(authorityData.get("authorityName")) + " (" + e.getMessage() + ")");
                    failedCount++;
                }
            }

            // 构建结果
            result.put("totalCount", totalCount);
            result.put("successCount", successCount);
            result.put("failedCount", failedCount);
            result.put("skippedCount", skippedCount);
            result.put("successList", successList);
            result.put("failedList", failedList);
            result.put("skippedList", skippedList);
            result.put("fileName", file.getOriginalFilename());
            result.put("operator", operator);
            result.put("importTime", LocalDateTime.now().toString());

            log.info("权限导入完成: 总数={}, 成功={}, 失败={}, 跳过={}",
                    totalCount, successCount, failedCount, skippedCount);
            return result;

        } catch (Exception e) {
            log.error("权限文件导入失败: {}", e.getMessage(), e);
            throw new IllegalArgumentException("文件导入失败: " + e.getMessage());
        }
    }

    /**
     * 批量权限同步
     */
    @Transactional
    public Map<String, Object> syncAuthorities(AuthoritySyncRequest request) {
        log.info("开始批量同步权限, 应用ID: {}, 权限数量: {}", request.getAppId(), request.getAuthorityCount());

        // 设置默认值
        request.setDefaults();

        // 验证请求参数
        if (!request.isValid()) {
            throw new IllegalArgumentException("同步请求参数不完整");
        }

        Map<String, Object> result = new HashMap<>();
        List<String> successList = new ArrayList<>();
        List<String> failedList = new ArrayList<>();

        // 如果是全量同步，先清理旧数据
        if (request.isFullSync()) {
            List<Authority> oldAuthorities = authorityRepository.findByAppIdAndSyncSource(
                request.getAppId(), request.getSyncSource());
            authorityRepository.deleteAll(oldAuthorities);
            log.info("全量同步，清理旧权限数据: {} 条", oldAuthorities.size());
        }

        // 批量处理权限
        for (AuthorityUploadRequest authorityRequest : request.getAuthorities()) {
            try {
                // 检查权限是否已存在
                boolean exists = authorityRequest.getAuthorityCode() != null &&
                    authorityRepository.existsByAuthorityCode(authorityRequest.getAuthorityCode());

                if (exists && !request.shouldForceOverride()) {
                    log.warn("权限已存在，跳过: {}", authorityRequest.getAuthorityCode());
                    failedList.add(authorityRequest.getAuthorityName() + " (已存在)");
                    continue;
                }

                // 创建或更新权限
                Authority authority;
                if (exists) {
                    authority = authorityRepository.findByAuthorityCode(authorityRequest.getAuthorityCode()).get();
                    updateAuthorityFromRequest(authority, authorityRequest);
                } else {
                    authority = createAuthorityFromRequest(authorityRequest);
                }

                // 关联模块
                Module module = moduleRepository.findById(authorityRequest.getModuleId())
                    .orElseThrow(() -> new IllegalArgumentException("模块不存在: " + authorityRequest.getModuleId()));
                authority.getModules().add(module);

                // 保存权限
                Authority savedAuthority = authorityRepository.save(authority);
                savedAuthority.updateSyncStatus("SUCCESS");
                authorityRepository.save(savedAuthority);

                successList.add(savedAuthority.getAuthorityName());

            } catch (Exception e) {
                log.error("权限同步失败: {}, 错误: {}", authorityRequest.getAuthorityName(), e.getMessage());
                failedList.add(authorityRequest.getAuthorityName() + " (" + e.getMessage() + ")");
            }
        }

        result.put("total", request.getAuthorityCount());
        result.put("success", successList.size());
        result.put("failed", failedList.size());
        result.put("successList", successList);
        result.put("failedList", failedList);

        log.info("批量同步完成, 成功: {}, 失败: {}", successList.size(), failedList.size());
        return result;
    }

    /**
     * 分页查询权限列表
     */
    public Page<AuthorityDisplayDto> getAuthorities(Long appId, Long moduleId, String keyword,
                                                   String resourceType, String syncSource, int page, int size) {
        log.info("分页查询权限列表, appId: {}, moduleId: {}, keyword: {}, page: {}, size: {}",
                appId, moduleId, keyword, page, size);

        // 创建分页对象
        Pageable pageable = PageRequest.of(page, size,
            Sort.by(Sort.Direction.ASC, "level", "sortOrder", "authorityName"));

        // 构建查询条件
        Specification<Authority> spec = AuthoritySpecification.buildSimpleSpecification(appId, moduleId, keyword, resourceType, syncSource);

        // 执行查询
        Page<Authority> authorityPage = authorityRepository.findAll(spec, pageable);

        // 转换为DTO
        return authorityPage.map(AuthorityDisplayDto::new);
    }

    /**
     * 列表查询权限（不分页）
     */
    public List<AuthorityDisplayDto> getAuthorityList(Long appId, Long moduleId, String keyword,
                                                     String resourceType, String syncSource) {
        log.info("列表查询权限, appId: {}, moduleId: {}, keyword: {}", appId, moduleId, keyword);

        // 构建查询条件
        Specification<Authority> spec = AuthoritySpecification.buildSimpleSpecification(appId, moduleId, keyword, resourceType, syncSource);

        // 执行查询，按层级和排序字段排序
        List<Authority> authorities = authorityRepository.findAll(spec,
            Sort.by(Sort.Direction.ASC, "level", "sortOrder", "authorityName"));

        // 转换为DTO
        return authorities.stream()
            .map(AuthorityDisplayDto::new)
            .collect(Collectors.toList());
    }

    /**
     * 获取权限树结构
     */
    public List<AuthorityDisplayDto> getAuthorityTree(Long appId, Long moduleId) {
        log.info("获取权限树结构, appId: {}, moduleId: {}", appId, moduleId);

        List<Authority> authorities;
        if (appId != null) {
            // 获取应用相关的权限，包括关联到该应用模块的权限和可能的孤立权限
            authorities = authorityRepository.findByAppId(appId);

            // 如果按应用查询的权限较少，可能存在未关联模块的父权限，需要补充查询
            Set<Long> parentIds = authorities.stream()
                .map(Authority::getParentId)
                .filter(Objects::nonNull)
                .filter(parentId -> parentId != 0)
                .collect(Collectors.toSet());

            // 查询缺失的父权限
            if (!parentIds.isEmpty()) {
                List<Authority> parentAuthorities = authorityRepository.findAllById(parentIds);
                // 合并权限列表，去重
                Set<Long> existingIds = authorities.stream()
                    .map(Authority::getAuthorityId)
                    .collect(Collectors.toSet());

                parentAuthorities.stream()
                    .filter(auth -> !existingIds.contains(auth.getAuthorityId()))
                    .forEach(authorities::add);
            }
        } else if (moduleId != null) {
            authorities = authorityRepository.findByModuleId(moduleId);
        } else {
            authorities = authorityRepository.findAll();
        }

        // 转换为DTO
        List<AuthorityDisplayDto> authorityDtos = authorities.stream()
            .map(AuthorityDisplayDto::new)
            .collect(Collectors.toList());

        // 构建树结构
        return buildAuthorityTree(authorityDtos);
    }

    /**
     * 根据ID获取权限详情
     */
    public AuthorityDisplayDto getAuthorityById(Long authorityId) {
        log.info("获取权限详情, ID: {}", authorityId);
        
        Authority authority = authorityRepository.findById(authorityId)
            .orElseThrow(() -> new ResourceNotFoundException("权限不存在: " + authorityId));
        
        return new AuthorityDisplayDto(authority);
    }

    /**
     * 从请求对象创建权限实体
     */
    private Authority createAuthorityFromRequest(AuthorityUploadRequest request) {
        return new Authority(
            request.getAuthorityName(),
            request.getAuthorityCode(),
            request.getAuthorityDescription(),
            request.getActionsJson(),
            request.getResourceType(),
            request.getResourcePathsJson(),
            request.getParentId(),
            request.getLevel(),
            request.getSortOrder(),
            request.getSyncSource()
        );
    }

    /**
     * 从请求对象更新权限实体
     */
    private void updateAuthorityFromRequest(Authority authority, AuthorityUploadRequest request) {
        authority.setAuthorityName(request.getAuthorityName());
        authority.setAuthorityDescription(request.getAuthorityDescription());
        authority.setActions(request.getActionsJson());
        authority.setResourceType(request.getResourceType());
        authority.setResourcePaths(request.getResourcePathsJson());
        authority.setParentId(request.getParentId());
        authority.setLevel(request.getLevel());
        authority.setSortOrder(request.getSortOrder());
        authority.setUpdateTime(LocalDateTime.now());

    }

    /**
     * 构建权限树结构
     */
    private List<AuthorityDisplayDto> buildAuthorityTree(List<AuthorityDisplayDto> authorities) {
        // 创建ID到权限的映射
        Map<Long, AuthorityDisplayDto> authorityMap = authorities.stream()
            .collect(Collectors.toMap(AuthorityDisplayDto::getAuthorityId, dto -> dto));

        // 找出根权限和构建父子关系
        List<AuthorityDisplayDto> rootAuthorities = new ArrayList<>();

        for (AuthorityDisplayDto authority : authorities) {
            if (authority.isRoot()) {
                rootAuthorities.add(authority);
            } else {
                AuthorityDisplayDto parent = authorityMap.get(authority.getParentId());
                if (parent != null) {
                    parent.addChild(authority);
                }
            }
        }

        // 对根权限进行排序
        rootAuthorities.sort(Comparator.comparing(AuthorityDisplayDto::getSortOrder)
            .thenComparing(AuthorityDisplayDto::getAuthorityName));

        // 递归排序子权限
        sortChildrenRecursively(rootAuthorities);

        return rootAuthorities;
    }

    /**
     * 递归排序子权限
     */
    private void sortChildrenRecursively(List<AuthorityDisplayDto> authorities) {
        for (AuthorityDisplayDto authority : authorities) {
            if (authority.hasChildren()) {
                authority.getChildren().sort(Comparator.comparing(AuthorityDisplayDto::getSortOrder)
                    .thenComparing(AuthorityDisplayDto::getAuthorityName));
                sortChildrenRecursively(authority.getChildren());
            }
        }
    }

    /**
     * 查询用户在指定应用下的权限分配情况（按模块组织）
     * 
     * @param appId 应用ID
     * @param userId 用户ID
     * @return 权限分配状态DTO
     */
    public AuthorityAssignmentDto getUserAuthorityAssignment(Long appId, Long userId) {
        log.info("查询用户权限分配情况 - 应用ID: {}, 用户ID: {}", appId, userId);
        
        // 获取应用和用户信息
        App app = appRepository.findById(appId)
            .orElseThrow(() -> new ResourceNotFoundException("应用不存在，ID: " + appId));
        User user = userRepository.findById(userId)
            .orElseThrow(() -> new ResourceNotFoundException("用户不存在，ID: " + userId));
        
        // 获取用户通过角色拥有的所有权限ID
        Set<Long> userAuthorityIds = getUserAuthorityIds(user);
        
        // 获取应用下的模块权限分配情况
        List<AuthorityAssignmentDto.ModuleAuthorityDto> modules = getModuleAuthorityAssignments(appId, userAuthorityIds);
        
        // 构建返回结果
        AuthorityAssignmentDto result = new AuthorityAssignmentDto();
        result.setAppId(appId);
        result.setAppName(app.getAppName());
        result.setUserId(userId);
        result.setUserName(user.getName());
        result.setModules(modules);
        
        log.info("用户权限分配情况查询成功 - 模块数: {}", modules.size());
        return result;
    }

    /**
     * 查询角色在指定应用下的权限分配情况（按模块组织）
     * 
     * @param appId 应用ID
     * @param roleId 角色ID
     * @return 权限分配状态DTO
     */
    public AuthorityAssignmentDto getRoleAuthorityAssignment(Long appId, Long roleId) {
        log.info("查询角色权限分配情况 - 应用ID: {}, 角色ID: {}", appId, roleId);
        
        // 获取应用和角色信息
        App app = appRepository.findById(appId)
            .orElseThrow(() -> new ResourceNotFoundException("应用不存在，ID: " + appId));
        Role role = roleRepository.findById(roleId)
            .orElseThrow(() -> new ResourceNotFoundException("角色不存在，ID: " + roleId));
        
        // 获取角色拥有的权限ID
        Set<Long> roleAuthorityIds = role.getAuthorities().stream()
            .map(Authority::getAuthorityId)
            .collect(Collectors.toSet());
        
        // 获取应用下的模块权限分配情况
        List<AuthorityAssignmentDto.ModuleAuthorityDto> modules = getModuleAuthorityAssignments(appId, roleAuthorityIds);
        
        // 构建返回结果
        AuthorityAssignmentDto result = new AuthorityAssignmentDto();
        result.setAppId(appId);
        result.setAppName(app.getAppName());
        result.setRoleId(roleId);
        result.setRoleName(role.getRoleName());
        result.setModules(modules);
        
        log.info("角色权限分配情况查询成功 - 模块数: {}", modules.size());
        return result;
    }

    /**
     * 获取用户通过角色拥有的所有权限ID
     */
    private Set<Long> getUserAuthorityIds(User user) {
        return user.getRoles().stream()
            .filter(role -> role.getDelFlag() == 0) // 过滤未删除的角色
            .flatMap(role -> role.getAuthorities().stream())
            .map(Authority::getAuthorityId)
            .collect(Collectors.toSet());
    }

    /**
     * 获取应用下各模块的权限分配情况
     */
    private List<AuthorityAssignmentDto.ModuleAuthorityDto> getModuleAuthorityAssignments(Long appId, Set<Long> assignedAuthorityIds) {
        List<Module> modules = moduleRepository.findAllByAppId(appId);
        
        return modules.stream()
            .map(module -> createModuleAuthorityDto(module, assignedAuthorityIds))
            .collect(Collectors.toList());
    }

    /**
     * 创建单个模块的权限分配DTO
     */
    private AuthorityAssignmentDto.ModuleAuthorityDto createModuleAuthorityDto(Module module, Set<Long> assignedAuthorityIds) {
        List<Authority> moduleAuthorities = authorityRepository.findByModuleId(module.getModuleId());
        
        // 分离已分配和未分配的权限
        Map<Boolean, List<AuthorityDisplayDto>> partitionedAuthorities = moduleAuthorities.stream()
            .map(AuthorityDisplayDto::new)
            .collect(Collectors.partitioningBy(dto -> assignedAuthorityIds.contains(dto.getAuthorityId())));
        
        AuthorityAssignmentDto.ModuleAuthorityDto moduleDto = new AuthorityAssignmentDto.ModuleAuthorityDto();
        moduleDto.setModuleId(module.getModuleId());
        moduleDto.setModuleName(module.getModuleName());
        moduleDto.setModuleDescription(module.getModuleDescription());
        moduleDto.setAssignedAuthorities(partitionedAuthorities.get(true));
        moduleDto.setUnassignedAuthorities(partitionedAuthorities.get(false));
        
        return moduleDto;
    }

    /**
     * 查询指定应用下资源类型的权限分配情况
     * 
     * @param appId 应用ID
     * @param resourceType 资源类型（可选，为空则查询所有类型）
     * @return 资源权限分配状态DTO
     */
    public ResourceAuthorityAssignmentDto getResourceAuthorityAssignment(Long appId, String resourceType) {
        log.info("查询资源权限分配情况 - 应用ID: {}, 资源类型: {}", appId, resourceType);
        
        // 获取应用信息
        App app = appRepository.findById(appId)
            .orElseThrow(() -> new ResourceNotFoundException("应用不存在，ID: " + appId));
        
        // 获取指定应用下指定资源类型的权限列表（包含角色关联信息）
        List<Authority> authorities = authorityRepository.findByAppIdAndResourceTypeWithRoles(appId, resourceType);
        
        // 构建权限分配信息列表
        List<ResourceAuthorityAssignmentDto.AuthorityAssignmentInfo> authorityInfos = authorities.stream()
            .map(this::createAuthorityAssignmentInfo)
            .collect(Collectors.toList());
        
        // 构建返回结果
        ResourceAuthorityAssignmentDto result = new ResourceAuthorityAssignmentDto();
        result.setAppId(appId);
        result.setAppName(app.getAppName());
        result.setResourceType(resourceType);
        result.setAuthorities(authorityInfos);
        
        log.info("资源权限分配情况查询成功 - 权限数: {}", authorityInfos.size());
        return result;
    }
    
    /**
     * 创建权限分配信息
     */
    private ResourceAuthorityAssignmentDto.AuthorityAssignmentInfo createAuthorityAssignmentInfo(Authority authority) {
        // 获取拥有该权限的角色列表
        List<RoleAppDTO> assignedRoles = authority.getRoles().stream()
            .filter(role -> role.getDelFlag() == 0) // 过滤未删除的角色
            .map(role -> new RoleAppDTO(role.getRoleId(), role.getRoleName(), role.getRoleDescription(), role.getDelFlag()))
            .collect(Collectors.toList());
        
        // 获取通过角色拥有该权限的用户列表（去重）
        Set<User> usersSet = authority.getRoles().stream()
            .filter(role -> role.getDelFlag() == 0)
            .flatMap(role -> role.getUsers().stream())
            .filter(user -> user.getDelFlag() == 0)
            .collect(Collectors.toSet());
        
        List<UserAppDTO> assignedUsers = usersSet.stream()
            .map(user -> new UserAppDTO(user.getUserId(), user.getName(), user.getDelFlag(), user.getSex()))
            .collect(Collectors.toList());
        
        // 构建权限分配信息
        ResourceAuthorityAssignmentDto.AuthorityAssignmentInfo info = 
            new ResourceAuthorityAssignmentDto.AuthorityAssignmentInfo();
        info.setAuthorityId(authority.getAuthorityId());
        info.setAuthorityName(authority.getAuthorityName());
        info.setAuthorityCode(authority.getAuthorityCode());
        info.setAuthorityDescription(authority.getAuthorityDescription());
        info.setResourceType(authority.getResourceType());
        info.setActions(authority.getActions());
        
        // 设置模块信息（取第一个模块，因为权限可能关联多个模块）
        if (!authority.getModules().isEmpty()) {
            Module module = authority.getModules().iterator().next();
            info.setModuleId(module.getModuleId());
            info.setModuleName(module.getModuleName());
            info.setModuleDescription(module.getModuleDescription());
        }
        
        info.setAssignedRoles(assignedRoles);
        info.setAssignedUsers(assignedUsers);
        info.setRoleCount(assignedRoles.size());
        info.setUserCount(assignedUsers.size());
        
        return info;
    }

    /**
     * 删除权限
     * @param authorityIds 权限ID列表
     */
    @Transactional
    public void deleteAuthorities(List<Long> authorityIds) {
        if (authorityIds == null || authorityIds.isEmpty()) {
            throw new IllegalArgumentException("权限ID列表不能为空");
        }

        log.info("开始删除权限，数量: {}", authorityIds.size());

        try {
            // 使用原生SQL直接删除关联表数据，性能更高
            log.debug("开始清理权限关联关系，权限数量: {}", authorityIds.size());

            // 1. 直接删除角色权限关联
            authorityRepository.deleteRoleAuthoritiesByAuthorityIds(authorityIds);
            log.debug("已清理角色权限关联");

            // 2. 直接删除部门权限关联
            authorityRepository.deleteDepartmentAuthoritiesByAuthorityIds(authorityIds);
            log.debug("已清理部门权限关联");

            // 3. 直接删除模块权限关联
            authorityRepository.deleteModuleAuthoritiesByAuthorityIds(authorityIds);
            log.debug("已清理模块权限关联");

            // 4. 检查权限是否存在
            List<Authority> authorities = authorityRepository.findAllById(authorityIds);
            if (authorities.size() != authorityIds.size()) {
                throw new ResourceNotFoundException("部分权限不存在");
            }

            // 5. 批量删除权限本身
            authorityRepository.deleteAllById(authorityIds);

            log.info("删除权限成功，数量: {}", authorityIds.size());

        } catch (DataAccessException e) {
            log.error("删除权限失败", e);
            throw new RuntimeException("删除权限时数据库操作失败", e);
        }
    }

    /**
     * 解析文件内容
     */
    private List<Map<String, Object>> parseFile(MultipartFile file) throws Exception {
        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            throw new IllegalArgumentException("文件名不能为空");
        }

        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();

        switch (extension) {
            case "xlsx":
                return parseExcelFile(file);
            case "csv":
                return parseCsvFile(file);
            case "json":
                return parseJsonFile(file);
            default:
                throw new IllegalArgumentException("不支持的文件格式: " + extension + "，支持格式: xlsx, csv, json");
        }
    }

    /**
     * 解析Excel文件
     */
    private List<Map<String, Object>> parseExcelFile(MultipartFile file) throws Exception {
        List<Map<String, Object>> result = new ArrayList<>();

        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);

            if (headerRow == null) {
                throw new IllegalArgumentException("Excel文件缺少表头行");
            }

            // 获取表头
            List<String> headers = new ArrayList<>();
            for (Cell cell : headerRow) {
                headers.add(cell.getStringCellValue());
            }

            // 解析数据行
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                Map<String, Object> data = new HashMap<>();
                for (int j = 0; j < headers.size(); j++) {
                    Cell cell = row.getCell(j);
                    String value = getCellValue(cell);
                    data.put(headers.get(j), value);
                }
                result.add(data);
            }
        }

        return result;
    }

    /**
     * 解析CSV文件
     */
    private List<Map<String, Object>> parseCsvFile(MultipartFile file) throws Exception {
        List<Map<String, Object>> result = new ArrayList<>();

        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(file.getInputStream(), StandardCharsets.UTF_8))) {

            String line;
            List<String> headers = null;

            while ((line = reader.readLine()) != null) {
                String[] fields = line.split(",");

                if (headers == null) {
                    headers = Arrays.asList(fields);
                    continue;
                }

                Map<String, Object> data = new HashMap<>();
                for (int i = 0; i < Math.min(headers.size(), fields.length); i++) {
                    data.put(headers.get(i), fields[i].trim());
                }
                result.add(data);
            }
        }

        return result;
    }

    /**
     * 解析JSON文件
     */
    private List<Map<String, Object>> parseJsonFile(MultipartFile file) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        String content = new String(file.getBytes(), StandardCharsets.UTF_8);

        TypeReference<List<Map<String, Object>>> typeRef = new TypeReference<List<Map<String, Object>>>() {};
        return mapper.readValue(content, typeRef);
    }

    /**
     * 构建权限请求对象
     */
    private AuthorityUploadRequest buildAuthorityRequest(Map<String, Object> data, String operator) {
        AuthorityUploadRequest request = new AuthorityUploadRequest();

        // 基本信息
        request.setAuthorityName(getString(data, "权限名称"));
        request.setAuthorityCode(getString(data, "权限编码"));
        request.setAuthorityDescription(getString(data, "权限说明"));
        request.setResourceType(getString(data, "资源类型"));

        // 操作列表
        String actionsStr = getString(data, "操作列表");
        if (actionsStr != null && !actionsStr.isEmpty()) {
            request.setActions(Arrays.asList(actionsStr.split("[,;]")));
        }

        // 资源路径
        String resourcePathsStr = getString(data, "资源路径");
        if (resourcePathsStr != null && !resourcePathsStr.isEmpty()) {
            request.setResourcePaths(Arrays.asList(resourcePathsStr.split("[,;]")));
        }

        // 数值字段
        request.setParentId(getLong(data, "父权限ID"));
        request.setLevel(getInteger(data, "权限层级", 1));
        request.setSortOrder(getInteger(data, "排序号", 0));

        // 应用和模块ID（从文件中读取）
        request.setAppId(getLong(data, "应用ID"));
        request.setModuleId(getLong(data, "模块ID"));

        // 其他信息
        request.setSyncSource(getString(data, "同步来源"));
        request.setCreatUser(operator);

        // 设置默认值
        request.setDefaults();

        return request;
    }

    private String getString(Map<String, Object> data, String key) {
        return getString(data, key, null);
    }

    private String getString(Map<String, Object> data, String key, String defaultValue) {
        Object value = data.get(key);
        return value != null ? value.toString().trim() : defaultValue;
    }

    private Long getLong(Map<String, Object> data, String key) {
        String value = getString(data, key);
        return value != null && !value.isEmpty() ? Long.parseLong(value) : null;
    }

    private Integer getInteger(Map<String, Object> data, String key, Integer defaultValue) {
        String value = getString(data, key);
        return value != null && !value.isEmpty() ? Integer.parseInt(value) : defaultValue;
    }

    /**
     * 获取单元格值
     */
    private String getCellValue(Cell cell) {
        if (cell == null) return "";

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    return numericValue == (long) numericValue ?
                        String.valueOf((long) numericValue) : String.valueOf(numericValue);
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            default:
                return "";
        }
    }

    // ==================== 模板管理相关方法 ====================

    /**
     * 生成权限导入模板
     */
    public void generateAuthorityTemplate(String format, HttpServletResponse response) throws IOException {
        log.info("生成权限导入模板，格式: {}", format);

        String formatLower = format.toLowerCase().trim();
        log.info("处理格式参数: [{}] -> [{}]", format, formatLower);

        switch (formatLower) {
            case "xlsx":
                log.info("生成Excel模板");
                generateExcelTemplate(response);
                break;
            case "csv":
                log.info("生成CSV模板");
                generateCsvTemplate(response);
                break;
            case "json":
                log.info("生成JSON模板");
                generateJsonTemplate(response);
                break;
            default:
                log.warn("不支持的格式: {}", format);
                response.sendError(HttpServletResponse.SC_BAD_REQUEST,
                    "不支持的格式: " + format + "，支持的格式: xlsx, csv, json");
                return;
        }
    }

    /**
     * 生成Excel格式模板
     */
    private void generateExcelTemplate(HttpServletResponse response) throws IOException {
        // 设置响应头
        String encodedFilename = "filename*=UTF-8''" + URLEncoder.encode("权限导入模板.xlsx", "UTF-8")
                .replaceAll("\\+", "%20");
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setHeader("Content-Disposition", "attachment; " + encodedFilename);

        // 创建Excel模板
        Workbook workbook = new XSSFWorkbook();
        Sheet sheet = workbook.createSheet("权限数据");

        // 创建绿色单元格样式
        XSSFCellStyle greenHeaderStyle = ((XSSFWorkbook) workbook).createCellStyle();
        greenHeaderStyle.setFillForegroundColor(new XSSFColor(new java.awt.Color(198, 239, 206), null));
        greenHeaderStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);

        // 创建表头行
        Row headerRow = sheet.createRow(0);
        String[] headers = getTemplateHeaders();

        // 设置列宽
        int[] columnWidths = {4000, 4000, 6000, 4000, 3000, 6000, 3000, 3000, 3000};

        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(greenHeaderStyle);
            if (i < columnWidths.length) {
                sheet.setColumnWidth(i, columnWidths[i]);
            }
        }

        // 添加示例数据行
        addExampleDataToExcel(sheet);

        // 写入响应流
        workbook.write(response.getOutputStream());
        workbook.close();
    }

    /**
     * 生成CSV格式模板
     */
    private void generateCsvTemplate(HttpServletResponse response) throws IOException {
        log.info("开始生成CSV模板");

        // 设置响应头
        String encodedFilename = "filename*=UTF-8''" + URLEncoder.encode("权限导入模板.csv", "UTF-8")
                .replaceAll("\\+", "%20");
        response.setContentType("text/csv; charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment; " + encodedFilename);

        // 创建CSV内容
        StringBuilder csv = new StringBuilder();

        // 添加标题行
        String[] headers = getTemplateHeaders();
        for (int i = 0; i < headers.length; i++) {
            if (i > 0) csv.append(",");
            csv.append("\"").append(headers[i]).append("\"");
        }
        csv.append("\n");

        // 添加示例数据
        String[][] examples = getExampleData();
        for (String[] example : examples) {
            for (int i = 0; i < example.length; i++) {
                if (i > 0) csv.append(",");
                csv.append("\"").append(example[i]).append("\"");
            }
            csv.append("\n");
        }

        // 添加BOM以支持Excel正确显示中文，然后写入CSV内容
        response.getOutputStream().write(new byte[]{(byte)0xEF, (byte)0xBB, (byte)0xBF});
        response.getOutputStream().write(csv.toString().getBytes("UTF-8"));
        response.getOutputStream().flush();

        log.info("CSV模板生成完成，大小: {} 字节", csv.length());
    }

    /**
     * 生成JSON格式模板
     */
    private void generateJsonTemplate(HttpServletResponse response) throws IOException {
        log.info("开始生成JSON模板");

        // 设置响应头
        String encodedFilename = "filename*=UTF-8''" + URLEncoder.encode("权限导入模板.json", "UTF-8")
                .replaceAll("\\+", "%20");
        response.setContentType("application/json; charset=UTF-8");
        response.setHeader("Content-Disposition", "attachment; " + encodedFilename);

        // 创建JSON模板数据
        List<Map<String, Object>> template = new ArrayList<>();
        String[] headers = getTemplateHeaders();
        String[][] examples = getExampleData();

        for (String[] example : examples) {
            Map<String, Object> item = new LinkedHashMap<>();
            for (int i = 0; i < headers.length && i < example.length; i++) {
                item.put(headers[i], example[i]);
            }
            template.add(item);
        }

        // 转换为JSON并写入响应
        ObjectMapper mapper = new ObjectMapper();
        String json = mapper.writerWithDefaultPrettyPrinter().writeValueAsString(template);

        // 设置字符编码并写入响应
        response.setCharacterEncoding("UTF-8");
        response.getWriter().write(json);
        response.getWriter().flush();

        log.info("JSON模板生成完成，大小: {} 字节", json.length());
    }

    /**
     * 获取模板表头
     */
    private String[] getTemplateHeaders() {
        return new String[]{
            "权限名称", "权限编码", "权限说明", "操作列表", "资源类型",
            "资源路径", "应用ID", "模块ID", "排序号"
        };
    }

    /**
     * 获取示例数据
     */
    private String[][] getExampleData() {
        return new String[][]{
            {"用户管理", "USER_MANAGE", "用户信息的增删改查权限", "create,read,update,delete",
             "menu", "/user/list,/user/add", "1", "1", "10"},
            {"角色管理", "ROLE_MANAGE", "角色信息的管理权限", "create,read,update,delete",
             "menu", "/role/list,/role/add", "1", "1", "20"},
            {"权限管理", "AUTHORITY_MANAGE", "权限信息的管理权限", "create,read,update,delete",
             "menu", "/authority/list,/authority/add", "1", "1", "30"}
        };
    }

    /**
     * 向Excel添加示例数据
     */
    private void addExampleDataToExcel(Sheet sheet) {
        String[][] examples = getExampleData();
        for (int i = 0; i < examples.length; i++) {
            Row row = sheet.createRow(i + 1);
            String[] example = examples[i];
            for (int j = 0; j < example.length; j++) {
                row.createCell(j).setCellValue(example[j]);
            }
        }
    }

}
