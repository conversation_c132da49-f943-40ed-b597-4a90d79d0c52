package cn.ac.iie.pmserver.service;

import cn.ac.iie.pmserver.response.ApiResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 文件服务类，负责处理文件上传、下载和删除
 */
@Service
public class FileService {

    @Value("${app.upload.dir}")
    private String uploadDir;

    /**
     * 上传文件
     * @param file 上传的文件
     * @return API响应
     */
    public ApiResponse uploadFile(MultipartFile file) {
        try {

            // 生成唯一文件名
            String originalFilename = file.getOriginalFilename();
            String extension = getFileExtension(originalFilename);
            String fileName = "appLogo_" + UUID.randomUUID().toString() + extension;

            // 确保上传目录存在
            File uploadPath = new File(uploadDir);
            if (!uploadPath.exists()) {
                uploadPath.mkdirs();
            }

            // 保存文件
            Path filePath = Paths.get(uploadDir, fileName);
            Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);

            // 构建响应
            Map<String, Object> response = new HashMap<>();
            response.put("fileName", fileName);
            response.put("fileUrl", "/api/files/images/" + fileName);
            response.put("fileSize", file.getSize());
            response.put("fileType", file.getContentType());

            return ApiResponse.success(response);
        } catch (Exception e){
            return ApiResponse.error(500, "上传失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     * @param fileName 文件名
     * @return API响应
     */
    public ApiResponse deleteFile(String fileName) {
        try {

            Path filePath = Paths.get(uploadDir, fileName);
            boolean deleted = Files.deleteIfExists(filePath);

            if (deleted) {
                return ApiResponse.error(200, "删除成功", null);
            } else {
                return ApiResponse.error(404, "文件不存在", null);
            }
        } catch (Exception e) {
            return ApiResponse.error(500, "删除失败: " + e.getMessage(), null);
        }
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String filename) {
        if (filename == null) return ".png";

        int lastDotIndex = filename.lastIndexOf(".");
        if (lastDotIndex == -1) return ".png";

        return filename.substring(lastDotIndex);
    }


//
//    /**
//     * 根据文件名确定内容类型
//     */
//    private String determineContentTypeFromFileName(String fileName) {
//        String extension = getFileExtension(fileName).toLowerCase();
//        switch (extension) {
//            case ".jpg":
//            case ".jpeg":
//                return "image/jpeg";
//            case ".png":
//                return "image/png";
//            default:
//                return "application/octet-stream";
//        }
//    }

//    /**
//     * 获取文件
//     * @param fileName 文件名
//     * @return 文件资源
//     */
//    public ApiResponse getFile(String fileName) {
//        try {
//
//            Path filePath = Paths.get(uploadDir, fileName);
//            Resource resource = new UrlResource(filePath.toUri());
//
//            if (resource.exists()) {
//                String contentType = Files.probeContentType(filePath);
//                if (contentType == null) {
//                    contentType = determineContentTypeFromFileName(fileName);
//                }
//
//                Map<String, Object> response = new HashMap<>();
//                response.put("contentType", contentType);
//                response.put("resource", resource);
//                return ApiResponse.success(response);
//            } else {
//                return ApiResponse.error(404, "文件不存在");
//            }
//        } catch (Exception e) {
//            return ApiResponse.error(500, "获取文件失败: " + e.getMessage());
//        }
//    }



//    /**
//     * 获取文件列表
//     * @return API响应
//     */
//    public ApiResponse getFileList() {
//        try {
//            File dir = new File(uploadDir);
//            if (!dir.exists() || !dir.isDirectory()) {
//                return ApiResponse.error(404, "上传目录不存在", null);
//            }
//
//            File[] files = dir.listFiles((d, name) -> name.startsWith("appLogo_"));
//            if (files == null || files.length == 0) {
//                return ApiResponse.error(200, "没有找到文件", new HashMap<>());
//            }
//
//            Map<String, Object> result = new HashMap<>();
//            for (File file : files) {
//                if (file.isFile()) {
//                    Map<String, Object> fileInfo = new HashMap<>();
//                    fileInfo.put("fileName", file.getName());
//                    fileInfo.put("fileUrl", "/api/files/" + file.getName());
//                    fileInfo.put("fileSize", file.length());
//                    fileInfo.put("lastModified", file.lastModified());
//
//                    result.put(file.getName(), fileInfo);
//                }
//            }
//            return ApiResponse.success(result);
//        } catch (Exception e) {
//            return ApiResponse.error(500, "获取文件列表失败: " + e.getMessage());
//        }
//    }


}
