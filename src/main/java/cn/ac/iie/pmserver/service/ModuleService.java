package cn.ac.iie.pmserver.service;

import cn.ac.iie.pmserver.Enum.DelFlagEnum;
import cn.ac.iie.pmserver.dto.app.ModuleAddDto;
import cn.ac.iie.pmserver.dto.app.ModuleDto;
import cn.ac.iie.pmserver.exception.ResourceNotFoundException;
import cn.ac.iie.pmserver.model.App;
import cn.ac.iie.pmserver.model.Module;
import cn.ac.iie.pmserver.repository.AppRepository;
import cn.ac.iie.pmserver.repository.ModuleRepository;
import cn.ac.iie.pmserver.utils.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

@Service
public class ModuleService {
    private final ModuleRepository moduleRepository;
    private final AppRepository appRepository;

    public ModuleService(ModuleRepository moduleRepository, AppRepository appRepository) {
        this.moduleRepository = moduleRepository;
        this.appRepository = appRepository;
    }

    /**
     * 根据appId获取应用模块列表
     * @param appId 应用id
     * @return 应用模块列表
     */
    public List<ModuleDto> getModulesByAppId(Long appId) {
        List<ModuleDto> modules = new ArrayList<>();
        Optional<App> app = appRepository.findById(appId);
        if(!app.isPresent()){
            return modules ;
        }
        List<ModuleDto> modules1 = moduleRepository.findAllByAppId(appId).stream().map(ModuleDto::new).toList();
        if(modules1.isEmpty()){
            return modules;
        }
        modules = modules1;
        return modules;
    }

    /**
     * 根据appId获取应用模块列表 分页
     * @param appId 应用id
     * @param pageable 分页参数
     * @return 应用模块列表
     */
    public Page<ModuleDto> getModulesByAppIdAndPage(Long appId, Pageable pageable) {
        Page<Module> modules = moduleRepository.findAllByAppId(appId, pageable);

        return modules.map(ModuleDto::new);
    }

    /**
     * 禁用模块
     * @param moduleId
     * @return
     */
    public boolean disableModule(Long moduleId) {
        Optional<Module> module = moduleRepository.findById(moduleId);
        if(module.isPresent()){
            Module module1 = module.get();
            module1.setDelFlag(DelFlagEnum.DISABLE.getValue());
            moduleRepository.save(module1);
            return true;
        }else {
            return false;
        }
    }

    /**
     * 更新模块状态
     * @param moduleId
     * @return
     */
    public void updateModuleStatus(Long moduleId) {
        Optional<Module> module = moduleRepository.findById(moduleId);
        if(module.isPresent()){
            Module module1 = module.get();
            if(module1.getDelFlag() == DelFlagEnum.ENABLE.getValue()){
                module1.setDelFlag(DelFlagEnum.DISABLE.getValue());
            }else{
                module1.setDelFlag(DelFlagEnum.ENABLE.getValue());
            }
            try {
                moduleRepository.save(module1);
            }
            catch (DataAccessException e){
                throw new RuntimeException("更新模块状态时数据库操作失败", e);
            }
        }else {
            throw new ResourceNotFoundException(String.format("更新模块状态时未查询到moduleId为%s的模块信息",moduleId));
        }
    }

    /**
     * 批量禁用模块
     * @param moduleIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDisableModule(List<Long> moduleIds) {
        for(Long moduleId:moduleIds){
            boolean b = disableModule(moduleId);
            if(!b){
                throw new ResourceNotFoundException(String.format("批量禁用模块时未查询到moduleId为%s的模块信息",moduleId));
            }
        }
    }

    /**
     * 更新模块信息
     * @param moduleId
     * @param moduleDto
     */
    public void updateModule(Long moduleId, ModuleDto moduleDto) {
        Optional<Module> module = moduleRepository.findById(moduleId);
        if(module.isPresent()){
            Module module1 = module.get();
            moduleDto.setCreatTime(module1.getCreatTime());
            moduleDto.setCreatUser(module1.getCreatUser());
            moduleDto.setUpdateTime(DateUtils.parse(DateUtils.getCurrentTime()));
            BeanUtils.copyProperties(moduleDto, module1);
            try {
                moduleRepository.save(module1);
            }
            catch (DataAccessException e){
                throw new RuntimeException("更新应用时数据库操作失败", e);
            }
        }else {
            throw new ResourceNotFoundException(String.format("更新应用时未查询到moduleId为%s的模块信息",moduleId));
        }

    }

    /**
     * 根据关键字搜索模块信息
     * @param keywords
     * @param appId
     * @return  List<ModuleDto>
     */
    public List<ModuleDto> searchModule(String keywords, Long appId) {
        List<ModuleDto> modules = moduleRepository.searchModule(keywords,appId).stream().map(ModuleDto::new).toList();
        if(modules.isEmpty()){
            throw new ResourceNotFoundException(String.format("未查询到关键字为%s的相关模块信息",keywords));
        }
        return modules;
    }

    @Transactional(rollbackFor = Exception.class)
    public void addModule(ModuleAddDto moduleAddDto) {
        try {
            Module module = new Module();
            BeanUtils.copyProperties(moduleAddDto,module);
            module.setCreatTime(DateUtils.parse(DateUtils.getCurrentTime()));
            Optional<App> appOpt = appRepository.findById(moduleAddDto.getAppId());
            if (appOpt.isEmpty()) {
                throw new ResourceNotFoundException("关联的应用不存在");
            }
            App app = appOpt.get();
            module.getApps().add(app);
            app.getModules().add(module);
            moduleRepository.save(module);
        } catch (Exception e) {
            throw new RuntimeException("新建模块失败: " + e.getMessage(), e);
        }
    }


//    //导入模块信息
//    public void resourcesImport(List<Map<String, Object>> resources) {
//        List<String> requiredFields = List.of("moduleName", "moduleUrl", "appId", "delFlag");
//        for (Map<String, Object> item : resources) {
//            for (String field : requiredFields) {
//                if (!item.containsKey(field)) {
//                    throw new IllegalArgumentException("缺少必填字段：" + field);
//                }
//            }
//            // 校验delFlag是否为0或1
//            Integer delFlag = (Integer) item.get("delFlag");
//            if (delFlag != 0 && delFlag != 1) {
//                throw new IllegalArgumentException("delFlag只能为0或1");
//            }
//        }
//        // 遍历map入库
//        resources.forEach(item -> {
//            Module module = new Module();
//            try {
//                module.setModuleName((String) item.get("moduleName"));
//                module.setModuleDescription((String) item.getOrDefault("moduleDescription", ""));
//                module.setModuleUrl((String) item.get("moduleUrl"));
//                module.setDelFlag((Integer) item.get("delFlag"));
//                module.setCreatTime(DateUtils.parse(DateUtils.getCurrentTime()));
//                module.setAppId((Long) item.get("appId"));
//                if (moduleRepository.findAllByAppId(module.getAppId()).isEmpty()) {
//                    Long appId = (Long) item.get("appId");
//                    String id = appId + "01";
//                    module.setModuleId(Long.parseLong(id));
//                }
//                moduleRepository.save(module);
//            } catch (DataAccessException e) {
//                throw new RuntimeException("导入模块时数据库操作失败", e);
//            }
//        });
//    }

}
