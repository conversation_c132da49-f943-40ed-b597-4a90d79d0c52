package cn.ac.iie.pmserver.service;

import cn.ac.iie.pmserver.Enum.DelFlagEnum;
import cn.ac.iie.pmserver.dto.app.*;
import cn.ac.iie.pmserver.exception.ResourceNotFoundException;
import cn.ac.iie.pmserver.model.App;
import cn.ac.iie.pmserver.model.Module;
import cn.ac.iie.pmserver.repository.AppRepository;
import cn.ac.iie.pmserver.repository.ModuleRepository;
import cn.ac.iie.pmserver.utils.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
public class AppService {

    private final AppRepository appRepository;

    private final ModuleRepository moduleRepository;

    private final JdbcTemplate jdbcTemplate;

    /**
     * 构造函数
     *
     * @param appRepository
     * @param moduleRepository
     */
    public AppService(AppRepository appRepository, ModuleRepository moduleRepository, JdbcTemplate jdbcTemplate) {
        this.appRepository = appRepository;
        this.moduleRepository = moduleRepository;
        this.jdbcTemplate = jdbcTemplate;
    }

    /**
     * 根据条件信息搜索应用
     *
     * @param appQueryDTO 应用的查询条件
     * @return 应用列表
     */
    public Page<AppDto> searchApps(AppQueryDTO appQueryDTO, Pageable pageable) {
        Page<App> appsPage = appRepository.searchApps(appQueryDTO, pageable);
        return appsPage.map(AppDto::new);
    }

    /**
     * 获取所有应用列表
     *
     * @return
     */
    public AppStatisticsDTO getAllAppsStatistics() {
        AppStatisticsDTO appStatisticsDTO = appRepository.getAllAppsStatistics();
        return appStatisticsDTO;

    }

    /**
     * 分页获取所有应用列表
     *
     * @param pageable 分页参数
     * @return
     */
    public Page<AppDto> getAllAppsByPage(Pageable pageable) {
        Page<App> allApps = appRepository.findAllApps(pageable);
        return allApps.map(AppDto::new);
    }


    /**
     * 禁用应用
     *
     * @param appId
     */
    public boolean disableApp(Long appId) {
        Optional<App> app = appRepository.findById(appId);
        if (app.isPresent()) {
            App app1 = app.get();
            app1.setDelFlag(DelFlagEnum.DISABLE.getValue());
            try {
                appRepository.save(app1);
                return true;
            } catch (DataAccessException e) {
                throw new RuntimeException("禁用应用时数据库操作失败", e);
            }
        } else {
            return false;
        }
    }

    /**
     * 启用应用
     *
     * @param appId
     * @return boolean
     */
    public boolean enableApp(Long appId) {
        Optional<App> app = appRepository.findById(appId);
        if (app.isPresent()) {
            App app1 = app.get();
            app1.setDelFlag(DelFlagEnum.ENABLE.getValue());
            try {
                appRepository.save(app1);
                return true;
            } catch (DataAccessException e) {
                throw new RuntimeException("启用应用时数据库操作失败", e);
            }
        } else {
            return false;
        }
    }

    /**
     * 根据id获取应用详情信息
     *
     * @param appId
     * @return AppDetailDto应用详情信息
     */
    public AppDetailDto getAppInfoById(Long appId) {
        //1.获取应用信息
        Optional<App> app = appRepository.findById(appId);
        if (app.isPresent()) {
            App app1 = app.get();
            //2.获取模块信息
            List<Module> modules = moduleRepository.findAllByAppId(appId);
            List<ModuleDto> moduleDtos = modules.stream().map(ModuleDto::new).toList();
            AppDetailDto appDetailDto = new AppDetailDto();
            appDetailDto.setAppDto(new AppDto(app1));
            appDetailDto.setModuleDtos(moduleDtos);
            return appDetailDto;
        } else {
            //app为空
            throw new ResourceNotFoundException(String.format("未查询到appId为%s的应用信息", appId));
        }
    }

    /**
     * 批量禁用应用
     *
     * @param appIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchDisableApp(List<Long> appIds) {
        for (Long appId : appIds) {
            boolean b = disableApp(appId);
            if (!b) {
                throw new ResourceNotFoundException(String.format("批量禁用应用时未查询到appId为%s的应用信息", appId));
            }
        }
    }

    /**
     * 修改应用信息
     *
     * @param appId
     * @param appDto
     */
    public void updateApp(Long appId, AppDto appDto) {
        Optional<App> app = appRepository.findById(appId);
        if (app.isPresent()) {
            App app1 = app.get();
            BeanUtils.copyProperties(appDto, app1);
            //设置为现在时间
            app1.setUpdateTime(DateUtils.parse(DateUtils.getCurrentTime()));
            try {
                appRepository.save(app1);
            } catch (DataAccessException e) {
                throw new RuntimeException("更新应用时数据库操作失败", e);
            }
        } else {
            throw new ResourceNotFoundException(String.format("更新应用时未查询到appId为%s的应用信息", appId));
        }
    }

    /**
     * 修改应用状态
     *
     * @param appId
     */
    public void updateAppStatus(Long appId) {
        Optional<App> app = appRepository.findById(appId);
        if (app.isPresent()) {
            App app1 = app.get();
            if (app1.getDelFlag() == DelFlagEnum.ENABLE.getValue()) {
                app1.setDelFlag(DelFlagEnum.DISABLE.getValue());
            } else {
                app1.setDelFlag(DelFlagEnum.ENABLE.getValue());
            }
            try {
                appRepository.save(app1);
            } catch (DataAccessException e) {
                throw new RuntimeException("更新应用状态时数据库操作失败", e);
            }
        } else {
            throw new ResourceNotFoundException(String.format("更新app状态时未查询到appId为%s的应用信息", appId));
        }

    }

    /**
     * 新增应用
     *
     * @param app
     */
    public void addApp(App app) {
        app.setCreatTime(DateUtils.parse(DateUtils.getCurrentTime()));
        try {
            appRepository.save(app);
        } catch (DataAccessException e) {
            throw new RuntimeException("新建应用时数据库操作失败", e);
        }
    }

    /**
     * 删除应用
     *
     * @param appIds
     */
    @Transactional(rollbackFor = Exception.class)
    public void deleteApp(List<Long> appIds) {
        try {
            String inClause = Collections.nCopies(appIds.size(), "?").stream()
                    .collect(Collectors.joining(",", "(", ")"));
            // 先删除中间表数据
            jdbcTemplate.update("DELETE FROM modules_apps WHERE app_id IN " + inClause, appIds.toArray());
            jdbcTemplate.update("DELETE FROM roles_apps WHERE app_id IN " + inClause, appIds.toArray());
            jdbcTemplate.update("DELETE FROM users_apps WHERE app_id IN " + inClause, appIds.toArray());

            // 再删除主表数据
            appRepository.deleteAllById(appIds);
        } catch (DataAccessException e) {
            throw new RuntimeException("删除应用时数据库操作失败", e);
        }
    }

    public List<AppListDTO> getAppList() {
        try {
            List<App> apps = appRepository.findAll();
            return apps.stream()
                    .map(AppListDTO::new)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw new RuntimeException("获取应用列表时数据库操作失败", e);
        }
    }


//    //导入应用信息
//    public void resourcesImport(List<Map<String, Object>> resources) {
//        // 校验必填字段是否存在
//        List<String> requiredFields = List.of("appName", "appUrl", "appCallbackUrl", "appLogo", "delFlag");
//        for (Map<String, Object> item : resources) {
//            for (String field : requiredFields) {
//                if (!item.containsKey(field)) {
//                    throw new IllegalArgumentException("缺少必填字段：" + field);
//                }
//            }
//            // 校验delFlag是否为0或1
//            Integer delFlag = (Integer) item.get("delFlag");
//            if (delFlag != 0 && delFlag != 1) {
//                throw new IllegalArgumentException("delFlag只能为0或1");
//            }
//        }
//
//        // 遍历map入库
//        resources.forEach(item -> {
//            App app = new App();
//             try {
//                 app.setAppName((String) item.get("appName"));
//                 app.setAppDescription((String) item.getOrDefault("appDescription", ""));
//                 app.setAppUrl((String) item.get("appUrl"));
//                 app.setAppCallbackUrl((String) item.get("appCallbackUrl"));
//                 app.setAppLogo((String) item.get("appLogo"));
//                 app.setDelFlag((Integer) item.get("delFlag"));
//                 app.setCreatTime(DateUtils.parse(DateUtils.getCurrentTime()));
//                appRepository.save(app);
//             }catch (DataAccessException e){
//                 throw new RuntimeException("导入应用信息时数据库操作失败", e);
//             }
//        });
//    }
}
