package cn.ac.iie.pmserver.service;

import cn.ac.iie.pmserver.model.Department;
import cn.ac.iie.pmserver.repository.DepartmentRepository;
import cn.ac.iie.pmserver.repository.UserRepository;
import cn.ac.iie.pmserver.specification.DepartmentSpecification;
import cn.ac.iie.pmserver.controller.department.vo.DepartmentTreeVo;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class DepartmentService {
    private final DepartmentRepository departmentRepository;

    public DepartmentService(DepartmentRepository departmentRepository) {
        this.departmentRepository = departmentRepository;
    }

    // 获取部门树结构
    @Transactional(readOnly = true)
    public List<DepartmentTreeVo> getDepartmentTree() {
        // 获取所有部门并按orderNum排序
        List<Department> allDepartments = departmentRepository.findAll(
                Specification.where(DepartmentSpecification.searchDepartments(null)),
                Sort.by(Sort.Direction.ASC, "orderNum") // 在查询时直接排序
        );

        // 将部门列表转换为树结构
        Map<Long, DepartmentTreeVo> nodeMap = allDepartments.stream()
                .map(DepartmentTreeVo::new)
                .collect(Collectors.toMap(DepartmentTreeVo::getId, vo -> vo));

        // 对顶级部门也按orderNum排序
        return allDepartments.stream()
                .filter(dept -> dept.getParentId() == null) // 顶级部门
                .map(dept -> buildTree(dept, nodeMap, allDepartments))
                .collect(Collectors.toList());
    }

    private DepartmentTreeVo buildTree(Department department,
                                       Map<Long, DepartmentTreeVo> nodeMap,
                                       List<Department> allDepartments) {
        DepartmentTreeVo vo = nodeMap.get(department.getDepartmentId());

        // 获取子部门并按orderNum排序
        List<Department> children = allDepartments.stream()
                .filter(d -> department.getDepartmentId().equals(d.getParentId()))
                .collect(Collectors.toList());

        for (Department child : children) {
            vo.getChildren().add(buildTree(child, nodeMap, allDepartments));
        }

        return vo;
    }

    // 关键字过滤查询（分页）
    @Transactional(readOnly = true)
    public Page<Department> searchDepartments(String keyword, Pageable pageable) {
        return departmentRepository.findAll(
                DepartmentSpecification.searchDepartments(keyword),
                pageable
        );
    }

    // 添加部门（支持同级/子级）
    @Transactional
    public Department addDepartment(Long parentId, String departmentName) {
        Department parent = parentId != null ?
                departmentRepository.findById(parentId).orElseThrow() : null;

        Department newDept = new Department();
        newDept.setDepartmentName(departmentName);
        newDept.setDelFlag(0);
        newDept.setCreatTime(LocalDateTime.now());
        newDept.setUpdateTime(LocalDateTime.now());

        // 添加默认值（关键修改）
        newDept.setDepartmentHead("默认负责人"); // 部门负责人默认值
        newDept.setDepartmentDescription("默认部门描述"); // 部门描述默认值
        newDept.setAddress("默认地址"); // 地址默认值
        newDept.setCreatUser("系统管理员"); // 创建人默认值（可根据登录用户调整）
        newDept.setUpdateUser("系统管理员"); // 更新人默认值（可根据登录用户调整）

        // 计算层级和路径（原有逻辑）
        if (parent != null) {
            newDept.setParentId(parent.getDepartmentId());
            newDept.setLevel(parent.getLevel() + 1);
            newDept.setPath(parent.getPath() + "," + newDept.getDepartmentId());
        } else {
            newDept.setParentId(null);
            newDept.setLevel(1);
            newDept.setPath("0," + newDept.getDepartmentId());
        }

        // 计算排序号（原有逻辑）
        Integer maxOrder = departmentRepository.findMaxOrderNumByParentId(parentId);
        newDept.setOrderNum(maxOrder != null ? maxOrder + 1 : 1);

        return departmentRepository.save(newDept);
    }

    // 编辑部门名称
    @Transactional
    public void updateDepartmentName(Long id, String newName) {
        Department dept = departmentRepository.findById(id).orElseThrow();
        dept.setDepartmentName(newName);
        dept.setUpdateTime(LocalDateTime.now());
        departmentRepository.save(dept);
    }

    // 删除部门（逻辑删除）
    @Transactional
    public void deleteDepartment(Long id) {
        if (!departmentRepository.existsById(id)) {
            throw new IllegalArgumentException("部门ID不存在: " + id);
        }
        departmentRepository.deleteById(id);
    }

    // 拖拽调整部门位置
    @Transactional
    public List<Department> moveDepartment(Long sourceId, Long targetId, String position) {
        Department source = departmentRepository.findById(sourceId)
                .orElseThrow(() -> new IllegalArgumentException("源部门不存在: " + sourceId));
        Department target = departmentRepository.findById(targetId)
                .orElseThrow(() -> new IllegalArgumentException("目标部门不存在: " + targetId));

        // 防止无效操作
        if (source.getDepartmentId().equals(target.getDepartmentId())) {
            throw new IllegalArgumentException("不能将部门移动到自身");
        }

        // 检查是否尝试将父节点移动到自己的子节点
        if (isChildNode(source, target)) {
            throw new IllegalArgumentException("不能将部门移动到自己的子部门");
        }

        List<Department> updatedDepartments;
        switch (position) {
            case "before":
            case "after":
                updatedDepartments = moveAsSibling(source, target, position);
                break;
            case "inner":
                updatedDepartments = moveAsChild(source, target);
                break;
            default:
                throw new IllegalArgumentException("无效的位置参数: " + position);
        }

        // 更新路径和层级
        updatePath(source, target.getParent());
        return updatedDepartments;
    }

    private boolean isChildNode(Department parent, Department child) {
        if (child.getParentId() == null) {
            return false;
        }
        if (child.getParentId().equals(parent.getDepartmentId())) {
            return true;
        }
        Department childParent = departmentRepository.findById(child.getParentId())
                .orElseThrow(() -> new IllegalArgumentException("父部门不存在: " + child.getParentId()));
        return isChildNode(parent, childParent);
    }

    private List<Department> moveAsSibling(Department source, Department target, String position) {
        // 设置相同的父部门
        source.setParentId(target.getParentId());
        source.setLevel(target.getLevel());

        // 获取所有同级部门（排除自己）
        List<Department> siblings = departmentRepository.findByParentIdOrderByOrderNum(target.getParentId());
        siblings.removeIf(dept -> dept.getDepartmentId().equals(source.getDepartmentId()));

        // 找到目标位置并插入
        int targetIndex = siblings.indexOf(target);
        if ("after".equals(position)) {
            targetIndex++;
        }
        siblings.add(targetIndex, source);

        // 重新计算所有同级部门的orderNum
        for (int i = 0; i < siblings.size(); i++) {
            siblings.get(i).setOrderNum(i);
        }

        // 批量保存并返回更新后的列表
        return departmentRepository.saveAll(siblings);
    }

    private List<Department> moveAsChild(Department source, Department target) {
        // 设置新父部门
        source.setParentId(target.getDepartmentId());
        source.setLevel(target.getLevel() + 1);

        // 获取目标部门所有子节点（排除自己）
        List<Department> children = departmentRepository.findByParentId(target.getDepartmentId());
        children.removeIf(dept -> dept.getDepartmentId().equals(source.getDepartmentId()));

        // 添加到子节点列表末尾
        children.add(source);

        // 重新排序子节点
        for (int i = 0; i < children.size(); i++) {
            children.get(i).setOrderNum(i);
        }

        // 更新目标部门的hasChildren状态
        target.setHasChildren(!children.isEmpty());
        departmentRepository.save(target);

        // 批量保存并返回更新后的子节点列表
        return departmentRepository.saveAll(children);
    }

    private void updatePath(Department dept, Department parent) {
        if (parent == null) {
            dept.setPath("0");
        } else {
            dept.setPath(parent.getPath() + "," + dept.getDepartmentId());
        }
        departmentRepository.save(dept);

        // 递归更新子部门路径
        List<Department> children = departmentRepository.findByParentId(dept.getDepartmentId());
        for (Department child : children) {
            updatePath(child, dept);
        }
    }
}