package cn.ac.iie.pmserver.service;

import cn.ac.iie.pmserver.dto.policies.PoliciesDto;
import cn.ac.iie.pmserver.dto.policies.PolicyEvaluationDto;
import cn.ac.iie.pmserver.dto.policies.PolicyEvaluationResultDto;
import cn.ac.iie.pmserver.dto.policies.PolicyQueryDto;
import cn.ac.iie.pmserver.exception.ResourceNotFoundException;
import cn.ac.iie.pmserver.model.App;
import cn.ac.iie.pmserver.model.Policies;
import cn.ac.iie.pmserver.model.Role;
import cn.ac.iie.pmserver.model.User;
import cn.ac.iie.pmserver.repository.AppRepository;
import cn.ac.iie.pmserver.repository.PoliciesRepository;
import cn.ac.iie.pmserver.repository.RoleRepository;
import cn.ac.iie.pmserver.repository.UserRepository;
import cn.ac.iie.pmserver.utils.DateUtils;
import cn.ac.iie.pmserver.utils.JsonUtils;
import cn.ac.iie.pmserver.utils.LogUtils;
import cn.ac.iie.pmserver.utils.PolicyUtils;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.springframework.dao.DataAccessException;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.persistence.criteria.Predicate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 策略服务类
 * 提供策略相关的所有业务操作
 */
@Service
@RequiredArgsConstructor
public class PoliciesService {

    private static final Logger logger = LogUtils.get(PoliciesService.class);

    private final PoliciesRepository policiesRepository;
    private final RoleRepository roleRepository;
    private final UserRepository userRepository;
    private final AppRepository appRepository;

    // ==================== 基础CRUD操作 ====================

    /**
     * 根据策略ID获取策略信息
     * @param policyId 策略ID
     * @return 策略DTO
     */
    @Transactional(readOnly = true)
    public PoliciesDto getPolicyById(Long policyId) {
        Policies policy = policiesRepository.findById(policyId)
            .orElseThrow(() -> new ResourceNotFoundException("策略不存在，ID: " + policyId));
        return new PoliciesDto(policy);
    }

    /**
     * 获取所有策略列表
     * @return 策略DTO列表
     */
    @Transactional(readOnly = true)
    public List<PoliciesDto> getAllPolicies() {
        List<Policies> policies = policiesRepository.findAll();
        return policies.stream()
            .map(PoliciesDto::new)
            .collect(Collectors.toList());
    }

    /**
     * 分页查询策略
     * @param queryDto 查询条件
     * @param pageable 分页参数
     * @return 策略分页结果
     */
    @Transactional(readOnly = true)
    public Page<PoliciesDto> searchPolicies(PolicyQueryDto queryDto, Pageable pageable) {
        Specification<Policies> spec = buildPolicySpecification(queryDto);
        Page<Policies> policyPage = policiesRepository.findAll(spec, pageable);
        return policyPage.map(PoliciesDto::new);
    }

    /**
     * 创建策略
     * @param policyDto 策略DTO
     * @return 创建的策略DTO
     */
    @Transactional
    public PoliciesDto createPolicy(PoliciesDto policyDto) {
        try {
            // 检查策略名称是否已存在
            if (policyDto.getPolicyName() != null &&
                policiesRepository.existsByPolicyName(policyDto.getPolicyName())) {
                throw new IllegalArgumentException("策略名称已存在: " + policyDto.getPolicyName());
            }

            Policies policy = new Policies();
            policy.setPolicyName(policyDto.getPolicyName());
            policy.setPolicyDescription(policyDto.getPolicyDescription());
            policy.setPolicyCode(policyDto.getPolicyCode());
            policy.setPolicyType(policyDto.getPolicyType());
            policy.setEffect(policyDto.getEffect());

            // 将DTO中的复杂对象转换为JSON字符串存储
            policy.setConditions(JsonUtils.toJson(policyDto.getConditions()));
            policy.setResources(JsonUtils.toJson(policyDto.getResources()));
            policy.setActions(JsonUtils.toJson(policyDto.getActions()));

            policy.setPriority(policyDto.getPriority());
            policy.setStatus(policyDto.getStatus() != null ? policyDto.getStatus() : 0);
            policy.setEffectiveTime(policyDto.getEffectiveTime());
            policy.setExpireTime(policyDto.getExpireTime());

            policy.setDelFlag(0); // 默认正常状态
            policy.setCreatUser(policyDto.getCreatUser());
            policy.setCreatTime(LocalDateTime.now());
            policy.setUpdateUser(policyDto.getCreatUser());
            policy.setUpdateTime(LocalDateTime.now());

            // 处理关联关系
            handlePolicyAssociations(policy, policyDto);

            Policies savedPolicy = policiesRepository.save(policy);
            logger.info("创建策略成功: {}", savedPolicy.getPolicyName());

            return new PoliciesDto(savedPolicy);
        } catch (DataAccessException e) {
            logger.error("创建策略失败", e);
            throw new RuntimeException("创建策略时数据库操作失败", e);
        }
    }

    /**
     * 更新策略信息
     * @param policyId 策略ID
     * @param policyDto 策略DTO
     * @return 更新后的策略DTO
     */
    @Transactional
    public PoliciesDto updatePolicy(Long policyId, PoliciesDto policyDto) {
        try {
            Policies policy = policiesRepository.findById(policyId)
                .orElseThrow(() -> new ResourceNotFoundException("策略不存在，ID: " + policyId));

            // 更新基本信息
            policy.setPolicyName(policyDto.getPolicyName());
            policy.setPolicyDescription(policyDto.getPolicyDescription());
            policy.setPolicyCode(policyDto.getPolicyCode());
            policy.setPolicyType(policyDto.getPolicyType());
            policy.setEffect(policyDto.getEffect());

            // 更新策略内容
            if (policyDto.getConditions() != null) {
                policy.setConditions(JsonUtils.toJson(policyDto.getConditions()));
            }
            if (policyDto.getResources() != null) {
                policy.setResources(JsonUtils.toJson(policyDto.getResources()));
            }
            if (policyDto.getActions() != null) {
                policy.setActions(JsonUtils.toJson(policyDto.getActions()));
            }

            if (policyDto.getPriority() != null) {
                policy.setPriority(policyDto.getPriority());
            }
            if (policyDto.getStatus() != null) {
                policy.setStatus(policyDto.getStatus());
            }
            if (policyDto.getEffectiveTime() != null) {
                policy.setEffectiveTime(policyDto.getEffectiveTime());
            }
            if (policyDto.getExpireTime() != null) {
                policy.setExpireTime(policyDto.getExpireTime());
            }

            policy.setUpdateUser(policyDto.getUpdateUser());
            policy.setUpdateTime(LocalDateTime.now());

            // 更新关联关系
            handlePolicyAssociations(policy, policyDto);

            Policies savedPolicy = policiesRepository.save(policy);
            logger.info("更新策略成功: {}", savedPolicy.getPolicyName());

            return new PoliciesDto(savedPolicy);
        } catch (DataAccessException e) {
            logger.error("更新策略失败", e);
            throw new RuntimeException("更新策略时数据库操作失败", e);
        }
    }

    /**
     * 更新策略状态
     * @param policyId 策略ID
     * @param status 新状态
     * @return 更新结果
     */
    @Transactional
    public boolean updatePolicyStatus(Long policyId, Integer status) {
        try {
            Policies policy = policiesRepository.findById(policyId)
                .orElseThrow(() -> new ResourceNotFoundException("策略不存在，ID: " + policyId));

            policy.setDelFlag(status);
            policy.setUpdateTime(LocalDateTime.now());
            policiesRepository.save(policy);

            logger.info("更新策略状态成功: {} -> {}", policy.getPolicyName(), status);
            return true;
        } catch (DataAccessException e) {
            logger.error("更新策略状态失败", e);
            throw new RuntimeException("更新策略状态时数据库操作失败", e);
        }
    }

    /**
     * 删除策略
     * @param policyIds 策略ID列表
     * @return 删除结果
     */
    @Transactional
    public int deletePolicies(List<Long> policyIds) {
        if (policyIds == null || policyIds.isEmpty()) {
            return 0;
        }

        try {
            // 检查策略是否存在
            List<Policies> policies = policiesRepository.findAllById(policyIds);
            if (policies.size() != policyIds.size()) {
                throw new ResourceNotFoundException("部分策略不存在");
            }

            // 检查策略是否被使用
            for (Policies policy : policies) {
                if (!policy.getRoles().isEmpty()) {
                    throw new IllegalStateException("策略 " + policy.getPolicyName() + " 正在被使用，无法删除");
                }
            }

            policiesRepository.deleteAllById(policyIds);
            logger.info("删除策略成功，数量: {}", policyIds.size());
            return policyIds.size();
        } catch (DataAccessException e) {
            logger.error("删除策略失败", e);
            throw new RuntimeException("删除策略时数据库操作失败", e);
        }
    }

    // ==================== 策略执行与评估 ====================

    /**
     * 策略评估
     * @param evaluationDto 评估参数
     * @return 评估结果
     */
    @Transactional(readOnly = true)
    public PolicyEvaluationResultDto evaluatePolicy(PolicyEvaluationDto evaluationDto) {
        long startTime = System.currentTimeMillis();

        try {
            // 获取用户信息
            User user = userRepository.findById(evaluationDto.getUserId())
                .orElseThrow(() -> new ResourceNotFoundException("用户不存在，ID: " + evaluationDto.getUserId()));

            // 获取适用的策略
            List<Policies> applicablePolicies = getApplicablePolicies(user, evaluationDto);

            // 按优先级排序
            applicablePolicies.sort((p1, p2) -> {
                PoliciesDto dto1 = new PoliciesDto(p1);
                PoliciesDto dto2 = new PoliciesDto(p2);
                return dto2.getPriority().compareTo(dto1.getPriority());
            });

            // 评估策略
            PolicyEvaluationResultDto result = new PolicyEvaluationResultDto();
            List<PolicyEvaluationResultDto.PolicyMatchDto> matchedPolicies = new ArrayList<>();
            String finalDecision = "DENY"; // 默认拒绝
            String reason = "无匹配策略";

            for (Policies policy : applicablePolicies) {
                PoliciesDto policyDto = new PoliciesDto(policy);

                if (matchesPolicy(policyDto, evaluationDto)) {
                    PolicyEvaluationResultDto.PolicyMatchDto match = new PolicyEvaluationResultDto.PolicyMatchDto();
                    match.setPolicyId(policy.getPolicyId());
                    match.setPolicyName(policy.getPolicyName());
                    match.setEffect(policyDto.getEffect());
                    match.setPriority(policyDto.getPriority());
                    match.setMatchReason("策略条件匹配");

                    matchedPolicies.add(match);

                    // 如果是DENY策略，立即返回
                    if ("DENY".equals(policyDto.getEffect())) {
                        finalDecision = "DENY";
                        reason = "匹配到拒绝策略: " + policy.getPolicyName();
                        match.setIsFinalDecision(true);
                        break;
                    } else if ("ALLOW".equals(policyDto.getEffect())) {
                        finalDecision = "ALLOW";
                        reason = "匹配到允许策略: " + policy.getPolicyName();
                        match.setIsFinalDecision(true);
                    }
                }
            }

            long evaluationTime = System.currentTimeMillis() - startTime;

            result.setDecision(finalDecision);
            result.setMatchedPolicies(matchedPolicies);
            result.setEvaluationTime(evaluationTime);
            result.setReason(reason);

            if (evaluationDto.getVerbose()) {
                Map<String, Object> details = new HashMap<>();
                details.put("totalPoliciesChecked", applicablePolicies.size());
                details.put("userRoles", user.getRoles().stream()
                    .map(role -> role.getRoleName())
                    .collect(Collectors.toList()));
                details.put("context", evaluationDto.getContext());
                result.setDetails(details);
            }

            return result;
        } catch (Exception e) {
            logger.error("策略评估失败", e);

            PolicyEvaluationResultDto errorResult = new PolicyEvaluationResultDto();
            errorResult.setDecision("DENY");
            errorResult.setReason("策略评估异常: " + e.getMessage());
            errorResult.setEvaluationTime(System.currentTimeMillis() - startTime);
            errorResult.setMatchedPolicies(new ArrayList<>());

            return errorResult;
        }
    }

    /**
     * 批量策略评估
     * @param requests 评估请求列表
     * @return 评估结果列表
     */
    @Transactional(readOnly = true)
    public List<PolicyEvaluationResultDto> evaluatePoliciesBatch(List<PolicyEvaluationDto> requests) {
        return requests.stream()
            .map(this::evaluatePolicy)
            .collect(Collectors.toList());
    }

    /**
     * 获取策略统计信息
     * @return 统计信息
     */
    @Transactional(readOnly = true)
    public Map<String, Object> getPolicyStatistics() {
        Map<String, Object> statistics = new HashMap<>();

        long totalPolicies = policiesRepository.count();

        // 按状态统计
        List<Policies> allPolicies = policiesRepository.findAll();
        long activePolicies = allPolicies.stream()
            .filter(policy -> policy.getDelFlag() == 0)
            .count();
        long disabledPolicies = allPolicies.stream()
            .filter(policy -> policy.getDelFlag() == 1)
            .count();
        long draftPolicies = allPolicies.stream()
            .filter(policy -> policy.getDelFlag() == 2)
            .count();

        // 按类型统计
        long rbacPolicies = allPolicies.stream()
            .filter(policy -> "RBAC".equals(new PoliciesDto(policy).getPolicyType()))
            .count();
        long abacPolicies = allPolicies.stream()
            .filter(policy -> "ABAC".equals(new PoliciesDto(policy).getPolicyType()))
            .count();
        long aclPolicies = allPolicies.stream()
            .filter(policy -> "ACL".equals(new PoliciesDto(policy).getPolicyType()))
            .count();

        // 按效果统计
        long allowPolicies = allPolicies.stream()
            .filter(policy -> "ALLOW".equals(new PoliciesDto(policy).getEffect()))
            .count();
        long denyPolicies = allPolicies.stream()
            .filter(policy -> "DENY".equals(new PoliciesDto(policy).getEffect()))
            .count();

        statistics.put("totalPolicies", totalPolicies);
        statistics.put("activePolicies", activePolicies);
        statistics.put("disabledPolicies", disabledPolicies);
        statistics.put("draftPolicies", draftPolicies);
        statistics.put("rbacPolicies", rbacPolicies);
        statistics.put("abacPolicies", abacPolicies);
        statistics.put("aclPolicies", aclPolicies);
        statistics.put("allowPolicies", allowPolicies);
        statistics.put("denyPolicies", denyPolicies);

        return statistics;
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 处理策略关联关系
     * @param policy 策略实体
     * @param policyDto 策略DTO
     */
    private void handlePolicyAssociations(Policies policy, PoliciesDto policyDto) {
        // 处理角色关联
        if (policyDto.getRoles() != null && !policyDto.getRoles().isEmpty()) {
            Set<Role> roles = policyDto.getRoles().stream()
                .map(roleDto -> roleRepository.findById(roleDto.getRoleId())
                    .orElseThrow(() -> new ResourceNotFoundException("角色不存在，ID: " + roleDto.getRoleId())))
                .collect(Collectors.toSet());
            policy.setRoles(roles);
        }

        // ❌ 删除：策略不应该直接关联用户，应该通过角色关联（符合RBAC原则）
        // 正确的策略应用路径：Policies -> Role -> User

        // 处理应用关联
        if (policyDto.getApps() != null && !policyDto.getApps().isEmpty()) {
            Set<App> apps = policyDto.getApps().stream()
                .map(appDto -> appRepository.findById(appDto.getAppId())
                    .orElseThrow(() -> new ResourceNotFoundException("应用不存在，ID: " + appDto.getAppId())))
                .collect(Collectors.toSet());
            policy.setApps(apps);
        }
    }

    /**
     * 构建策略查询条件
     * @param queryDto 查询条件
     * @return JPA Specification
     */
    private Specification<Policies> buildPolicySpecification(PolicyQueryDto queryDto) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 关键字搜索
            if (queryDto.getKeyword() != null && !queryDto.getKeyword().trim().isEmpty()) {
                String keyword = "%" + queryDto.getKeyword().trim() + "%";
                Predicate namePredicate = criteriaBuilder.like(root.get("policyName"), keyword);
                Predicate descPredicate = criteriaBuilder.like(root.get("policyDescription"), keyword);
                predicates.add(criteriaBuilder.or(namePredicate, descPredicate));
            }

            // 状态过滤
            if (queryDto.getStatus() != null) {
                predicates.add(criteriaBuilder.equal(root.get("delFlag"), queryDto.getStatus()));
            }

            // 创建人过滤
            if (queryDto.getCreatUser() != null && !queryDto.getCreatUser().trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("creatUser"), queryDto.getCreatUser()));
            }

            // 创建时间范围
            if (queryDto.getCreateTimeStart() != null && !queryDto.getCreateTimeStart().isEmpty()) {
                LocalDateTime startTime = DateUtils.parse(queryDto.getCreateTimeStart());
                predicates.add(criteriaBuilder.greaterThanOrEqualTo(root.get("creatTime"), startTime));
            }
            if (queryDto.getCreateTimeEnd() != null && !queryDto.getCreateTimeEnd().isEmpty()) {
                LocalDateTime endTime = DateUtils.parse(queryDto.getCreateTimeEnd());
                predicates.add(criteriaBuilder.lessThanOrEqualTo(root.get("creatTime"), endTime));
            }

            // 排除已过期策略
            if (!queryDto.getIncludeExpired()) {
                predicates.add(criteriaBuilder.greaterThan(root.get("creatTime"), LocalDateTime.now()));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * 获取适用的策略
     * @param user 用户
     * @param evaluationDto 评估参数
     * @return 适用的策略列表
     */
    private List<Policies> getApplicablePolicies(User user, PolicyEvaluationDto evaluationDto) {
        List<Policies> applicablePolicies = new ArrayList<>();

        // ❌ 删除：用户不应该直接关联策略，应该通过角色获取（符合RBAC原则）
        // 正确的策略获取路径：User -> Role -> Policies

        // 获取用户角色关联的策略
        user.getRoles().forEach(role ->
            applicablePolicies.addAll(role.getPolicies()));

        // 如果指定了应用ID，还要获取应用相关的策略
        if (evaluationDto.getAppId() != null) {
            // 通过策略仓库查找与应用相关的策略
            List<Policies> appPolicies = policiesRepository.findAll().stream()
                .filter(policy -> policy.getApps() != null &&
                    policy.getApps().stream().anyMatch(app -> app.getAppId().equals(evaluationDto.getAppId())))
                .collect(Collectors.toList());
            applicablePolicies.addAll(appPolicies);
        }

        // 去重并过滤有效策略
        return applicablePolicies.stream()
            .distinct()
            .filter(policy -> policy.getDelFlag() == 0) // 只考虑启用的策略
            .filter(policy -> isEffective(policy)) // 只考虑在有效期内的策略
            .collect(Collectors.toList());
    }

    /**
     * 判断策略是否匹配
     * @param policyDto 策略DTO
     * @param evaluationDto 评估参数
     * @return 是否匹配
     */
    private boolean matchesPolicy(PoliciesDto policyDto, PolicyEvaluationDto evaluationDto) {
        // 检查资源匹配
        if (!matchesResource(policyDto.getResources(), evaluationDto.getResource())) {
            return false;
        }

        // 检查操作匹配
        if (!matchesAction(policyDto.getActions(), evaluationDto.getAction())) {
            return false;
        }

        // 检查条件匹配
        return matchesConditions(policyDto.getConditions(), evaluationDto);
    }

    /**
     * 判断策略是否在有效期内
     * @param policy 策略
     * @return 是否有效
     */
    private boolean isEffective(Policies policy) {
        LocalDateTime now = LocalDateTime.now();
        PoliciesDto policyDto = new PoliciesDto(policy);

        LocalDateTime effectiveTime = policyDto.getEffectiveTime();
        LocalDateTime expireTime = policyDto.getExpireTime();

        return (effectiveTime == null || now.isAfter(effectiveTime)) &&
               (expireTime == null || now.isBefore(expireTime));
    }

    /**
     * 判断资源是否匹配
     * @param policyResources 策略资源列表
     * @param requestResource 请求资源
     * @return 是否匹配
     */
    private boolean matchesResource(List<String> policyResources, String requestResource) {
        if (policyResources == null || policyResources.isEmpty()) {
            return true; // 空资源列表表示匹配所有资源
        }

        return policyResources.stream()
            .anyMatch(resource -> {
                if (resource.endsWith("/**")) {
                    String basePath = resource.substring(0, resource.length() - 3);
                    return requestResource.startsWith(basePath);
                } else if (resource.endsWith("/*")) {
                    String basePath = resource.substring(0, resource.length() - 2);
                    return requestResource.startsWith(basePath) &&
                           requestResource.substring(basePath.length()).indexOf('/') == -1;
                } else {
                    return resource.equals(requestResource);
                }
            });
    }

    /**
     * 判断操作是否匹配
     * @param policyActions 策略操作列表
     * @param requestAction 请求操作
     * @return 是否匹配
     */
    private boolean matchesAction(List<String> policyActions, String requestAction) {
        if (policyActions == null || policyActions.isEmpty()) {
            return true; // 空操作列表表示匹配所有操作
        }

        return policyActions.contains(requestAction) || policyActions.contains("*");
    }

    /**
     * 判断条件是否匹配（增强版本，支持完整的ABAC模型）
     * @param policyConditions 策略条件
     * @param evaluationDto 评估参数
     * @return 是否匹配
     */
    private boolean matchesConditions(Map<String, Object> policyConditions, PolicyEvaluationDto evaluationDto) {
        if (policyConditions == null || policyConditions.isEmpty()) {
            return true; // 空条件表示匹配所有情况
        }

        try {
            // 获取用户信息用于属性验证
            User user = userRepository.findById(evaluationDto.getUserId()).orElse(null);
            if (user == null) {
                return false;
            }

            // 检查角色条件（RBAC部分）
            if (policyConditions.containsKey("roles")) {
                @SuppressWarnings("unchecked")
                List<String> requiredRoles = (List<String>) policyConditions.get("roles");
                if (!PolicyUtils.matchesRoleConditions(requiredRoles, user)) {
                    return false;
                }
            }

            // 检查用户属性条件（ABAC - 主体属性）
            if (policyConditions.containsKey("userAttributes")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> userAttributes = (Map<String, Object>) policyConditions.get("userAttributes");
                if (!PolicyUtils.matchesUserAttributes(userAttributes, user)) {
                    return false;
                }
            }

            // 检查资源属性条件（ABAC - 客体属性）
            if (policyConditions.containsKey("resourceAttributes")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> resourceAttributes = (Map<String, Object>) policyConditions.get("resourceAttributes");
                if (!PolicyUtils.matchesResourceAttributes(resourceAttributes, evaluationDto.getResource())) {
                    return false;
                }
            }

            // 检查环境属性条件（ABAC - 环境属性）
            if (policyConditions.containsKey("environment")) {
                @SuppressWarnings("unchecked")
                Map<String, Object> environmentAttributes = (Map<String, Object>) policyConditions.get("environment");
                if (!PolicyUtils.matchesEnvironmentAttributes(environmentAttributes, evaluationDto.getContext())) {
                    return false;
                }
            }

            // 检查时间范围条件
            if (policyConditions.containsKey("timeRange")) {
                @SuppressWarnings("unchecked")
                Map<String, String> timeRange = (Map<String, String>) policyConditions.get("timeRange");
                if (!PolicyUtils.matchesTimeRange(timeRange)) {
                    return false;
                }
            }

            // 检查IP白名单条件
            if (policyConditions.containsKey("ipWhitelist") && evaluationDto.getContext() != null) {
                @SuppressWarnings("unchecked")
                List<String> ipWhitelist = (List<String>) policyConditions.get("ipWhitelist");
                String clientIp = (String) evaluationDto.getContext().get("ip");
                if (!PolicyUtils.matchesIpWhitelist(ipWhitelist, clientIp)) {
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            logger.error("策略条件匹配失败", e);
            return false;
        }
    }

    // ✅ 策略条件匹配相关方法已重构至PolicyUtils工具类
    // 提高了代码复用性和维护性
}
