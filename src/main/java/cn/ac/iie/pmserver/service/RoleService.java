package cn.ac.iie.pmserver.service;

import cn.ac.iie.pmserver.dto.role.RoleAppDTO;
import cn.ac.iie.pmserver.dto.role.RoleDto;
import cn.ac.iie.pmserver.dto.role.RoleEditDto;
import cn.ac.iie.pmserver.dto.role.*;
import cn.ac.iie.pmserver.exception.ResourceNotFoundException;
import cn.ac.iie.pmserver.model.*;
import cn.ac.iie.pmserver.repository.*;
import cn.ac.iie.pmserver.specification.RoleSpecification;
import cn.ac.iie.pmserver.utils.LogUtils;
import io.micrometer.common.util.StringUtils;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import jakarta.persistence.criteria.Predicate;
import lombok.RequiredArgsConstructor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.hibernate.Hibernate;
import org.springframework.data.domain.PageImpl;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.slf4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;
import cn.ac.iie.pmserver.model.Module;
import org.springframework.web.multipart.MultipartFile;

/**
 * 角色服务类
 * 提供角色相关的所有业务操作
 */
@Service
public class RoleService {
    private static final Logger logger = LogUtils.get(RoleService.class);

    private final UserRepository userRepository;
    private final RoleRepository roleRepository;

    private final ImportRecordRepository importRecordRepository;
    private final ImportDetailRepository importDetailRepository;
    private final AuthorityRepository authorityRepository;
    private final AppRepository appRepository;
    private final ModuleRepository moduleRepository;

    public RoleService(UserRepository userRepository, RoleRepository roleRepository,
                       AuthorityRepository authorityRepository, AppRepository appRepository,
                       ModuleRepository moduleRepository,ImportRecordRepository importRecordRepository,
                       ImportDetailRepository importDetailRepository) {
        this.userRepository = userRepository;
        this.roleRepository = roleRepository;
        this.authorityRepository = authorityRepository;
        this.appRepository = appRepository;
        this.moduleRepository = moduleRepository;
        this.importRecordRepository = importRecordRepository;
        this.importDetailRepository = importDetailRepository;
    }

    // ==================== 基础CRUD操作 ====================
    /**
     * 根据角色ID获取角色信息
     * @param id 角色ID
     * @return 角色对象
     */
    public Role getRoleById(Long id) {
        return roleRepository.findById(id).orElse(null);
    }

    /**
     * 获取所有角色
     * @return 角色列表
     */
    public List<Role> getAllRole() {
        return roleRepository.findAll();
    }

    /**
     * 删除角色信息
     * @param ids 角色ID集合
     * @return 删除结果
     */
    @Transactional
    public int delRoles(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }
        try {
            roleRepository.deleteAllById(ids);
            logger.info("删除角色信息: {}条", ids.size());
            return 1;
        } catch (Exception e) {
            logger.error("删除角色信息失败", e);
            throw new RuntimeException("删除角色信息失败", e);
        }
    }

    /**
     * 新增角色
     * @param roleEditDto 角色编辑DTO
     * @return 保存后的角色对象
     */
    @Transactional
    public Role addRole(RoleEditDto roleEditDto) {
        // 创建角色对象并设置基本信息
        Role role = new Role();
        role.setRoleName(roleEditDto.getRoleName());
        role.setRoleDescription(roleEditDto.getRoleDescription());
        role.setDelFlag(roleEditDto.getDelFlag());
        
        // 设置创建和更新信息
        LocalDateTime now = LocalDateTime.now();
        role.setCreatTime(now);
        role.setUpdateTime(now);
        role.setCreatUser("system"); // 这里应该从当前登录用户中获取
        role.setUpdateUser("system"); // 这里应该从当前登录用户中获取

        // 先保存角色基本信息
        role = roleRepository.save(role);

        // 处理应用关系
        if (roleEditDto.getAppIds() != null && !roleEditDto.getAppIds().isEmpty()) {
            Set<App> apps = roleEditDto.getAppIds().stream()
                    .map(appId -> appRepository.findById(appId)
                            .orElseThrow(() -> new ResourceNotFoundException("未找到该id的应用: " + appId)))
                    .filter(app -> app.getDelFlag() == 0)
                    .collect(Collectors.toSet());

            if (apps.size() != roleEditDto.getAppIds().size()) {
                throw new ResourceNotFoundException("部分应用未找到或不可用");
            }

            // 在App端添加Role
            for (App app : apps) {
                app.getRoles().add(role);
                appRepository.save(app);
            }
        }

        return roleRepository.findByIdWithAllRelations(role.getRoleId())
                .orElseThrow(() -> new ResourceNotFoundException("创建角色后未能成功检索"));
    }

    /**
     * 修改角色信息
     * @param roleId 角色ID
     * @param roleEditDto 角色编辑DTO
     * @return 修改结果
     */
    @Transactional
    public int updateRole(Long roleId, RoleEditDto roleEditDto) {
        try {
            Role role = roleRepository.findByIdWithAllRelations(roleId)
                    .orElseThrow(() -> new ResourceNotFoundException("未找到该id的角色: " + roleId));

            // 更新基本信息
            role.setRoleName(roleEditDto.getRoleName());
            role.setRoleDescription(roleEditDto.getRoleDescription());
            role.setDelFlag(roleEditDto.getDelFlag());
            
            // 更新修改信息
            role.setUpdateTime(LocalDateTime.now());
            role.setUpdateUser("system"); // 这里应该从当前登录用户中获取

            // 先保存角色基本信息
            role = roleRepository.save(role);

            // 处理应用关系
            if (roleEditDto.getAppIds() != null) {
                // 获取当前角色关联的所有应用
                Set<App> currentApps = role.getApps();
                
                // 获取新的应用集合
                Set<App> newApps = roleEditDto.getAppIds().stream()
                        .map(appId -> appRepository.findById(appId)
                                .orElseThrow(() -> new ResourceNotFoundException("未找到该id的应用: " + appId)))
                        .filter(app -> app.getDelFlag() == 0)
                        .collect(Collectors.toSet());

                if (newApps.size() != roleEditDto.getAppIds().size()) {
                    throw new ResourceNotFoundException("部分应用未找到或不可用");
                }

                // 移除不再关联的应用
                for (App app : currentApps) {
                    if (!newApps.contains(app)) {
                        app.getRoles().remove(role);
                        appRepository.save(app);
                    }
                }

                // 添加新关联的应用
                for (App app : newApps) {
                    if (!currentApps.contains(app)) {
                        app.getRoles().add(role);
                        appRepository.save(app);
                    }
                }
            }

            logger.info("修改角色信息: {}", role.getRoleName());
            return 1;
        } catch (Exception e) {
            logger.error("修改角色信息失败", e);
            throw new RuntimeException("修改角色信息失败", e);
        }
    }

    // ==================== 查询操作 ====================

    /**
     * 查询应用下所有角色
     */
    @Transactional(readOnly = true)
    public List<RoleAppDTO> getRolesByAppId(Long appId) {
        logger.info("查询应用 {} 下的所有角色", appId);
        try {
            List<RoleAppDTO> roleAppDTOS = roleRepository.findRolesByAppId(appId);
            return roleAppDTOS;
        } catch (Exception e) {
            logger.error("查询应用 {} 下的所有角色失败", appId, e);
            throw new RuntimeException("查询应用 " + appId + " 下的所有角色失败", e);
        }
    }

    @Transactional(readOnly = true)
    public List<RoleSimpleDto> getAllRoles() {
        return roleRepository.findAll().stream()
                .map(RoleSimpleDto::new)
                .collect(Collectors.toList());
    }

    @Transactional(readOnly = true)
    public Map<Long, Map<String, Object>> getAppRoleStatistics() {
        // 获取所有应用
        List<App> apps = appRepository.findAll();

        // 创建结果Map，key为应用ID，value为包含应用名称和角色数量的Map
        Map<Long, Map<String, Object>> result = new LinkedHashMap<>();

        for (App app : apps) {
            Map<String, Object> appInfo = new HashMap<>();
            appInfo.put("appName", app.getAppName());
            // 统计该应用下的角色数量
            appInfo.put("roleCount", app.getRoles().size());
            appInfo.put("delFlag", app.getDelFlag());

            result.put(app.getAppId(), appInfo);
        }

        return result;
    }

    /**
     * 分页条件查询角色
     * @param keyword 关键字
     * @param delFlag 删除标志
     * @param appId 应用ID
     * @param pageable 分页参数
     * @return 角色分页结果
     */
    @Transactional(readOnly = true)
    public Page<RoleListDto> searchRoles(String keyword, Integer delFlag, Long appId, Pageable pageable) {
        // 使用Specification构建查询条件
        Specification<Role> spec = (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 添加关键字查询条件
            if (StringUtils.isNotBlank(keyword)) {
                predicates.add(cb.or(
                        cb.like(root.get("roleName"), "%" + keyword + "%"),
                        cb.like(root.get("roleDescription"), "%" + keyword + "%")
                ));
            }

            // 添加状态查询条件
            if (delFlag != null) {
                predicates.add(cb.equal(root.get("delFlag"), delFlag));
            }

            // 添加应用ID查询条件
            if (appId != null) {
                Join<Role, App> appJoin = root.join("apps", JoinType.LEFT);
                predicates.add(cb.equal(appJoin.get("appId"), appId));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };

        // 执行查询
        Page<Role> rolePage = roleRepository.findAll(spec, pageable);

        // 转换为DTO
        List<RoleListDto> dtos = rolePage.getContent().stream()
                .map(role -> {
                    // 计算权限数量
                    int authorityCount = 0;
                    if (appId != null) {
                        authorityCount = (int) role.getAuthorities().stream()
                                .filter(authority -> authorityBelongsToApp(authority, appId))
                                .count();
                    } else {
                        authorityCount = role.getAuthorities().size();
                    }

                    // 获取关联的应用（如果指定了appId则只返回该应用）
                    App app = null;
                    if (appId != null) {
                        app = role.getApps().stream()
                                .filter(a -> a.getAppId().equals(appId))
                                .findFirst()
                                .orElse(null);
                    } else if (!role.getApps().isEmpty()) {
                        app = role.getApps().iterator().next();
                    }

                    return new RoleListDto(role, app, authorityCount, 0); // policyCount设为0
                })
                .collect(Collectors.toList());

        return new PageImpl<>(dtos, pageable, rolePage.getTotalElements());
    }


    /**
     * 批量更新角色状态
     * @param roleIds 角色ID列表
     * @param status 状态值(0:启用,1:禁用)
     */
    @Transactional
    public void batchUpdateRoleStatus(List<Long> roleIds, Integer status) {
        List<Role> roles = roleRepository.findAllById(roleIds);
        if (roles.size() != roleIds.size()) {
            throw new RuntimeException("部分角色不存在");
        }

        roles.forEach(role -> {
            role.setDelFlag(status);
            role.setUpdateTime(LocalDateTime.now());
        });

        roleRepository.saveAll(roles);
    }

    /**
     * 获取角色详细信息
     * @param roleId 角色ID
     * @return 角色详细信息
     */
    @Transactional(readOnly = true)
    public RoleDto getRoleDetail(Long roleId) {
        Role role = roleRepository.findByIdWithAllRelations(roleId)
                .orElseThrow(() -> new ResourceNotFoundException("Role not found with id: " + roleId));
        return new RoleDto(role);
    }

    @Transactional(readOnly = true)
    public RoleAppDetailDto getRoleAppDetails(Long roleId, Long appId) {
        // 1. 查询当前角色 - 使用EntityGraph明确指定需要加载的关联字段
        Role role = roleRepository.findById(roleId)
                .orElseThrow(() -> new ResourceNotFoundException("角色不存在"));

        // 2. 查询应用
        App app = appRepository.findById(appId)
                .orElseThrow(() -> new ResourceNotFoundException("应用不存在"));

        // 3. 手动加载关联数据（避免N+1查询）
        Hibernate.initialize(role.getApps());
        Hibernate.initialize(role.getAuthorities());

        // 4. 验证角色是否关联了指定应用
        boolean hasApp = role.getApps().stream()
                .anyMatch(a -> a.getAppId().equals(appId));
        if (!hasApp) {
            throw new ResourceNotFoundException("该角色未关联指定应用");
        }

        // 5. 查询父角色名称（如果parentRoleId存在）
        String parentRoleName = null;
        if (role.getParentRoleCode() != null) {
            parentRoleName = roleRepository.findRoleNameByCode(role.getParentRoleCode()).orElse(null);
        }

        // 6. 返回DTO（传入父角色名称）
        return new RoleAppDetailDto(role, app, parentRoleName);
    }


    @Transactional(readOnly = true)
    public Map<String, Object> getRoleAppAuthorities(Long roleId, Long appId) {
        // 1. 获取角色信息及其关联的应用和权限
        Optional<Role> roleOpt = roleRepository.findByIdWithAuthoritiesAndApps(roleId);
        if (!roleOpt.isPresent()) {
            throw new RuntimeException("角色不存在");
        }
        Role role = roleOpt.get();

        // 2. 验证角色是否关联了指定应用
        boolean hasApp = role.getApps().stream()
                .anyMatch(app -> app.getAppId().equals(appId));
        if (!hasApp) {
            throw new RuntimeException("该角色未关联指定应用");
        }

        // 3. 获取应用信息
        App app = appRepository.findById(appId)
                .orElseThrow(() -> new RuntimeException("应用不存在"));

        // 4. 构建应用信息
        Map<String, Object> appInfo = buildAppInfo(app);

        // 5. 获取该角色在该应用下的所有权限
        List<Map<String, Object>> authorities = role.getAuthorities().stream()
                .filter(authority -> authorityBelongsToApp(authority, appId))
                .map(this::buildAuthorityInfo)
                .collect(Collectors.toList());

        // 6. 构建返回结果
        Map<String, Object> result = new LinkedHashMap<>();
        result.put("appInfo", appInfo);
        result.put("authorities", authorities);

        return result;
    }

    private Map<String, Object> buildAppInfo(App app) {
        Map<String, Object> appInfo = new LinkedHashMap<>();
        appInfo.put("appId", app.getAppId());
        appInfo.put("appName", app.getAppName());
//        appInfo.put("appUrl", app.getAppUrl());
        appInfo.put("appDescription", app.getAppDescription());
//        appInfo.put("appCallbackUrl", app.getAppCallbackUrl());
//        appInfo.put("appLogo", app.getAppLogo());
//        appInfo.put("status", app.getDelFlag() == 0 ? 1 : 0);
//        appInfo.put("aliveFlag", app.getAliveFlag());
        appInfo.put("creator", app.getCreatUser());
        appInfo.put("createTime", app.getCreatTime().toString());
        appInfo.put("updater", app.getUpdateUser());
        appInfo.put("updateTime", app.getUpdateTime() != null ? app.getUpdateTime().toString() : "");
//        appInfo.put("pointUserPhone", app.getPointUserPhone());
        return appInfo;
    }

    private boolean authorityBelongsToApp(Authority authority, Long appId) {
        return authority.getModules().stream()
                .anyMatch(module -> module.getApps().stream()
                        .anyMatch(app -> app.getAppId().equals(appId)));
    }

    private Map<String, Object> buildAuthorityInfo(Authority authority) {
        Map<String, Object> authInfo = new LinkedHashMap<>();
        authInfo.put("authorityId", authority.getAuthorityId());
        authInfo.put("authorityKey", authority.getAuthorityName().toLowerCase().replace(" ", ":"));
        authInfo.put("authorityName", authority.getAuthorityName());
        authInfo.put("authorityDescription", authority.getAuthorityDescription());
        authInfo.put("authorityType", "operation"); // 根据实际业务设置
        authInfo.put("scope", "global"); // 根据实际业务设置
        authInfo.put("actions", authority.getActions());
        authInfo.put("status", 1); // 假设都是启用的
        authInfo.put("creator", authority.getCreatUser());
        authInfo.put("createTime", authority.getCreatTime().toString());
        authInfo.put("updater", authority.getUpdateUser());
        authInfo.put("updateTime", authority.getUpdateTime() != null ? authority.getUpdateTime().toString() : "");

        // 获取拥有该权限的角色名称
        String roleNames = authority.getRoles().stream()
                .map(Role::getRoleName)
                .collect(Collectors.joining(","));
        authInfo.put("roleNames", roleNames);

        return authInfo;
    }

    @Transactional(readOnly = true)
    public List<UserSimpleDto> getRoleUsersInApp(Long roleId, Long appId) {
        return userRepository.findByRoles_RoleIdAndApps_AppId(roleId, appId).stream()
                .map(UserSimpleDto::new)
                .collect(Collectors.toList());
    }

    /**
     * 角色导入服务类
     */

        /**
         * 从Excel文件导入角色数据
         * @param file 上传的Excel文件
         * @param operator 操作人
         * @return 导入结果
         * @throws IOException 文件读取异常
         */
        @Transactional
        public Map<String, Object> importRolesFromFile(MultipartFile file, String operator) throws IOException {
            // 1. 解析Excel文件
            List<RoleImportDto> importData = parseRoleExcel(file);

            // 2. 创建导入记录
            ImportRecord record = createImportRecord(file.getOriginalFilename(), importData.size(), operator, ImportRecord.ImportMode.FULL);
            record = importRecordRepository.save(record);

            // 3. 处理导入数据
            ImportResult result = processImportData(importData, record, "ROLE_IMPORT");

            // 4. 更新导入记录状态
            updateImportRecord(record, result.successCount, result.failedCount, result.errorMessages);

            // 5. 保存导入详情
            importDetailRepository.saveAll(result.details);

            return buildResult(importData.size(), result.successCount, result.failedCount, record.getId());
        }

        /**
         * 同步角色数据
         * @param resourceCode 资源编码(应用编码)
         * @param roleList 角色数据列表
         * @param operator 操作人
         * @return 同步结果
         */
        @Transactional(propagation = Propagation.REQUIRES_NEW)
        public Map<String, Object> syncRoles(String resourceCode, List<Map<String, String>> roleList, String operator) {
            // 1. 创建导入记录
            ImportRecord record = createImportRecord("API_SYNC", roleList.size(), operator, ImportRecord.ImportMode.INCREMENTAL);
            record = importRecordRepository.save(record);

            // 2. 处理同步数据
            ImportResult result = processSyncData(resourceCode, roleList, record);

            // 3. 更新导入记录状态
            updateImportRecord(record, result.successCount, result.failedCount, result.errorMessages);

            // 4. 保存导入详情
            importDetailRepository.saveAll(result.details);

            return buildResult(roleList.size(), result.successCount, result.failedCount, record.getId());
        }

        /**
         * 解析Excel文件中的角色数据
         */
        private List<RoleImportDto> parseRoleExcel(MultipartFile file) throws IOException {
            List<RoleImportDto> dtos = new ArrayList<>();
            try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
                Sheet sheet = workbook.getSheetAt(0);

                for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                    Row row = sheet.getRow(i);
                    if (row == null) continue;

                    RoleImportDto dto = new RoleImportDto();
                    dto.setRoleName(getCellStringValue(row.getCell(0)));
                    dto.setRoleCode(getCellStringValue(row.getCell(1)));
                    dto.setRoleDescription(getCellStringValue(row.getCell(2)));
                    dto.setDelFlag(getCellNumericValue(row.getCell(3)));
                    dto.setParentRoleCode(getCellStringValue(row.getCell(4)));
                    dto.setRoleLevel(getCellNumericValue(row.getCell(5)));
                    dto.setAppCode(getCellStringValue(row.getCell(6)));

                    // 解析子角色编码
                    String childRolesStr = getCellStringValue(row.getCell(7));
                    if (StringUtils.isNotBlank(childRolesStr)) {
                        dto.setChildRoleCodes(Arrays.asList(childRolesStr.split("[,，]")));
                    }

                    dtos.add(dto);
                }
            }
            return dtos;
        }

        /**
         * 处理导入数据
         */
        private ImportResult processImportData(List<RoleImportDto> importData, ImportRecord record, String recordKey) {
            List<ImportDetail> details = new ArrayList<>();
            int successCount = 0;
            int failedCount = 0;
            List<String> errorMessages = new ArrayList<>();

            for (int i = 0; i < importData.size(); i++) {
                RoleImportDto dto = importData.get(i);
                ImportDetail detail = createImportDetail(record, recordKey);

                try {
                    processRoleImport(dto, detail);
                    successCount++;
                    detail.setIsSuccess(true);
                } catch (Exception e) {
                    failedCount++;
                    detail.setIsSuccess(false);
                    String errorMsg = "第" + (i+1) + "行: " + e.getMessage();
                    detail.setErrorMessage(errorMsg);
                    errorMessages.add(errorMsg);
                    logger.error("角色导入失败: {}", errorMsg, e);
                }
                details.add(detail);
            }

            return new ImportResult(details, successCount, failedCount, errorMessages);
        }

        /**
         * 处理同步数据
         */
        private ImportResult processSyncData(String resourceCode, List<Map<String, String>> roleList, ImportRecord record) {
            List<ImportDetail> details = new ArrayList<>();
            int successCount = 0;
            int failedCount = 0;
            List<String> errorMessages = new ArrayList<>();

            // 第一遍：先创建所有角色基本数据
            List<RoleImportDto> dtos = new ArrayList<>();
            for (int i = 0; i < roleList.size(); i++) {
                Map<String, String> roleData = roleList.get(i);
                try {
                    RoleImportDto dto = convertToRoleImportDto(roleData, resourceCode);
                    dtos.add(dto);

                    // 只创建基本角色信息，不处理关系
                    ImportDetail detail = createImportDetail(record, "ROLE_SYNC");
                    processBasicRole(dto, detail);
                    details.add(detail);
                    successCount++;
                } catch (Exception e) {
                    failedCount++;
                    String errorMsg = "第" + (i+1) + "条: " + e.getMessage();
                    errorMessages.add(errorMsg);
                    logger.error("角色同步失败(基础信息): {}", errorMsg, e);
                }
            }

            // 第二遍：处理角色关系 - 修改为只添加新关系
            for (int i = 0; i < dtos.size(); i++) {
                RoleImportDto dto = dtos.get(i);
                try {
                    // 只处理新增关系，不删除现有关系
                    ImportDetail detail = details.get(i);
                    if (detail.getIsSuccess()) {
                        addNewRelationsOnly(dto, detail); // 新增方法，只处理新增关系
                        roleRepository.flush();
                    }
                } catch (Exception e) {
                    // 错误处理逻辑保持不变
                    ImportDetail detail = details.get(i);
                    detail.setIsSuccess(false);
                    String errorMsg = "第" + (i+1) + "条(关系处理): " + e.getMessage();
                    detail.setErrorMessage(detail.getErrorMessage() != null ?
                            detail.getErrorMessage() + "; " + errorMsg : errorMsg);
                    errorMessages.add(errorMsg);
                    logger.error("角色关系处理失败: {}", errorMsg, e);
                    successCount--;
                    failedCount++;
                }
            }

            return new ImportResult(details, successCount, failedCount, errorMessages);
        }

    // 只处理新增关系，不删除现有关系
    private void addNewRelationsOnly(RoleImportDto dto, ImportDetail detail) {
        // 1. 获取角色
        Role role = roleRepository.findByRoleCode(dto.getRoleCode())
                .orElseThrow(() -> new RuntimeException("角色不存在: " + dto.getRoleCode()));

        // 2. 处理应用关联
        if (StringUtils.isNotBlank(dto.getAppCode())) {
            App app = appRepository.findByAppName(dto.getAppCode())
                    .orElseThrow(() -> new RuntimeException("应用不存在: " + dto.getAppCode()));

            // 只添加新关系，不删除现有关系
            if (!role.getApps().contains(app)) {
                role.getApps().add(app);
                app.getRoles().add(role);
                appRepository.save(app);
                roleRepository.save(role);
            }
        }

        // 3. 处理父角色关系
        if (StringUtils.isNotBlank(dto.getParentRoleCode())) {
            Role parentRole = roleRepository.findByRoleCode(dto.getParentRoleCode())
                    .orElseThrow(() -> new RuntimeException("父角色不存在: " + dto.getParentRoleCode()));

            // 只设置父角色关系，不删除现有关系
            if (role.getParentRole() == null || !role.getParentRole().equals(parentRole)) {
                role.setParentRole(parentRole);
                role.setParentRoleCode(parentRole.getRoleCode());
                roleRepository.save(role);
            }
        }

        // 4. 处理子角色关系
        if (dto.getChildRoleCodes() != null && !dto.getChildRoleCodes().isEmpty()) {
            for (String childRoleCode : dto.getChildRoleCodes()) {
                Role childRole = roleRepository.findByRoleCode(childRoleCode.trim())
                        .orElseThrow(() -> new RuntimeException("子角色不存在: " + childRoleCode));

                // 只添加新子角色关系，不删除现有关系
                if (!role.getChildren().contains(childRole)) {
                    role.getChildren().add(childRole);
                    childRole.setParentRole(role);
                    childRole.setParentRoleCode(role.getRoleCode());
                    roleRepository.save(childRole);
                }
            }
            roleRepository.save(role);
        }
    }

    private void processBasicRole(RoleImportDto dto, ImportDetail detail) {
        validateRequiredFields(dto);
        App app = appRepository.findByAppName(dto.getAppCode())
                .orElseThrow(() -> new RuntimeException("应用不存在: " + dto.getAppCode()));

        Optional<Role> existingRole = roleRepository.findByRoleCode(dto.getRoleCode());
        if (existingRole.isPresent()) {
            Role role = updateExistingRole(existingRole.get(), dto, app);
            roleRepository.save(role);
            detail.setOperationType(ImportDetail.OperationType.UPDATE);
        } else {
            Role role = createNewRoleWithoutRelations(dto, app);
            roleRepository.save(role);
            detail.setOperationType(ImportDetail.OperationType.CREATE);
        }
        detail.setIsSuccess(true);
    }

    private Role createNewRoleWithoutRelations(RoleImportDto dto, App app) {
        Role role = new Role();
        role.setRoleCode(dto.getRoleCode());
        role.setRoleName(dto.getRoleName());
        role.setRoleDescription(dto.getRoleDescription());
        role.setRoleLevel(dto.getRoleLevel() != null ? dto.getRoleLevel() : 10);
        role.setSourceSystem("BUSINESS_SYSTEM");
        role.setResourceCode(dto.getAppCode());
        role.setDelFlag(dto.getDelFlag() != null ? dto.getDelFlag() : 0);

        LocalDateTime now = LocalDateTime.now();
        role.setCreatTime(now);
        role.setUpdateTime(now);
        role.setCreatUser("SYSTEM_IMPORT");
        role.setUpdateUser("SYSTEM_IMPORT");

        role.getApps().add(app);
        app.getRoles().add(role);
        return role;
    }

    private void processRoleRelations(RoleImportDto dto, ImportDetail detail) {
        Role role = roleRepository.findByRoleCode(dto.getRoleCode())
                .orElseThrow(() -> new RuntimeException("角色不存在: " + dto.getRoleCode()));
        App app = appRepository.findByAppName(dto.getAppCode())
                .orElseThrow(() -> new RuntimeException("应用不存在: " + dto.getAppCode()));

        // 处理父角色关系
        if (StringUtils.isNotBlank(dto.getParentRoleCode())) {
            Role parentRole = roleRepository.findByRoleCodeAndAppName(dto.getParentRoleCode(), dto.getAppCode())
                    .orElseThrow(() -> new RuntimeException("父角色不存在: " + dto.getParentRoleCode()));

            ensureRoleAppAssociation(parentRole, app);
            role.setParentRole(parentRole);
            role.setParentRoleCode(parentRole.getRoleCode());
        }

        // 处理子角色关系
        if (dto.getChildRoleCodes() != null && !dto.getChildRoleCodes().isEmpty()) {
            processChildRoles(role, dto.getChildRoleCodes(), dto.getAppCode());
        }

        ensureRoleAppAssociation(role, app);
        roleRepository.save(role);
    }

        /**
         * 处理单个角色导入
         */
        private void processRoleImport(RoleImportDto dto, ImportDetail detail) {
            // 1. 验证必填字段
            validateRequiredFields(dto);

            // 2. 查找关联应用
            App app = appRepository.findByAppName(dto.getAppCode())
                    .orElseThrow(() -> new RuntimeException("应用不存在: " + dto.getAppCode()));

            // 3. 创建/更新角色 - 先保存角色本身
            Optional<Role> existingRole = roleRepository.findByRoleCode(dto.getRoleCode());
            Role role;
            if (existingRole.isPresent()) {
                role = updateExistingRole(existingRole.get(), dto, app);
                detail.setOperationType(ImportDetail.OperationType.UPDATE);
            } else {
                role = createNewRole(dto, app);
                detail.setOperationType(ImportDetail.OperationType.CREATE);
            }

            // 先保存角色基本信息
            role = roleRepository.save(role);

            // 4. 处理父角色关系
            if (StringUtils.isNotBlank(dto.getParentRoleCode())) {
                Role parentRole = roleRepository.findByRoleCodeAndAppName(dto.getParentRoleCode(), dto.getAppCode())
                        .orElseThrow(() -> new RuntimeException("父角色不存在: " + dto.getParentRoleCode()));

                // 确保父角色与应用有关联
                ensureRoleAppAssociation(parentRole, app);

                role.setParentRole(parentRole);
                role.setParentRoleCode(parentRole.getRoleCode());
            }

            // 5. 处理子角色关系
            if (dto.getChildRoleCodes() != null && !dto.getChildRoleCodes().isEmpty()) {
                processChildRoles(role, dto.getChildRoleCodes(), dto.getAppCode());
            }

            // 6. 确保角色与应用有关联
            ensureRoleAppAssociation(role, app);

            // 最后保存完整的关系
            roleRepository.save(role);
        }

        /**
         * 确保角色与应用有关联关系
         */
        private void ensureRoleAppAssociation(Role role, App app) {
            if (!role.getApps().contains(app)) {
                role.getApps().add(app);
                app.getRoles().add(role);
                roleRepository.save(role);
            }
        }

        /**
         * 处理子角色关系
         */
        private void processChildRoles(Role parentRole, List<String> childRoleCodes, String appCode) {
            // 只添加新的子角色关系，不删除现有关系
            for (String childRoleCode : childRoleCodes) {
                Role childRole = roleRepository.findByRoleCodeAndAppName(childRoleCode.trim(), appCode)
                        .orElseThrow(() -> new RuntimeException("子角色不存在: " + childRoleCode));

                // 确保子角色与应用有关联
                App app = appRepository.findByAppName(appCode)
                        .orElseThrow(() -> new RuntimeException("应用不存在: " + appCode));
                ensureRoleAppAssociation(childRole, app);

                // 只添加不存在的子角色关系
                if (!parentRole.getChildren().contains(childRole)) {
                    parentRole.addChild(childRole);
                }
            }
        }

        /**
         * 创建新角色
         */
        private Role createNewRole(RoleImportDto dto, App app) {
            Role role = new Role();
            role.setRoleCode(dto.getRoleCode());
            role.setRoleName(dto.getRoleName());
            role.setRoleDescription(dto.getRoleDescription());
            role.setRoleLevel(dto.getRoleLevel() != null ? dto.getRoleLevel() : 10);
            role.setSourceSystem("BUSINESS_SYSTEM");
            role.setResourceCode(dto.getAppCode());
            role.setDelFlag(dto.getDelFlag() != null ? dto.getDelFlag() : 0);

            // 设置创建信息
            LocalDateTime now = LocalDateTime.now();
            role.setCreatTime(now);
            role.setUpdateTime(now);
            role.setCreatUser("SYSTEM_IMPORT");
            role.setUpdateUser("SYSTEM_IMPORT");

            // 关联应用
            role.getApps().add(app);
            app.getRoles().add(role);
            return role;
        }

        /**
         * 更新现有角色
         */
        private Role updateExistingRole(Role role, RoleImportDto dto, App app) {
            role.setRoleName(dto.getRoleName());
            role.setRoleDescription(dto.getRoleDescription());
            if (dto.getRoleLevel() != null) {
                role.setRoleLevel(dto.getRoleLevel());
            }
            if (dto.getDelFlag() != null) {
                role.setDelFlag(dto.getDelFlag());
            }

            // 设置更新信息
            role.setUpdateTime(LocalDateTime.now());
            role.setUpdateUser("SYSTEM_IMPORT");

            return role;
        }

        /**
         * 验证必填字段
         */
        private void validateRequiredFields(RoleImportDto dto) {
            if (StringUtils.isBlank(dto.getRoleCode())) throw new RuntimeException("角色编码不能为空");
            if (StringUtils.isBlank(dto.getRoleName())) throw new RuntimeException("角色名称不能为空");
            if (StringUtils.isBlank(dto.getAppCode())) throw new RuntimeException("应用编码不能为空");
            if (dto.getRoleLevel() == null) dto.setRoleLevel(1); // 设置默认层级
        }

        /**
         * 创建导入记录
         */
        private ImportRecord createImportRecord(String fileName, int totalCount, String operator, ImportRecord.ImportMode mode) {
            return new ImportRecord(
                    fileName,
                    "ROLE",
                    totalCount,
                    0,
                    0,
                    mode,
                    operator,
                    LocalDateTime.now(),
                    LocalDateTime.now(),
                    ImportRecord.ImportStatus.PROCESSING,
                    "无错误信息",
                    "BUSINESS_SYSTEM"
            );
        }

        /**
         * 创建导入详情
         */
        private ImportDetail createImportDetail(ImportRecord record, String recordKey) {
            ImportDetail detail = new ImportDetail();
            detail.setRecordKey(recordKey);
            detail.setImportRecord(record);
            detail.setProcessTime(LocalDateTime.now());
            return detail;
        }

        /**
         * 将Map数据转换为RoleImportDto
         */
        private RoleImportDto convertToRoleImportDto(Map<String, String> roleData, String resourceCode) {
            RoleImportDto dto = new RoleImportDto();
            dto.setRoleName(roleData.get("roleName"));
            dto.setRoleCode(roleData.get("roleCode"));
            dto.setRoleDescription(roleData.get("roleDescription"));
            dto.setDelFlag(Integer.parseInt(roleData.getOrDefault("delFlag", "0")));
            dto.setParentRoleCode(roleData.get("parentRoleCode"));
            dto.setRoleLevel(roleData.containsKey("roleLevel") ? Integer.parseInt(roleData.get("roleLevel")) : 1);
            dto.setAppCode(resourceCode);
            return dto;
        }

        /**
         * 更新导入记录
         */
        private void updateImportRecord(ImportRecord record, int successCount, int failedCount, List<String> errorMessages) {
            record.setSuccessCount(successCount);
            record.setFailedCount(failedCount);
            record.setEndTime(LocalDateTime.now());

            if (failedCount > 0) {
                record.setStatus(ImportRecord.ImportStatus.PARTIAL);
                record.setErrorSummary(String.join("; ", errorMessages));
            } else {
                record.setStatus(ImportRecord.ImportStatus.SUCCESS);
                record.setErrorSummary("无错误信息");
            }
        }

        /**
         * 构建返回结果
         */
        private Map<String, Object> buildResult(int total, int success, int failed, Long recordId) {
            Map<String, Object> result = new HashMap<>();
            result.put("total", total);
            result.put("success", success);
            result.put("failed", failed);
            result.put("recordId", recordId);
            result.put("status", failed > 0 ? "部分成功" : "全部成功");
            return result;
        }

        /**
         * 获取单元格字符串值
         */
        private String getCellStringValue(Cell cell) {
            if (cell == null) {
                return null;
            }
            switch (cell.getCellType()) {
                case STRING:
                    return cell.getStringCellValue();
                case NUMERIC:
                    return String.valueOf((long) cell.getNumericCellValue());
                case BOOLEAN:
                    return String.valueOf(cell.getBooleanCellValue());
                case FORMULA:
                    return cell.getCellFormula();
                default:
                    return null;
            }
        }

        /**
         * 获取单元格数值
         */
        private Integer getCellNumericValue(Cell cell) {
            if (cell == null) {
                return null;
            }
            if (cell.getCellType() == CellType.NUMERIC) {
                return (int) cell.getNumericCellValue();
            }
            return null;
        }

        /**
         * 导入结果内部类
         */
        private static class ImportResult {
            List<ImportDetail> details;
            int successCount;
            int failedCount;
            List<String> errorMessages;

            public ImportResult(List<ImportDetail> details, int successCount, int failedCount, List<String> errorMessages) {
                this.details = details;
                this.successCount = successCount;
                this.failedCount = failedCount;
                this.errorMessages = errorMessages;
            }
        }
}