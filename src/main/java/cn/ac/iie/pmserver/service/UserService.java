package cn.ac.iie.pmserver.service;

import cn.ac.iie.pmserver.dto.user.*;
import cn.ac.iie.pmserver.exception.ResourceNotFoundException;
import cn.ac.iie.pmserver.model.*;
import cn.ac.iie.pmserver.repository.UserRepository;
import cn.ac.iie.pmserver.repository.*;
import cn.ac.iie.pmserver.specification.UserSpecification;
import cn.ac.iie.pmserver.utils.LogUtils;
import io.micrometer.common.util.StringUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.usermodel.*;
import org.springframework.transaction.annotation.Transactional;

import org.slf4j.Logger;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 用户服务类
 * 提供用户相关的所有业务操作
 */
@Service
public class UserService {

    private final UserRepository userRepository;

    private final RoleRepository roleRepository;

    private final DepartmentRepository departmentRepository;
    private final ImportRecordRepository importRecordRepository;
    private final ImportDetailRepository importDetailRepository;

    private final AppRepository appRepository;

    public UserService(UserRepository userRepository,ImportRecordRepository importRecordRepository,
                       ImportDetailRepository importDetailRepository, AppRepository appRepository,
                       DepartmentRepository departmentRepository, RoleRepository roleRepository) {
        this.userRepository = userRepository;
        this.importRecordRepository = importRecordRepository;
        this.importDetailRepository = importDetailRepository;
        this.appRepository = appRepository;
        this.departmentRepository = departmentRepository;
        this.roleRepository = roleRepository;
    }

    private static final Logger logger = LogUtils.get(UserService.class);

    // ==================== 基础CRUD操作 ====================

    /**
     * 新增用户
     */
    public User addUser(User user) {
        return userRepository.save(user);
    }

    /**
     * 根据用户ID获取用户信息
     */
    public User getUserById(Long id) {
        return userRepository.findById(id).orElse(null);
    }

    /**
     * 获取所有用户
     */
    public List<User> getAllUser() {
        return userRepository.findAll();
    }

    /**
     * 删除用户信息
     */
    @Transactional
    public int delUsers(List<Long> ids) {
        if (ids == null || ids.isEmpty()) {
            return 0;
        }
        try {
            userRepository.deleteAllById(ids);
            logger.info("成功删除用户信息，数量：{}条", ids.size());
            return 1;
        } catch (Exception e) {
            logger.error("删除用户信息时发生异常", e);
            throw new RuntimeException("删除用户信息失败，原因：" + e.getMessage());
        }
    }

    /**
     * 修改用户信息
     */
    @Transactional
    public int updateUser(User user) {
        if (user == null) {
            return 0;
        }
        try {
            if (getUserById(user.getUserId()) != null) {
                userRepository.save(user);
                logger.info("成功修改用户信息，用户名：{}", user.getName());
                return 1;
            } else {
                logger.warn("尝试修改不存在的用户，用户ID：{}", user.getUserId());
                return 0;
            }
        } catch (Exception e) {
            logger.error("修改用户信息时发生异常", e);
            throw new RuntimeException("修改用户信息失败，原因：" + e.getMessage());
        }
    }

    @Transactional
    public void updateUserBasicInfo(Long userId, String phone, String email) {
        User user = getUserWithAllRelations(userId);
        user.setPhone(phone);
        user.setEmail(email);
        user.setUpdateTime(LocalDateTime.now());
        userRepository.save(user);
        logger.info("成功更新用户基本信息，用户ID：{}", userId);
    }

    @Transactional
    public void batchUpdateUserStatus(List<Long> userIds, Integer status) {
        List<User> users = userRepository.findAllById(userIds);
        if (users.size() != userIds.size()) {
            throw new RuntimeException("部分用户不存在");
        }

        users.forEach(user -> {
            user.setDelFlag(status);
            user.setUpdateTime(LocalDateTime.now());
        });

        userRepository.saveAll(users);
    }

    /**
     * 更新用户状态
     * @param userId 用户ID
     * @param status 状态值(0:启用,1:禁用)
     */
    @Transactional
    public void updateUserStatus(Long userId, Integer status) {
        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        user.setDelFlag(status);
        user.setUpdateTime(LocalDateTime.now());
        userRepository.save(user);
    }

    // ==================== 查询操作 ====================

    /**
     * 分页条件查询用户
     */
    @Transactional(readOnly = true)
    public Page<UserDto> searchUsers(String keyword, Integer delFlag, LocalDateTime startTime, LocalDateTime endTime, Long roleId, Pageable pageable) {
        Specification<User> spec = UserSpecification.searchUsers(keyword, delFlag, startTime, endTime, roleId);
        Page<User> userPage = userRepository.findAll(spec, pageable);

        return userPage.map(user -> {
            UserDto dto = new UserDto(user);
            dto.setAppCount(userRepository.countAppsByUserId(user.getUserId()).intValue());
            dto.setModuleCount(userRepository.countModulesByUserId(user.getUserId()).intValue());

            // 新增：获取用户角色名称列表
            List<String> roleNames = user.getRoles().stream()
                    .map(Role::getRoleName)
                    .collect(Collectors.toList());
            dto.setRoleNames(roleNames);
            return dto;
        });
    }

    /**
     * 获取用户详细信息
     */
    @Transactional(readOnly = true)
    public UserInfoDto getUserDetail(Long userId) {
        try {
            return new UserInfoDto(getUserWithAllRelations(userId));
        } catch (Exception e) {
            logger.error("获取用户详情失败，用户ID：{}", userId, e);
            throw new ResourceNotFoundException ("获取用户信息失败");
        }
    }

    /**
     * 获取用户应用角色信息
     */
    @Transactional(readOnly = true)
    public UserInfoEditDto getUserAppRoles(Long userId) {
        try {
            return new UserInfoEditDto(getUserWithAllRelations(userId));
        } catch (Exception e) {
            logger.error("获取用户详情失败，用户ID：{}", userId, e);
            throw new ResourceNotFoundException ("获取用户应用角色信息失败");
        }
    }
//=================================权限管理模块调用API=========================================

    /**
     * 查询应用下所有用户
     */
    @Transactional(readOnly = true)
    public List<UserAppDTO> getUsersByAppId(Long appId) {
        logger.info("查询应用 {} 下的所有用户", appId);
        try {
            return userRepository.findUsersByAppId(appId);
        } catch (Exception e) {
            logger.error("查询应用下的用户失败，应用ID：{}", appId, e);
            throw new ResourceNotFoundException("查询应用下的用户失败");
        }
    }

    // ==================== 角色分配操作 ====================

    /**
     * 为用户分配应用角色
     */
    @Transactional
    public void assignRolesToUser(Long userId, Long appId, List<Long> roleIds) {
        User user = getUserWithAllRelations(userId);

        App app = user.getApps().stream()
                .filter(a -> a.getAppId().equals(appId) && a.getDelFlag() == 0)
                .findFirst()
                .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + appId + " 的应用"));

        Set<Role> roles = app.getRoles().stream()
                .filter(r -> roleIds.contains(r.getRoleId()) && r.getDelFlag() == 0)
                .collect(Collectors.toSet());

        if (roles.size() != roleIds.size()) {
            throw new ResourceNotFoundException("部分角色不存在或无效");
        }

        user.getRoles().addAll(roles);
        userRepository.save(user);
        logger.info("成功为用户 {} 分配应用 {} 中的角色：{}", userId, appId, roleIds);
    }

    /**
     * 批量移除用户的应用角色
     */
    @Transactional
    public void removeRolesFromUser(Long userId, Long appId, List<Long> roleIds) {
        User user = getUserWithAllRelations(userId);

        App app = user.getApps().stream()
                .filter(a -> a.getAppId().equals(appId) && a.getDelFlag() == 0)
                .findFirst()
                .orElseThrow(() -> new ResourceNotFoundException("未找到ID为 " + appId + " 的应用"));

        Set<Role> roles = app.getRoles().stream()
                .filter(r -> roleIds.contains(r.getRoleId()) && r.getDelFlag() == 0)
                .collect(Collectors.toSet());

        if (roles.size() != roleIds.size()) {
            throw new ResourceNotFoundException("部分角色不存在或无效");
        }

        user.getRoles().removeAll(roles);
        userRepository.save(user);
        logger.info("成功移除用户 {} 在应用 {} 中的角色：{}", userId, appId, roleIds);
    }

    // ==================== 公共方法 ====================
    private User getUserWithAllRelations(Long userId) {
        long start = System.currentTimeMillis();
        User user = userRepository.findByIdWithAllRelations(userId)
                .orElseThrow(() -> {
                    logger.error("查询用户失败，用户ID：{}", userId);
                    return new ResourceNotFoundException("未找到ID为 " + userId + " 的用户");
                });
        logger.debug("加载用户完整信息耗时：{}ms", System.currentTimeMillis() - start);
        return user;
    }

    // ==================== 文件导入部分 ====================
    @Transactional
    public Map<String, Object> importUsersFromFile(MultipartFile file, String operator) throws IOException {
        // 1. 解析Excel文件
        List<UserImportDto> importData = parseExcelFile(file);

        // 2. 创建导入记录
        ImportRecord record = createImportRecord(file, importData.size(), operator);
        record = importRecordRepository.save(record);

        // 3. 批量处理导入数据
        List<ImportDetail> details = new ArrayList<>(importData.size());
        int[] counts = processImportDataBatch(importData, record, details);

        // 4. 批量保存详情和更新记录
        importDetailRepository.saveAll(details);
        updateImportRecord(record, counts[0], counts[1], Collections.emptyList());

        return buildResult(importData.size(), counts[0], counts[1], record.getId());
    }

    /**
     * 批量处理导入数据
     * @param importData 导入数据列表
     * @param record 导入记录
     * @param details 导入详情列表
     * @return 数组[成功数, 失败数]
     */
    private int[] processImportDataBatch(List<UserImportDto> importData, ImportRecord record, List<ImportDetail> details) {
        int successCount = 0;
        int failedCount = 0;

        for (int i = 0; i < importData.size(); i++) {
            UserImportDto dto = importData.get(i);
            ImportDetail detail = createImportDetail(record, i + 2); // 行号从2开始

            try {
                processUserImport(dto, detail);
                successCount++;
                detail.setIsSuccess(true);
            } catch (Exception e) {
                failedCount++;
                handleImportError(detail, e, i + 2);
            }

            details.add(detail);
        }

        return new int[]{successCount, failedCount};
    }

    /**
     * 创建导入详情记录
     */
    private ImportDetail createImportDetail(ImportRecord record, int rowNumber) {
        ImportDetail detail = new ImportDetail();
        detail.setRecordKey("QXGL_FILE_USER");
        detail.setImportRecord(record);
        detail.setProcessTime(LocalDateTime.now());
        return detail;
    }

    /**
     * 处理导入错误
     */
    private void handleImportError(ImportDetail detail, Exception e, int rowNumber) {
        String errorMsg = "第" + rowNumber + "行数据错误: " + e.getMessage();
        detail.setErrorMessage(errorMsg);
        detail.setIsSuccess(false);
        logger.error("用户导入失败: {}", errorMsg, e);
    }

    /**
     * 创建导入记录
     */
    private ImportRecord createImportRecord(MultipartFile file, int totalCount, String operator) {
        return new ImportRecord(
                file.getOriginalFilename(),
                "USER",
                totalCount,
                0,
                0,
                ImportRecord.ImportMode.FULL,
                operator,
                LocalDateTime.now(),
                LocalDateTime.now(),
                ImportRecord.ImportStatus.PROCESSING,
                "无错误信息",
                "BUSINESS_SYSTEM"
        );
    }

    @Transactional
    protected void processUserImport(UserImportDto dto, ImportDetail detail) {
        try {
            // 1. 校验基本字段
            validateRequiredFields(dto);
            validateEmailAndPhone(dto);

            // 2. 检查应用是否存在
            validateAppExists(dto.getAppCode());

            // 3. 处理部门信息
            Department department = processDepartmentInfo(dto);

            // 4. 处理用户信息(创建或更新)
            User user = processUserInfo(dto, detail);

            // 5. 处理部门关联关系
            if (department != null) {
                processDepartmentAssociation(user, department);
            }

            // 6. 处理应用关联关系
            processAppAssociation(user, dto.getAppCode());

            // 7. 处理角色关联关系
            if (!CollectionUtils.isEmpty(dto.getRoleCodes())) {
                processRoleAssociation(user, dto.getRoleCodes());
            }
        } catch (Exception e) {
            detail.setErrorMessage(e.getMessage());
            throw e;
        }
    }

    /**
     * 校验必填字段
     */
    private void validateRequiredFields(UserImportDto dto) {
        if (StringUtils.isEmpty(dto.getName())) {
            throw new RuntimeException("姓名不能为空");
        }
        if (StringUtils.isEmpty(dto.getKeyId())) {
            throw new RuntimeException("key号不能为空");
        }
        if (StringUtils.isEmpty(dto.getPassword())) {
            throw new RuntimeException("密码不能为空");
        }
        if (StringUtils.isEmpty(dto.getUserCode())) {
            throw new RuntimeException("用户编码不能为空");
        }
        if (StringUtils.isEmpty(dto.getAppCode())) {
            throw new RuntimeException("应用编码不能为空");
        }
        if (dto.getSex() == null) {
            throw new RuntimeException("性别不能为空");
        }
        if (StringUtils.isEmpty(dto.getDepartmentName())) {
            throw new RuntimeException("部门名称不能为空");
        }
        if (StringUtils.isEmpty(dto.getDepartmentCode())) {
            throw new RuntimeException("部门编码不能为空");
        }
    }

    /**
     * 校验邮箱和电话格式
     */
    private void validateEmailAndPhone(UserImportDto dto) {
        // 可根据需要实现具体校验逻辑
    }

    /**
     * 校验应用是否存在
     */
    private void validateAppExists(String appCode) {
        if (StringUtils.isNotEmpty(appCode)) {
            appRepository.findByAppName(appCode)
                    .orElseThrow(() -> new RuntimeException("应用编码不存在: " + appCode));
        }
    }

    /**
     * 处理部门信息
     */
    private Department processDepartmentInfo(UserImportDto dto) {
        if (StringUtils.isEmpty(dto.getDepartmentCode())) {
            return null;
        }

        return departmentRepository.findByDepartmentCode(dto.getDepartmentCode())
                .map(department -> {
                    // 更新现有部门信息
                    department.setDepartmentName(dto.getDepartmentName());
                    department.setUpdateTime(LocalDateTime.now());
                    return departmentRepository.save(department);
                })
                .orElseGet(() -> {
                    // 创建新部门
                    Department department = new Department();
                    department.setDepartmentCode(dto.getDepartmentCode());
                    department.setDepartmentName(dto.getDepartmentName());
                    department.setDelFlag(0);
                    department.setCreatTime(LocalDateTime.now());
                    department.setUpdateTime(LocalDateTime.now());
                    // 设置默认值
                    department.setDepartmentHead("默认负责人");
                    department.setDepartmentDescription("默认部门描述");
                    department.setAddress("默认地址");
                    department.setLevel(1);
                    department.setParentId(null);
                    department.setOrderNum(1);
                    department.setPath("0");
                    department.setHasChildren(false);
                    return departmentRepository.save(department);
                });
    }

    /**
     * 处理用户信息(创建或更新)
     */
    private User processUserInfo(UserImportDto dto, ImportDetail detail) {
        Optional<User> existingUserOpt = userRepository.findByUserCode(dto.getUserCode());

        if (existingUserOpt.isPresent()) {
            detail.setOperationType(ImportDetail.OperationType.UPDATE);
            return updateExistingUser(existingUserOpt.get(), dto);
        } else {
            detail.setOperationType(ImportDetail.OperationType.CREATE);
            return createNewUser(dto);
        }
    }

    /**
     * 更新现有用户
     */
    private User updateExistingUser(User user, UserImportDto dto) {
        user.setName(dto.getName());
        if (dto.getEmail() != null) user.setEmail(dto.getEmail());
        if (dto.getPhone() != null) user.setPhone(dto.getPhone());
        if (dto.getSex() != null) user.setSex(dto.getSex());
        user.setDelFlag(dto.getDelFlag());
        user.setUpdateTime(LocalDateTime.now());
        user.setUpdateUser("SYSTEM_IMPORT");
        user.setPassword(dto.getPassword());
        return userRepository.save(user);
    }

    /**
     * 创建新用户
     */
    private User createNewUser(UserImportDto dto) {
        User user = new User();
        user.setKeyId(dto.getKeyId());
        user.setName(dto.getName());
        user.setPassword(dto.getPassword());
        user.setEmail(dto.getEmail());
        user.setPhone(dto.getPhone());
        user.setSex(dto.getSex());
        user.setDelFlag(dto.getDelFlag());
        user.setCreatUser("SYSTEM_IMPORT");
        user.setCreatTime(LocalDateTime.now());
        user.setUpdateUser("SYSTEM_IMPORT");
        user.setUpdateTime(LocalDateTime.now());
        user.setSourceSystem("BUSINESS_SYSTEM");
        user.setResourceCode(dto.getAppCode());
        user.setUserCode(dto.getUserCode());
        user.setAuthUserCode(UUID.randomUUID().toString());
        return userRepository.save(user);
    }

    /**
     * 处理部门关联关系
     */
    private void processDepartmentAssociation(User user, Department department) {
        boolean relationExists = user.getDepartments().stream()
                .anyMatch(d -> d.getDepartmentId().equals(department.getDepartmentId()));

        if (!relationExists) {
            user.getDepartments().add(department);
            department.getUsers().add(user);
            userRepository.save(user);
        }
    }

    /**
     * 处理应用关联关系（暂时）
     */
    private void processAppAssociation(User user, String appCode) {
        App app = appRepository.findByAppName(appCode)
                .orElseThrow(() -> new RuntimeException("应用编码不存在: " + appCode));

        boolean relationExists = user.getApps().stream()
                .anyMatch(a -> a.getAppId().equals(app.getAppId()));

        if (!relationExists) {
            user.getApps().add(app);
            app.getUsers().add(user);
            userRepository.save(user);
        }
    }

    /**
     * 处理角色关联关系
     */
    private void processRoleAssociation(User user, List<String> roleCodes) {
        // 批量查找所有角色
        List<Role> roles = roleRepository.findByRoleCodeIn(roleCodes);

        // 检查是否存在不匹配的角色编码
        if (roles.size() != roleCodes.size()) {
            Set<String> existingCodes = roles.stream()
                    .map(Role::getRoleCode)
                    .collect(Collectors.toSet());

            List<String> missingCodes = roleCodes.stream()
                    .filter(code -> !existingCodes.contains(code))
                    .collect(Collectors.toList());

            throw new RuntimeException("以下角色编码不存在: " + String.join(", ", missingCodes));
        }

        // 批量添加新角色关联
        Set<Role> currentRoles = user.getRoles();
        for (Role role : roles) {
            if (!currentRoles.contains(role)) {
                user.getRoles().add(role);
                role.getUsers().add(user);
            }
        }

        userRepository.save(user);
    }

    // ==================== 接口导入部分 ====================
    @Transactional
    public void syncUserInfo(String resourceCode, List<Map<String, String>> userList) {
        // 1. 创建导入记录
        ImportRecord record = createSyncImportRecord(userList.size());
        record = importRecordRepository.save(record);

        // 2. 批量处理同步数据
        List<ImportDetail> details = new ArrayList<>(userList.size());
        int[] counts = processSyncDataBatch(resourceCode, userList, record, details);

        // 3. 批量保存详情和更新记录
        importDetailRepository.saveAll(details);
        updateImportRecord(record, counts[0], counts[1], Collections.emptyList());

        if (counts[1] > 0) {
            throw new RuntimeException("用户同步完成，但有" + counts[1] + "条数据失败");
        }
    }

    /**
     * 创建同步导入记录
     */
    private ImportRecord createSyncImportRecord(int totalCount) {
        return new ImportRecord(
                "SYNC_FROM_API",
                "USER",
                totalCount,
                0,
                0,
                ImportRecord.ImportMode.FULL,
                "SYSTEM_SYNC",
                LocalDateTime.now(),
                LocalDateTime.now(),
                ImportRecord.ImportStatus.PROCESSING,
                "无错误信息",
                "BUSINESS_SYSTEM"
        );
    }

    /**
     * 批量处理同步数据
     */
    private int[] processSyncDataBatch(String resourceCode, List<Map<String, String>> userList,
                                       ImportRecord record, List<ImportDetail> details) {
        int successCount = 0;
        int failedCount = 0;

        for (int i = 0; i < userList.size(); i++) {
            Map<String, String> userData = userList.get(i);
            ImportDetail detail = createSyncImportDetail(record, i + 1);

            try {
                UserImportDto dto = convertMapToDto(userData, resourceCode);
                processUserImport(dto, detail);
                successCount++;
                detail.setIsSuccess(true);
            } catch (Exception e) {
                failedCount++;
                handleSyncError(detail, e, i + 1);
            }

            details.add(detail);
        }

        return new int[]{successCount, failedCount};
    }

    /**
     * 创建同步导入详情
     */
    private ImportDetail createSyncImportDetail(ImportRecord record, int index) {
        ImportDetail detail = new ImportDetail();
        detail.setRecordKey("SYNC_API_USER");
        detail.setImportRecord(record);
        detail.setProcessTime(LocalDateTime.now());
        return detail;
    }

    /**
     * 处理同步错误
     */
    private void handleSyncError(ImportDetail detail, Exception e, int index) {
        String errorMsg = "第" + index + "条数据错误: " + e.getMessage();
        detail.setErrorMessage(errorMsg);
        detail.setIsSuccess(false);
        logger.error("用户同步失败: {}", errorMsg, e);
    }

    /**
     * 将Map转换为UserImportDto
     */
    private UserImportDto convertMapToDto(Map<String, String> userData, String resourceCode) {
        UserImportDto dto = new UserImportDto();
        dto.setName(userData.get("姓名"));
        dto.setKeyId(userData.get("key号"));
        dto.setPassword(userData.get("密码"));
        dto.setUserCode(userData.get("用户编码"));
        dto.setEmail(userData.get("邮箱"));
        dto.setPhone(userData.get("电话号"));
        dto.setDepartmentName(userData.get("部门名称"));
        dto.setDepartmentCode(userData.get("部门编码"));

        // 修改性别和状态的处理方式
        Object sexObj = userData.get("性别(0:女,1:男)");
        dto.setSex(sexObj instanceof Integer ? (Integer) sexObj :
                sexObj != null ? Integer.parseInt(sexObj.toString()) : 1);

        Object statusObj = userData.get("用户状态(0:启用,1:禁用)");
        dto.setDelFlag(statusObj instanceof Integer ? (Integer) statusObj :
                statusObj != null ? Integer.parseInt(statusObj.toString()) : 0);

        dto.setAppCode(resourceCode);

        // 处理角色编码
        String roleCodesStr = userData.get("角色编码(多个用逗号分隔)");
        if (StringUtils.isNotEmpty(roleCodesStr)) {
            dto.setRoleCodes(Arrays.stream(roleCodesStr.split("[,，]"))
                    .map(String::trim)
                    .filter(s -> !s.isEmpty())
                    .collect(Collectors.toList()));
        } else {
            dto.setRoleCodes(new ArrayList<>());
        }

        return dto;
    }

// ==================== 公共工具方法 ====================
    /**
     * 解析Excel文件
     */
    private List<UserImportDto> parseExcelFile(MultipartFile file) throws IOException {
        List<UserImportDto> dataList = new ArrayList<>();

        try (Workbook workbook = WorkbookFactory.create(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);

            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;

                UserImportDto dto = new UserImportDto();
                dto.setName(getCellStringValue(row.getCell(0)));
                dto.setKeyId(getCellStringValue(row.getCell(1)));
                dto.setPassword(getCellStringValue(row.getCell(2)));
                dto.setUserCode(getCellStringValue(row.getCell(3)));
                dto.setEmail(getCellStringValue(row.getCell(4)));
                dto.setPhone(getCellStringValue(row.getCell(5)));
                dto.setDepartmentName(getCellStringValue(row.getCell(6)));
                dto.setDepartmentCode(getCellStringValue(row.getCell(7)));

                // 性别(0女/1男)
                Cell sexCell = row.getCell(8);
                dto.setSex(sexCell != null && sexCell.getCellType() == CellType.NUMERIC ?
                        (int) sexCell.getNumericCellValue() : null);

                // 状态(0启用/1禁用)
                Cell statusCell = row.getCell(9);
                dto.setDelFlag(statusCell != null && statusCell.getCellType() == CellType.NUMERIC ?
                        (int) statusCell.getNumericCellValue() : 0);

                // 应用编码
                dto.setAppCode(getCellStringValue(row.getCell(10)));

                // 解析角色编码列(第11列)
                String roleCodesStr = getCellStringValue(row.getCell(11));
                if (StringUtils.isNotEmpty(roleCodesStr)) {
                    dto.setRoleCodes(Arrays.stream(roleCodesStr.split("[,，]"))
                            .map(String::trim)
                            .filter(s -> !s.isEmpty())
                            .collect(Collectors.toList()));
                } else {
                    dto.setRoleCodes(new ArrayList<>());
                }

                dataList.add(dto);
            }
        }

        return dataList;
    }

    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) return null;

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                return String.valueOf((int) cell.getNumericCellValue()).trim();
            default:
                return null;
        }
    }

    /**
     * 更新导入记录
     */
    private void updateImportRecord(ImportRecord record, int successCount, int failedCount, List<String> errorMessages) {
        record.setSuccessCount(successCount);
        record.setFailedCount(failedCount);
        record.setEndTime(LocalDateTime.now());
        record.setStatus(failedCount == 0 ? ImportRecord.ImportStatus.SUCCESS :
                successCount == 0 ? ImportRecord.ImportStatus.FAILED : ImportRecord.ImportStatus.PARTIAL);

        // 记录前5条错误信息
        if (!errorMessages.isEmpty()) {
            record.setErrorSummary(errorMessages.stream()
                    .limit(5)
                    .collect(Collectors.joining("; ")));
        }

        importRecordRepository.save(record);
    }

    /**
     * 构建返回结果
     */
    private Map<String, Object> buildResult(int totalCount, int successCount, int failedCount, Long recordId) {
        Map<String, Object> result = new HashMap<>();
        result.put("totalCount", totalCount);
        result.put("importedCount", successCount);
        result.put("failedCount", failedCount);
        result.put("recordId", recordId);
        return result;
    }

}