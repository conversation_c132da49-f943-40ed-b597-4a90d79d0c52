package cn.ac.iie.pmserver.specification;

import cn.ac.iie.pmserver.model.Department;
import cn.ac.iie.pmserver.model.User;
import cn.ac.iie.pmserver.model.Role;
import org.springframework.data.jpa.domain.Specification;
import jakarta.persistence.criteria.Predicate;
import jakarta.persistence.criteria.Join;
import jakarta.persistence.criteria.JoinType;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class UserSpecification {

    public static Specification<User> searchUsers(String keyword, Integer delFlag,
                                                  LocalDateTime startTime, LocalDateTime endTime, Long roleId) {
        return (root, query, cb) -> {

            // 添加 distinct 去重
            query.distinct(true);

            List<Predicate> predicates = new ArrayList<>();

            // 关键字搜索（用户名、邮箱、电话、角色名称）
            if (keyword != null && !keyword.trim().isEmpty()) {
                String likePattern = "%" + keyword.trim() + "%";

                // 创建与角色的连接
                Join<User, Role> roleJoin = root.join("roles", JoinType.LEFT);
                Join<User, Department> departmentJoin = root.join("departments", JoinType.LEFT);

                predicates.add(cb.or(
                        cb.like(root.get("name"), likePattern),
                        cb.like(root.get("phone"), likePattern),
                        cb.like(roleJoin.get("roleName"), likePattern),  // 通过连接查询角色名称
                        cb.like(departmentJoin.get("departmentName"), likePattern)  // 通过连接查询部门名称
                ));
            }

            // 禁用标志
            if (delFlag != null) {
                predicates.add(cb.equal(root.get("delFlag"), delFlag));
            }

            // 时间范围
            if (startTime != null) {
                predicates.add(cb.greaterThanOrEqualTo(root.get("creatTime"), startTime));
            }
            if (endTime != null) {
                predicates.add(cb.lessThanOrEqualTo(root.get("creatTime"), endTime));
            }

            // 新增角色ID过滤
            if (roleId != null) {
                Join<User, Role> roleJoin = root.join("roles", JoinType.INNER);
                predicates.add(cb.equal(roleJoin.get("roleId"), roleId));
            }

            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }
}