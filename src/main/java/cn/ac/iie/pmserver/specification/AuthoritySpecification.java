package cn.ac.iie.pmserver.specification;

import cn.ac.iie.pmserver.model.Authority;
import cn.ac.iie.pmserver.model.Module;
import cn.ac.iie.pmserver.model.App;
import jakarta.persistence.criteria.*;
import org.springframework.data.jpa.domain.Specification;

import java.util.ArrayList;
import java.util.List;

/**
 * 权限查询规格构建器
 * 提供简化的权限查询条件构建
 */
public class AuthoritySpecification {

    /**
     * 构建简单的权限查询规格
     */
    public static Specification<Authority> buildSimpleSpecification(Long appId, Long moduleId, String keyword, 
                                                                   String resourceType, String syncSource) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 应用ID条件
            if (appId != null) {
                Join<Authority, Module> moduleJoin = root.join("modules", JoinType.INNER);
                Join<Module, App> appJoin = moduleJoin.join("apps", JoinType.INNER);
                predicates.add(criteriaBuilder.equal(appJoin.get("appId"), appId));
            }

            // 模块ID条件
            if (moduleId != null) {
                Join<Authority, Module> moduleJoin = root.join("modules", JoinType.INNER);
                predicates.add(criteriaBuilder.equal(moduleJoin.get("moduleId"), moduleId));
            }

            // 关键字条件（权限名称或编码）
            if (keyword != null && !keyword.trim().isEmpty()) {
                String likePattern = "%" + keyword.trim() + "%";
                Predicate namePredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("authorityName")), 
                    likePattern.toLowerCase()
                );
                Predicate codePredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("authorityCode")), 
                    likePattern.toLowerCase()
                );
                predicates.add(criteriaBuilder.or(namePredicate, codePredicate));
            }

            // 资源类型条件
            if (resourceType != null && !resourceType.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("resourceType"), resourceType));
            }

            // 同步来源条件
            if (syncSource != null && !syncSource.trim().isEmpty()) {
                predicates.add(criteriaBuilder.equal(root.get("syncSource"), syncSource));
            }

            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }
}
