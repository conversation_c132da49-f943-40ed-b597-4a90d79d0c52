package cn.ac.iie.pmserver.specification;

import cn.ac.iie.pmserver.model.Department;
import jakarta.persistence.criteria.Predicate;
import org.springframework.data.jpa.domain.Specification;

import java.util.ArrayList;
import java.util.List;

public class DepartmentSpecification {
    public static Specification<Department> searchDepartments(String keyword) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 关键字搜索（部门名称、负责人、描述）
            if (keyword != null && !keyword.trim().isEmpty()) {
                String likePattern = "%" + keyword.trim() + "%";
                predicates.add(cb.or(
                        cb.like(root.get("departmentName"), likePattern),
                        cb.like(root.get("departmentHead"), likePattern),
                        cb.like(root.get("departmentDescription"), likePattern)
                ));
            }

            // 只查询未删除的部门
            predicates.add(cb.equal(root.get("delFlag"), 0));

            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }
}