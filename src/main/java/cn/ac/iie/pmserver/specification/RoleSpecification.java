package cn.ac.iie.pmserver.specification;

import cn.ac.iie.pmserver.model.App;
import cn.ac.iie.pmserver.model.Role;
import jakarta.persistence.criteria.*;
import org.springframework.data.jpa.domain.Specification;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public class RoleSpecification {
    public static Specification<Role> searchRoles(String keyword, Integer delFlag, Long appId) {
        return (root, query, cb) -> {
            List<Predicate> predicates = new ArrayList<>();

            // 关键字搜索
            if (keyword != null && !keyword.trim().isEmpty()) {
                predicates.add(cb.or(
                        cb.like(root.get("roleName"), "%" + keyword + "%"),
                        cb.like(root.get("roleDescription"), "%" + keyword + "%")
                ));
            }

            // 状态过滤
            if (delFlag != null) {
                predicates.add(cb.equal(root.get("delFlag"), delFlag));
            }

            // 应用ID过滤
            if (appId != null) {
                Join<Role, App> appJoin = root.join("apps", JoinType.INNER);
                predicates.add(cb.equal(appJoin.get("appId"), appId));
            }
            return cb.and(predicates.toArray(new Predicate[0]));
        };
    }
}