package cn.ac.iie.pmserver.utils;

import cn.ac.iie.pmserver.dto.policies.PolicyEvaluationDto;
import cn.ac.iie.pmserver.model.User;
import cn.ac.iie.pmserver.model.Role;
import org.slf4j.Logger;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 策略管理工具类
 * 提供策略相关的通用工具方法
 */
public class PolicyUtils {
    
    private static final Logger logger = LogUtils.get(PolicyUtils.class);
    
    /**
     * 检查角色条件匹配（RBAC模型）
     * @param requiredRoles 所需角色列表
     * @param user 用户
     * @return 是否匹配
     */
    public static boolean matchesRoleConditions(List<String> requiredRoles, User user) {
        if (requiredRoles == null || requiredRoles.isEmpty()) {
            return true;
        }
        
        if (user.getRoles() == null || user.getRoles().isEmpty()) {
            return false;
        }
        
        Set<String> userRoles = user.getRoles().stream()
            .map(Role::getRoleName)
            .collect(Collectors.toSet());
        
        // 检查用户是否拥有任一所需角色
        return requiredRoles.stream().anyMatch(userRoles::contains);
    }
    
    /**
     * 检查用户属性匹配（ABAC模型 - 主体属性）
     * @param requiredAttributes 所需属性
     * @param user 用户
     * @return 是否匹配
     */
    public static boolean matchesUserAttributes(Map<String, Object> requiredAttributes, User user) {
        if (requiredAttributes == null || requiredAttributes.isEmpty()) {
            return true;
        }
        
        // 检查部门属性
        if (requiredAttributes.containsKey("department")) {
            String requiredDept = (String) requiredAttributes.get("department");
            if (user.getDepartments() == null || user.getDepartments().isEmpty()) {
                return false;
            }
            
            boolean deptMatches = user.getDepartments().stream()
                .anyMatch(dept -> dept.getDepartmentName().equals(requiredDept));
            if (!deptMatches) {
                return false;
            }
        }
        
        // 检查用户级别属性
        if (requiredAttributes.containsKey("level")) {
            String requiredLevel = (String) requiredAttributes.get("level");
            // 这里需要根据实际的用户级别字段进行匹配
            // 假设用户有level字段或通过其他方式获取
            // if (!requiredLevel.equals(user.getLevel())) return false;
        }
        
        // 检查其他用户属性
        if (requiredAttributes.containsKey("position")) {
            String requiredPosition = (String) requiredAttributes.get("position");
            // 检查用户职位
            // if (!requiredPosition.equals(user.getPosition())) return false;
        }
        
        return true;
    }
    
    /**
     * 检查资源属性匹配（ABAC模型 - 客体属性）
     * @param requiredAttributes 所需属性
     * @param resourcePath 资源路径
     * @return 是否匹配
     */
    public static boolean matchesResourceAttributes(Map<String, Object> requiredAttributes, String resourcePath) {
        if (requiredAttributes == null || requiredAttributes.isEmpty()) {
            return true;
        }
        
        // 检查资源分类属性
        if (requiredAttributes.containsKey("classification")) {
            String requiredClassification = (String) requiredAttributes.get("classification");
            String resourceClassification = determineResourceClassification(resourcePath);
            if (!requiredClassification.equals(resourceClassification)) {
                return false;
            }
        }
        
        // 检查资源所有者属性
        if (requiredAttributes.containsKey("owner")) {
            String requiredOwner = (String) requiredAttributes.get("owner");
            String resourceOwner = getResourceOwner(resourcePath);
            if (!requiredOwner.equals(resourceOwner)) {
                return false;
            }
        }
        
        // 检查资源敏感级别
        if (requiredAttributes.containsKey("sensitivityLevel")) {
            String requiredLevel = (String) requiredAttributes.get("sensitivityLevel");
            String resourceLevel = getResourceSensitivityLevel(resourcePath);
            if (!requiredLevel.equals(resourceLevel)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 检查环境属性匹配（ABAC模型 - 环境属性）
     * @param requiredAttributes 所需属性
     * @param context 上下文信息
     * @return 是否匹配
     */
    public static boolean matchesEnvironmentAttributes(Map<String, Object> requiredAttributes, Map<String, Object> context) {
        if (requiredAttributes == null || requiredAttributes.isEmpty()) {
            return true;
        }
        
        if (context == null) {
            context = new HashMap<>();
        }
        
        // 检查时间条件
        if (requiredAttributes.containsKey("time")) {
            @SuppressWarnings("unchecked")
            Map<String, String> timeCondition = (Map<String, String>) requiredAttributes.get("time");
            if (!matchesTimeRange(timeCondition)) {
                return false;
            }
        }
        
        // 检查位置条件
        if (requiredAttributes.containsKey("location")) {
            String requiredLocation = (String) requiredAttributes.get("location");
            String userLocation = (String) context.get("location");
            if (!requiredLocation.equals(userLocation)) {
                return false;
            }
        }
        
        // 检查网络条件
        if (requiredAttributes.containsKey("network")) {
            String requiredNetwork = (String) requiredAttributes.get("network");
            String userNetwork = (String) context.get("network");
            if (!requiredNetwork.equals(userNetwork)) {
                return false;
            }
        }
        
        // 检查设备类型
        if (requiredAttributes.containsKey("deviceType")) {
            String requiredDeviceType = (String) requiredAttributes.get("deviceType");
            String userDeviceType = (String) context.get("deviceType");
            if (!requiredDeviceType.equals(userDeviceType)) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 判断时间范围是否匹配
     * @param timeRange 时间范围
     * @return 是否匹配
     */
    public static boolean matchesTimeRange(Map<String, String> timeRange) {
        if (timeRange == null) {
            return true;
        }
        
        String start = timeRange.get("start");
        String end = timeRange.get("end");
        
        if (start == null || end == null) {
            return true;
        }
        
        LocalDateTime now = LocalDateTime.now();
        int currentHour = now.getHour();
        int currentMinute = now.getMinute();
        int currentTime = currentHour * 60 + currentMinute;
        
        try {
            String[] startParts = start.split(":");
            int startTime = Integer.parseInt(startParts[0]) * 60 + Integer.parseInt(startParts[1]);
            
            String[] endParts = end.split(":");
            int endTime = Integer.parseInt(endParts[0]) * 60 + Integer.parseInt(endParts[1]);
            
            return currentTime >= startTime && currentTime <= endTime;
        } catch (Exception e) {
            logger.warn("时间范围格式错误: {}", timeRange);
            return true;
        }
    }
    
    /**
     * 判断IP是否在白名单中
     * @param ipWhitelist IP白名单
     * @param clientIp 客户端IP
     * @return 是否匹配
     */
    public static boolean matchesIpWhitelist(List<String> ipWhitelist, String clientIp) {
        if (ipWhitelist == null || ipWhitelist.isEmpty() || clientIp == null) {
            return true;
        }
        
        return ipWhitelist.stream()
            .anyMatch(allowedIp -> {
                if (allowedIp.contains("/")) {
                    // CIDR格式，简单实现
                    return clientIp.startsWith(allowedIp.split("/")[0].substring(0, 
                        allowedIp.split("/")[0].lastIndexOf(".")));
                } else {
                    return allowedIp.equals(clientIp);
                }
            });
    }
    
    /**
     * 确定资源分类
     * @param resourcePath 资源路径
     * @return 资源分类
     */
    public static String determineResourceClassification(String resourcePath) {
        if (resourcePath.contains("/users/")) {
            return "user_data";
        } else if (resourcePath.contains("/admin/")) {
            return "admin_data";
        } else if (resourcePath.contains("/reports/")) {
            return "report_data";
        }
        return "general";
    }
    
    /**
     * 获取资源所有者
     * @param resourcePath 资源路径
     * @return 资源所有者
     */
    public static String getResourceOwner(String resourcePath) {
        // 这里应该根据实际业务逻辑获取资源所有者
        // 可能需要解析资源路径或查询数据库
        return "system"; // 默认系统所有
    }
    
    /**
     * 获取资源敏感级别
     * @param resourcePath 资源路径
     * @return 敏感级别
     */
    public static String getResourceSensitivityLevel(String resourcePath) {
        if (resourcePath.contains("/admin/") || resourcePath.contains("/config/")) {
            return "high";
        } else if (resourcePath.contains("/users/") || resourcePath.contains("/roles/")) {
            return "medium";
        }
        return "low";
    }
    
    /**
     * 生成策略编码
     * @param policyName 策略名称
     * @param policyId 策略ID
     * @return 策略编码
     */
    public static String generatePolicyCode(String policyName, Long policyId) {
        if (policyName == null) {
            return "policy_" + policyId;
        }
        
        String code = policyName
            .toLowerCase()
            .replaceAll("[\\s\\u4e00-\\u9fa5]+", "_")
            .replaceAll("[^a-z0-9_]", "")
            .replaceAll("_{2,}", "_")
            .replaceAll("^_|_$", "");
            
        return code.isEmpty() ? "policy_" + policyId : code + "_policy";
    }
}
