package cn.ac.iie.pmserver.utils;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 日期时间工具类
 * 提供常用的日期时间格式化和解析功能
 */
public class DateUtils {
    // 默认的日期时间格式化模式
    private static final DateTimeFormatter DEFAULT_FORMATTER =
            DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    /**
     * 获取当前时间的格式化字符串
     * @return 返回当前时间的格式化字符串，格式为"yyyy-MM-dd HH:mm:ss"
     */
    public static String getCurrentTime() {
        return LocalDateTime.now().format(DEFAULT_FORMATTER);
    }

    /**
     * 将符合格式的日期时间字符串解析为LocalDateTime对象
     * @param dateStr 日期时间字符串，必须符合"yyyy-MM-dd HH:mm:ss"格式
     * @return 解析后的LocalDateTime对象
     * @throws java.time.format.DateTimeParseException 如果字符串格式不匹配会抛出此异常
     */
    public static LocalDateTime parse(String dateStr) {
        return LocalDateTime.parse(dateStr, DEFAULT_FORMATTER);
    }
}