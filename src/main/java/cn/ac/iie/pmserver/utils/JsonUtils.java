package cn.ac.iie.pmserver.utils;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

/**
 * JSON 数据处理工具类
 * 提供 Java 对象与 JSON 字符串之间的互相转换能力
 *
 * <p>使用 Jackson 库作为底层实现，线程安全，适合多线程环境使用</p>
 *
 * <p>典型用法：
 * <pre>{@code
 * // 对象转JSON
 * String json = JsonUtils.toJson(user);
 *
 * // JSON转对象
 * User user = JsonUtils.fromJson(jsonStr, User.class);
 * }</pre>
 * </p>
 */
public class JsonUtils {

    /**
     * Jackson 核心对象映射器
     * <p>注意：ObjectMapper 是线程安全的，可以全局共享</p>
     */
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 将 Java 对象序列化为 JSON 字符串
     *
     * @param obj 要序列化的 Java 对象，可以是任意非空对象
     * @return 对象的 JSON 字符串表示
     * @throws RuntimeException 当序列化失败时抛出，包含原始异常信息
     *
     * @example 示例：
     * <pre>{@code
     * User user = new User("张三", 25);
     * String json = JsonUtils.toJson(user);
     * // 结果：{"name":"张三","age":25}
     * }</pre>
     */
    public static String toJson(Object obj) {
        try {
            return objectMapper.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON 序列化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 将 JSON 字符串反序列化为 Java 对象
     *
     * @param json 要反序列化的 JSON 字符串，不能为 null
     * @param clazz 目标 Java 类型的 Class 对象
     * @param <T> 目标类型的泛型参数
     * @return 反序列化后的 Java 对象
     * @throws RuntimeException 当反序列化失败时抛出，包含原始异常信息
     *
     * @example 示例：
     * <pre>{@code
     * String json = "{\"name\":\"李四\",\"age\":30}";
     * User user = JsonUtils.fromJson(json, User.class);
     * }</pre>
     */
    public static <T> T fromJson(String json, Class<T> clazz) {
        try {
            return objectMapper.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            throw new RuntimeException("JSON 反序列化失败: " + e.getMessage(), e);
        }
    }
}