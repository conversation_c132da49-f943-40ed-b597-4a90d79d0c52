variables:
  GRADLE_USER_HOME: "$CI_PROJECT_DIR/.gradle-cache"
  GRADLE_OPTS: "-Dorg.gradle.daemon=false -Dorg.gradle.caching=true -Duser.home=$CI_PROJECT_DIR"

default:
  tags:
    - pm-service
  image: docker.m.daocloud.io/eclipse-temurin:21-jdk # 默认镜像
  cache:
    key:
      files:
        - build.gradle.kts
        - settings.gradle.kts
    paths:
      - .gradle/ # 项目级别的 Gradle 构建缓存 (非发行版)
      - $GRADLE_USER_HOME/caches/       # 缓存的依赖
      - $GRADLE_USER_HOME/wrapper/dists/ # <--- 缓存 Gradle 发行版 (zip包和解压后的内容)
  before_script:
    - chmod +x ./gradlew
    - echo "CI_PIPELINE_SOURCE=$CI_PIPELINE_SOURCE"
    - echo "CI_COMMIT_BRANCH = $CI_COMMIT_BRANCH"
    - echo "CI_DEFAULT_BRANCH = $CI_DEFAULT_BRANCH"

workflow:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "push" || $CI_PIPELINE_SOURCE == "merge_request_event"'

stages:
  - dependencies
  - build
  - test
  - docker
  - deploy

install_deps:
  stage: dependencies
  script:
    # - rm -rf $GRADLE_USER_HOME/wrapper/dists/* # 清理 Gradle 发行版缓存，尝试解决缺少了 ant 模块
    - ./gradlew dependencies --parallel --info
  timeout: 30 minutes

build_jar:
  stage: build
  script:
    - ./gradlew build -x test # Build the project, skip running tests
  artifacts:
    paths:
      - build/libs/*.jar
    expire_in: 7 days
  needs:
    - job: install_deps

test_unit:
  stage: test
  script:
    - ./gradlew test # Run unit tests
  artifacts:
    reports:
      junit: build/test-results/**/*.xml
    when: always
  needs:
    - job: build_jar

.docker_job_template: &docker_job_definition # YAML Anchor for common Docker job settings
  image: docker.m.daocloud.io/docker:28
  #  services:
  #    - name: docker.m.daocloud.io/docker:dind
  #      alias: docker
  #      command: [ "--insecure-registry=**********:5005" ]
  #  variables:
  #    DOCKER_HOST: tcp://docker:2375
  #    DOCKER_TLS_CERTDIR: "" # 禁用 TLS 与 DinD 通信
  before_script:
    - echo "Logging into GitLab Registry $CI_REGISTRY"
    - echo "$CI_JOB_TOKEN" | docker login -u "$CI_REGISTRY_USER" --password-stdin "$CI_REGISTRY"
    - echo "Login successful."

docker_build:
  <<: *docker_job_definition # Use the anchor
  stage: docker
  script:
    - IMAGE_TAG_COMMIT="$CI_REGISTRY_IMAGE:$CI_COMMIT_SHA"
    - IMAGE_TAG_REF="$CI_REGISTRY_IMAGE:$CI_COMMIT_REF_SLUG" # 通常用于分支名或者 tag
    - docker build -t "$IMAGE_TAG_COMMIT" -t "$IMAGE_TAG_REF" .
    - docker push "$IMAGE_TAG_COMMIT"
    - docker push "$IMAGE_TAG_REF"
  needs:
    - job: build_jar

deploy_staging:
  <<: *docker_job_definition # Use the anchor
  stage: deploy
  script:
    - STAGING_IMAGE_TO_DEPLOY="$CI_REGISTRY_IMAGE:$CI_COMMIT_SHA"
    - echo "Deploying image $STAGING_IMAGE_TO_DEPLOY to staging"
    - docker stop pm-server || true
    - docker rm pm-server || true
    - docker run -d --name pm-server --network qx_app-network -e SPRING_PROFILES_ACTIVE=docker -p 9080:8080 -v /opt/x-team/data/pm-server/images:/app/images --restart unless-stopped "$STAGING_IMAGE_TO_DEPLOY"
    - echo "Deployment to staging initiated. Application should be available at http://**********:9080/swagger-ui.html if port mapping is correct"
  needs:
    - job: docker_build
  environment:
    name: staging
    url: http://**********:9080/swagger-ui.html
